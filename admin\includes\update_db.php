<?php
require_once __DIR__ . '/../../admin/config.php';
require_once 'db.php';

function updateDatabase() {
    global $db;
    
    try {
        // 检查players表是否存在is_banned字段
        $result = $db->query("SHOW COLUMNS FROM players LIKE 'is_banned'");
        if (!$result || $result->num_rows === 0) {
            // 添加is_banned字段
            $sql = "ALTER TABLE players ADD COLUMN is_banned TINYINT(1) DEFAULT 0 AFTER last_seen";
            if ($db->query($sql)) {
                echo "成功添加is_banned字段到players表\n";
            } else {
                echo "添加is_banned字段失败\n";
            }
        } else {
            echo "is_banned字段已存在\n";
        }
        
        echo "数据库更新完成\n";
        
    } catch (Exception $e) {
        die("数据库更新失败: " . $e->getMessage());
    }
}

// 运行更新
updateDatabase(); 