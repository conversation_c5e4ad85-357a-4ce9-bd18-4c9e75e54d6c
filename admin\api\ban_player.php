<?php
require_once '../includes/utils.php';
require_once '../includes/db.php';

// 检查是否是AJAX请求
Utils::checkLoginAjax();

// 获取POST数据
$data = json_decode(file_get_contents('php://input'), true);

if (!isset($data['player_id']) || !isset($data['action'])) {
    Utils::apiResponse('error', '缺少必要参数');
}

$player_id = (int)$data['player_id'];
$action = $data['action'];
$ban_type = isset($data['ban_type']) ? $data['ban_type'] : null;

// 验证操作类型
if (!in_array($action, ['ban', 'unban'])) {
    Utils::apiResponse('error', '无效的操作类型');
}

// 封禁类型校验
$valid_ban_types = ['cheat', 'violation'];
if ($action === 'ban' && !in_array($ban_type, $valid_ban_types)) {
    Utils::apiResponse('error', '无效的封禁类型');
}

if ($action === 'ban') {
    $update_data = ['is_banned' => 1, 'ban_type' => $ban_type];
} else {
    $update_data = ['is_banned' => 0, 'ban_type' => null];
}

$result = $db->update('players', $update_data, "id = {$player_id}");

if ($result !== false) {
    // 获取玩家信息用于日志记录
    $player = $db->getRow("SELECT nickname FROM players WHERE id = {$player_id}");
    $action_text = $action === 'ban' ? '封禁' : '解除封禁';
    $ban_type_text = $action === 'ban' ? ($ban_type === 'cheat' ? '（作弊）' : '（违规）') : '';
    
    // 记录操作日志
    Utils::logActivity(
        "{$action_text}玩家{$ban_type_text}", 
        "管理员{$action_text}{$ban_type_text}了玩家 {$player['nickname']}"
    );
    
    // 返回更具体的成功消息
    $success_message = $action === 'ban' ?
        "已成功{$action_text}{$ban_type_text}玩家 {$player['nickname']}" :
        "已成功解除玩家 {$player['nickname']} 的封禁";
    
    Utils::apiResponse('success', $success_message);
} else {
    Utils::apiResponse('error', '操作失败，请稍后重试');
} 