<?php
require_once '../config.php';
require_once '../includes/db.php';
require_once '../includes/utils.php';

// 检查登录状态
Utils::checkLoginAjax();

// 设置响应头
header('Content-Type: application/json');

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'success' => false,
        'message' => '无效的请求方法'
    ]);
    exit;
}

// 检查是否有文件上传
if (!isset($_FILES['image']) || $_FILES['image']['error'] !== UPLOAD_ERR_OK) {
    $error = isset($_FILES['image']) ? getUploadErrorMessage($_FILES['image']['error']) : '没有上传文件';
    echo json_encode([
        'success' => false,
        'message' => '文件上传失败: ' . $error
    ]);
    exit;
}

$file = $_FILES['image'];
$description = isset($_POST['description']) ? $_POST['description'] : '';

// 检查文件类型
$allowed_types = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
if (!in_array($file['type'], $allowed_types)) {
    echo json_encode([
        'success' => false,
        'message' => '不支持的文件类型，请上传JPG、PNG或GIF图片'
    ]);
    exit;
}

// 检查文件大小
$max_size = 5 * 1024 * 1024; // 5MB
if ($file['size'] > $max_size) {
    echo json_encode([
        'success' => false,
        'message' => '文件过大，请上传小于5MB的图片'
    ]);
    exit;
}

// 创建上传目录
$upload_dir = '../../uploads/article_images/';
if (!file_exists($upload_dir)) {
    mkdir($upload_dir, 0755, true);
}

// 生成唯一文件名
$filename = uniqid() . '_' . Utils::sanitizeFilename($file['name']);
$upload_path = $upload_dir . $filename;

// 移动上传的文件
if (!move_uploaded_file($file['tmp_name'], $upload_path)) {
    echo json_encode([
        'success' => false,
        'message' => '文件保存失败'
    ]);
    exit;
}

// 获取文件URL路径
$image_url = SITE_URL . 'uploads/article_images/' . $filename;

// 记录上传活动
Utils::logActivity('上传图片', "上传了图片: {$filename}");

// 返回成功响应
echo json_encode([
    'success' => true,
    'message' => '图片上传成功',
    'image_url' => $image_url,
    'filename' => $filename
]);

/**
 * 获取上传错误信息
 */
function getUploadErrorMessage($error_code) {
    switch ($error_code) {
        case UPLOAD_ERR_INI_SIZE:
        case UPLOAD_ERR_FORM_SIZE:
            return '文件超过允许的大小';
        case UPLOAD_ERR_PARTIAL:
            return '文件只有部分被上传';
        case UPLOAD_ERR_NO_FILE:
            return '没有文件被上传';
        case UPLOAD_ERR_NO_TMP_DIR:
            return '缺少临时文件夹';
        case UPLOAD_ERR_CANT_WRITE:
            return '文件写入失败';
        case UPLOAD_ERR_EXTENSION:
            return 'PHP扩展阻止了文件上传';
        default:
            return '未知错误';
    }
} 