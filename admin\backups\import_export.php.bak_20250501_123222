<?php
// 设置适当的执行时间和内存限制
@ini_set('max_execution_time', 300); // 5分钟
@ini_set('memory_limit', '256M');    // 256MB内存
@set_time_limit(300);                // 防止超时

require_once 'config.php';
require_once 'includes/db.php';
require_once 'includes/utils.php';
require_once '../includes/formatting.php';
require_once '../includes/markdown.php';

// 检查用户是否已登录
Utils::checkLogin();

// 处理导出请求 - 需要在任何HTML输出前处理
if (isset($_GET['action']) && $_GET['action'] === 'export') {
    // 开始输出缓冲，防止任何输出污染二进制ZIP文件
    ob_start();
    
    $content_type = $_GET['content_type'] ?? '';
    $format = $_GET['format'] ?? '';
    
    // 确保导出目录存在
    $export_base_dir = dirname(__FILE__) . '/exports';
    if (!file_exists($export_base_dir)) {
        mkdir($export_base_dir, 0777, true);
    } else {
        // 确保目录有写入权限
        chmod($export_base_dir, 0777);
    }
    
    // 创建一个唯一的导出文件名
    $timestamp = date('Ymd_His');
    $filename_prefix = ($content_type === 'article') ? 'articles' : 'announcements';
    $zip_name = "{$filename_prefix}_{$format}_{$timestamp}.zip";
    $zip_path = $export_base_dir . '/' . $zip_name;
    
    // 获取内容数据
    if ($content_type === 'article') {
        $sql = "SELECT * FROM articles ORDER BY create_time DESC";
        $items = $db->getRows($sql);
    } else { // announcement
        $sql = "SELECT * FROM announcements ORDER BY created_at DESC";
        $items = $db->getRows($sql);
    }
    
    // 检查是否有内容可导出
    if (empty($items)) {
        // 如果没有内容，设置错误消息并重定向回页面
        $_SESSION['export_error'] = "没有可导出的{$content_type}内容";
        header("Location: import_export.php");
        exit;
    }
    
    // 创建新的ZIP对象
    $zip = new ZipArchive();
    
    // 打开ZIP文件
    if ($zip->open($zip_path, ZipArchive::CREATE | ZipArchive::OVERWRITE) !== TRUE) {
        $_SESSION['export_error'] = '无法创建ZIP文件';
        header("Location: import_export.php");
        exit;
    }
    
    // 添加单个文件和批量文件到ZIP
    $batch_content = '';
    
    foreach ($items as $index => $item) {
        $title = $item['title'];
        $content = $item['content'];
        
        // 改进文件名处理，保留中文字符
        $file_index = $index + 1;
        // 只替换Windows文件系统不允许的字符，保留中文字符
        $safe_title = str_replace(['\\', '/', ':', '*', '?', '"', '<', '>', '|'], '_', $title);
        
        // 准备内容
        if ($format === 'txt') {
            // 添加UTF-8 BOM以确保文本编辑器正确识别中文
            $file_content = "\xEF\xBB\xBF" . $title . "\n\n" . $content;
            $ext = 'txt';
            $batch_content .= $title . "\n\n" . $content . "\n\n---CONTENT_DELIMITER---\n\n";
        } 
        elseif ($format === 'markdown') {
            // 添加UTF-8 BOM
            $file_content = "\xEF\xBB\xBF# " . $title . "\n\n" . $content;
            $ext = 'md';
            $batch_content .= "# " . $title . "\n\n" . $content . "\n\n---CONTENT_DELIMITER---\n\n";
        }
        elseif ($format === 'html') {
            // 使用parse_markdown函数而不是Markdown::convert
            $html_content = parse_markdown($content);
            $file_content = "<!DOCTYPE html>\n<html>\n<head>\n";
            $file_content .= "<meta charset=\"UTF-8\">\n";
            $file_content .= "<title>" . htmlspecialchars($title, ENT_QUOTES, 'UTF-8') . "</title>\n";
            $file_content .= "<style>body { font-family: Arial, sans-serif; line-height: 1.6; padding: 20px; max-width: 800px; margin: 0 auto; }</style>\n";
            $file_content .= "</head>\n<body>\n";
            $file_content .= "<h1>" . htmlspecialchars($title, ENT_QUOTES, 'UTF-8') . "</h1>\n";
            // 由于parse_markdown只返回原始内容，我们需要将Markdown文本包装在<pre>标签中以保持格式
            $file_content .= "<div class=\"markdown-content\">" . nl2br(htmlspecialchars($content, ENT_QUOTES, 'UTF-8')) . "</div>";
            $file_content .= "\n</body>\n</html>";
            $ext = 'html';
            
            // 构建批量HTML内容
            if ($index === 0) { // 如果是第一个条目，添加HTML头
                $batch_content = "<!DOCTYPE html>\n<html>\n<head>\n";
                $batch_content .= "<meta charset=\"UTF-8\">\n";
                $batch_content .= "<title>所有" . ($content_type === 'article' ? '文章' : '公告') . "</title>\n";
                $batch_content .= "<style>body { font-family: Arial, sans-serif; line-height: 1.6; padding: 20px; max-width: 800px; margin: 0 auto; } .content-item { margin-bottom: 30px; }</style>\n";
                $batch_content .= "</head>\n<body>\n";
            }
            
            $batch_content .= "<div class=\"content-item\">\n";
            $batch_content .= "<h1>" . htmlspecialchars($title, ENT_QUOTES, 'UTF-8') . "</h1>\n";
            $batch_content .= "<div class=\"markdown-content\">" . nl2br(htmlspecialchars($content, ENT_QUOTES, 'UTF-8')) . "</div>";
            $batch_content .= "\n</div>\n<hr>\n\n";
            
            if ($index === count($items) - 1) { // 如果是最后一个条目，添加HTML尾
                $batch_content .= "\n</body>\n</html>";
            }
        }
        
        // 添加单个文件到ZIP
        $filename = "{$file_index}_{$safe_title}.{$ext}";
        $zip->addFromString($filename, $file_content);
    }
    
    // 添加批量文件到ZIP
    $batch_filename = "all_{$filename_prefix}.{$ext}";
    
    // 为批量文件添加UTF-8 BOM
    if ($format === 'txt' || $format === 'markdown') {
        $batch_content = "\xEF\xBB\xBF" . $batch_content;
    }
    
    $zip->addFromString($batch_filename, $batch_content);
    
    // 关闭ZIP文件
    $zip->close();
    
    // 记录活动
    Utils::logActivity('导出内容', "导出" . ($content_type === 'article' ? '文章' : '公告') . "为{$format}格式");
    
    // 检查文件是否创建成功
    if (!file_exists($zip_path) || filesize($zip_path) < 10) { // 文件大小至少应该有几个字节
        $_SESSION['export_error'] = '导出文件创建失败';
        header("Location: import_export.php");
        exit;
    }
    
    // 清除所有输出缓冲
    while (ob_get_level()) {
        ob_end_clean();
    }
    
    // 设置头信息并发送文件
    header("Content-Type: application/zip");
    
    // 使用RFC 5987编码处理文件名，支持中文和其他非ASCII字符
    $encoded_filename = rawurlencode($zip_name);
    header("Content-Disposition: attachment; filename*=UTF-8''{$encoded_filename}");
    
    header("Content-Length: " . filesize($zip_path));
    header("Cache-Control: no-cache, no-store, must-revalidate");
    header("Pragma: no-cache");
    header("Expires: 0");
    
    // 读取并输出文件
    readfile($zip_path);
    
    // 删除临时文件（可选，如果想保留文件，可以注释此行）
    @unlink($zip_path);
    
    exit;
}

// 创建临时目录 - 用于导入功能
$temp_dir = sys_get_temp_dir() . '/import_export_' . time();
if (!file_exists($temp_dir)) {
    mkdir($temp_dir, 0777, true);
}

// 处理导入请求
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'import') {
    $content_type = $_POST['content_type'] ?? '';
    $error = '';
    $success = '';
    
    if ($content_type !== 'article' && $content_type !== 'announcement') {
        $error = '无效的内容类型';
    } elseif (!isset($_FILES['import_file']) || $_FILES['import_file']['error'] !== UPLOAD_ERR_OK) {
        $error = '文件上传失败: ' . ($_FILES['import_file']['error'] ?? '未知错误');
    } else {
        $file = $_FILES['import_file'];
        
        // 检查文件类型
        $file_ext = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if ($file_ext !== 'txt') {
            $error = '仅支持导入TXT文件';
        } else {
            // 处理导入文件
            $imported_count = 0;
            $file_content = file_get_contents($file['tmp_name']);
            
            // 按分隔符拆分内容
            $delimiter = "---CONTENT_DELIMITER---";
            $entries = explode($delimiter, $file_content);
            
            foreach ($entries as $entry) {
                if (empty(trim($entry))) continue;
                
                // 提取标题和内容
                $lines = explode("\n", trim($entry));
                $title = trim($lines[0]);
                $content = trim(implode("\n", array_slice($lines, 1)));
                
                if (empty($title) || empty($content)) continue;
                
                $now = date('Y-m-d H:i:s');
                
                if ($content_type === 'article') {
                    // 导入文章
                    $result = $db->insert('articles', [
                        'title' => $title,
                        'content' => $content,
                        'category_id' => 1, // 默认分类
                        'published' => 0, // 默认为草稿
                        'create_time' => $now,
                        'update_time' => $now
                    ]);
                } else {
                    // 导入公告
                    $result = $db->insert('announcements', [
                        'title' => $title,
                        'content' => $content,
                        'status' => 0, // 默认为草稿
                        'priority' => 0, // 默认优先级
                        'valid_from' => date('Y-m-d'),
                        'valid_to' => date('Y-m-d', strtotime('+30 days')),
                        'created_at' => $now,
                        'updated_at' => $now
                    ]);
                }
                
                if ($result) {
                    $imported_count++;
                }
            }
            
            if ($imported_count > 0) {
                $success = "成功导入 {$imported_count} 条" . ($content_type === 'article' ? '文章' : '公告');
                Utils::logActivity('导入内容', "导入了 {$imported_count} 条" . ($content_type === 'article' ? '文章' : '公告'));
            } else {
                $error = '导入失败，未导入任何内容';
            }
        }
    }
}

// 包含头部
include 'includes/header.php';
?>

<div class="main-content">
    <div class="container">
<?php
?>

<h1 class="page-title">导入/导出内容</h1>

<?php if (isset($error) && !empty($error)): ?>
    <div class="alert alert-danger"><?php echo $error; ?></div>
<?php endif; ?>

<?php if (isset($success) && !empty($success)): ?>
    <div class="alert alert-success"><?php echo $success; ?></div>
<?php endif; ?>

<?php if (isset($_SESSION['export_error']) && !empty($_SESSION['export_error'])): ?>
    <div class="alert alert-danger"><?php echo $_SESSION['export_error']; unset($_SESSION['export_error']); ?></div>
<?php endif; ?>

<!-- 使用表格布局确保左右显示 -->
<style>
.import-export-wrapper {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -10px;
}
.import-section, .export-section {
    flex: 1;
    min-width: 300px;
    padding: 0 10px;
    box-sizing: border-box;
}
.import-section {
    border-right: 1px solid #e0e0e0;
}
@media (max-width: 768px) {
    .import-export-wrapper {
        flex-direction: column;
    }
    .import-section {
        border-right: none;
        border-bottom: 1px solid #e0e0e0;
        margin-bottom: 20px;
        padding-bottom: 20px;
    }
}
</style>

<div class="card">
    <div class="card-title">内容管理工具</div>
    <div class="card-body">
        <div class="import-export-wrapper">
            <!-- 导入功能 - 左侧 -->
            <div class="import-section">
                <h3>导入内容</h3>
                <form method="POST" action="import_export.php" enctype="multipart/form-data">
                    <input type="hidden" name="action" value="import">
                    
                    <div class="form-group mb-3">
                        <label for="content_type" class="form-label">选择内容类型</label>
                        <select id="content_type" name="content_type" class="form-control" required>
                            <option value="article">文章</option>
                            <option value="announcement">公告</option>
                        </select>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label for="import_file" class="form-label">选择导入文件</label>
                        <input type="file" id="import_file" name="import_file" class="form-control" accept=".txt" required>
                        <div class="form-text">支持的格式: TXT文本文件</div>
                    </div>
                     <button type="submit" class="btn btn-primary">
                        <i class="fas fa-file-import"></i> 开始导入
                    </button>
                    <div class="import-format-info mb-3">
                        <div class="card border-info">
                            <div class="card-header bg-info text-white">导入格式说明</div>
                            <div class="card-body">
                                <p>文件格式要求：</p>
                                <ul>
                                    <li>文件首行为标题</li>
                                    <li>从第二行开始为内容</li>
                                    <li>使用 <code>---CONTENT_DELIMITER---</code> 分隔多篇内容</li>
                                </ul>
                                <pre class="bg-light p-2"><code>第一篇标题
这是第一篇内容第一行
这是第一篇内容第二行

---CONTENT_DELIMITER---

第二篇标题
这是第二篇内容第一行
这是第二篇内容第二行</code></pre>
                            </div>
                        </div>
                    </div>
                    
                   
                </form>
            </div>
            
            <!-- 导出功能 - 右侧 -->
            <div class="export-section">
                <h3>导出内容</h3>
                <form method="GET" action="import_export.php">
                    <input type="hidden" name="action" value="export">
                    
                    <div class="form-group mb-3">
                        <label for="export_content_type" class="form-label">选择内容类型</label>
                        <select id="export_content_type" name="content_type" class="form-control" required>
                            <option value="article">文章</option>
                            <option value="announcement">公告</option>
                        </select>
                    </div>
                    
                    <div class="form-group mb-4">
                        <label class="form-label">选择导出格式</label>
                        <div class="d-flex flex-wrap gap-2">
                            <button type="submit" name="format" value="txt" class="btn btn-outline-primary" style="margin-right: 5px;">
                                <i class="fas fa-file-alt"></i> TXT文本
                            </button>
                            <button type="submit" name="format" value="markdown" class="btn btn-outline-success" style="margin-right: 5px;">
                                <i class="fab fa-markdown"></i> Markdown
                            </button>
                            <button type="submit" name="format" value="html" class="btn btn-outline-info">
                                <i class="fas fa-file-code"></i> HTML
                            </button>
                        </div>
                    </div>
                    
                    <div class="export-info">
                        <div class="card border-info">
                            <div class="card-header bg-info text-white">导出格式说明</div>
                            <div class="card-body">
                                <p><strong>导出内容包括：</strong></p>
                                <ul>
                                    <li>所有内容将以ZIP压缩包形式下载</li>
                                    <li>ZIP包含单独的每篇内容文件</li>
                                    <li>同时包含一个合并了所有内容的文件</li>
                                </ul>
                                <p><strong>支持的格式：</strong></p>
                                <ul>
                                    <li><strong>TXT文本</strong>：纯文本格式，适合基本编辑</li>
                                    <li><strong>Markdown</strong>：保留Markdown格式，适合再次编辑</li>
                                    <li><strong>HTML</strong>：渲染为HTML页面，适合直接查看</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 如果用户从其他页面带参数跳转过来，自动设置对应的内容类型
    const urlParams = new URLSearchParams(window.location.search);
    const contentType = urlParams.get('type');
    
    if (contentType === 'article' || contentType === 'announcement') {
        document.getElementById('content_type').value = contentType;
        document.getElementById('export_content_type').value = contentType;
    }
});
</script>

    </div><!-- 结束 .container -->
</div><!-- 结束 .main-content -->

<?php
// 包含底部<!-- 结束 .container -->
</div><!-- 结束 .main-content -->

<?php
include 'includes/footer.php';

// 清理临时文件
if (isset($temp_dir) && file_exists($temp_dir)) {
    register_shutdown_function(function() use ($temp_dir) {
        Utils::deleteDirectory($temp_dir);
    });
}
?> 