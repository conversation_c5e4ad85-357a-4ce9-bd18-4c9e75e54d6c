// 引入API请求
const api = require('../../utils/api')
const util = require('../../utils/util')

/**
 * 玩家列表页面
 * 
 * 性能优化点:
 * 1. 使用批量API替代多个单独请求，减少网络请求次数
 * 2. 延迟加载统计数据，优先展示基本信息提升首屏速度
 * 3. 图片懒加载和缓存处理，减少不必要的图片处理操作
 * 4. 优化数据处理流程，减少不必要的数据转换
 * 5. 添加加载状态指示器，提升用户体验
 * 6. 缓存机制优化，确保正确的数据加载和分页
 */

// 缓存相关的常量
const CACHE_KEY = {
  PLAYERS_PREFIX: 'cached_players_page_', // 修改为分页缓存key
  STATS: 'cached_player_stats',
  TIMESTAMP: 'players_cache_timestamp',
  TOTAL_PAGES: 'cached_total_pages' // 新增：记录总页数
}
const CACHE_DURATION = 5 * 60 * 1000 // 缓存时间5分钟

// 工具函数：格式化KD和胜率
const formatKD = val => {
  const num = parseFloat(val);
  return isNaN(num) ? '0.00' : num.toFixed(2);
};

const formatWinRate = val => {
  if (val === undefined || val === null) return '0.00%';
  if (typeof val === 'string' && val.includes('%')) {
    // 已经是百分比字符串
    const num = parseFloat(val.replace('%', ''));
    return isNaN(num) ? '0.00%' : num.toFixed(2) + '%';
  }
  // 其它情况，转数字
  let num = parseFloat(val);
  if (isNaN(num)) return '0.00%';
  if (num <= 1) num = num * 100;
  return num.toFixed(2) + '%';
};

// 新增：合并win_rate时不跳过0
const getWinRate = (player, cache) => {
  if (player.win_rate !== undefined && player.win_rate !== null) return player.win_rate;
  if (cache.win_rate !== undefined && cache.win_rate !== null) return cache.win_rate;
  return undefined;
};

const getKD = (player, cache) => {
  if (player.kd !== undefined && player.kd !== null) return player.kd;
  if (cache.kd !== undefined && cache.kd !== null) return cache.kd;
  return undefined;
};

Page({
  data: {
    players: [],
    searchKeyword: '',
    searchHistory: [],
    loading: false,
    errorMsg: '',
    retryCount: 0,
    sortField: '', // 排序字段
    sortOrder: 'desc', // 排序顺序：asc 升序，desc 降序
    hasMore: true, // 是否还有更多数据
    statsCache: {}, // 缓存玩家统计数据
    offset: 0, // 数据偏移量
    pageSize: 25, // 每页加载数量
    currentPage: 0, // 当前页码
  },

  onLoad: function () {
    // 初始化搜索历史为空，并重置排序
    this.setData({ 
      searchHistory: [],
      sortField: '',      // 重置排序字段
      sortOrder: 'desc',  // 重置排序顺序
      offset: 0,         // 重置偏移量
      hasMore: true,     // 重置加载状态
      currentPage: 0     // 重置当前页码
    })
    
    // 尝试从缓存加载数据，如果没有缓存会自动加载首页数据
    this.loadFromCache()
  },

  // 从缓存加载数据
  loadFromCache: function() {
    try {
      // 获取缓存时间戳
      const timestamp = wx.getStorageSync(CACHE_KEY.TIMESTAMP)
      const now = Date.now()
      
      // 检查缓存是否过期
      if (timestamp && (now - timestamp) < CACHE_DURATION) {
        // 只加载第一页缓存
        const firstPageKey = `${CACHE_KEY.PLAYERS_PREFIX}0`
        const cachedFirstPage = wx.getStorageSync(firstPageKey)
        const cachedStats = wx.getStorageSync(CACHE_KEY.STATS)
        const totalPages = wx.getStorageSync(CACHE_KEY.TOTAL_PAGES) || 0
        
        if (cachedFirstPage && cachedFirstPage.length > 0) {
          // 使用缓存数据
          this.setData({
            players: cachedFirstPage,
            statsCache: cachedStats || {},
            loading: false,
            hasMore: totalPages > 1, // 如果有多页，则标记可以加载更多
            currentPage: 0,
            offset: cachedFirstPage.length // 确保offset正确设置为已加载的数据量
          })
          console.log('从缓存加载第一页数据成功')
          return true
        }
      }

      // 如果没有缓存或缓存已过期，加载首页数据
      this.loadPlayers()
      return false
    } catch (err) {
      console.error('读取缓存失败:', err)
      // 发生错误时也尝试加载首页数据
      this.loadPlayers()
      return false
    }
  },

  // 更新缓存
  updateCache: function(players, stats, currentPage) {
    try {
      // 只缓存前2页的数据
      if (currentPage >= 2) {
        console.log('超过最大缓存页数限制(2页)，不再缓存')
        return
      }

      // 计算当前页的key
      const pageKey = `${CACHE_KEY.PLAYERS_PREFIX}${currentPage}`
      
      // 存储当前页数据
      if (players) {
        wx.setStorageSync(pageKey, players)
      }
      
      // 更新统计数据缓存，限制缓存数量
      if (stats) {
        const currentStats = wx.getStorageSync(CACHE_KEY.STATS) || {}
        const allStats = { ...currentStats, ...stats }
        
        // 如果统计数据超出限制，只保留最新的50条
        const statsEntries = Object.entries(allStats)
        if (statsEntries.length > 50) {
          const limitedStats = Object.fromEntries(
            statsEntries.slice(-50)
          )
          wx.setStorageSync(CACHE_KEY.STATS, limitedStats)
          console.log('统计数据超出限制，已截取最新50条')
        } else {
          wx.setStorageSync(CACHE_KEY.STATS, allStats)
        }
      }
      
      // 更新缓存时间戳
      wx.setStorageSync(CACHE_KEY.TIMESTAMP, Date.now())
      
      // 更新总页数，但不超过最大缓存页数
      const totalPages = Math.max(
        currentPage + 1,
        wx.getStorageSync(CACHE_KEY.TOTAL_PAGES) || 0
      )
      wx.setStorageSync(CACHE_KEY.TOTAL_PAGES, Math.min(totalPages, 5))
      
      console.log(`更新第${currentPage}页缓存成功`)
    } catch (err) {
      console.error('更新缓存失败:', err)
    }
  },

  // 从缓存加载更多数据
  loadMoreFromCache: function() {
    try {
      const nextPage = this.data.currentPage + 1
      const nextPageKey = `${CACHE_KEY.PLAYERS_PREFIX}${nextPage}`
      const cachedNextPage = wx.getStorageSync(nextPageKey)
      
      if (cachedNextPage && cachedNextPage.length > 0) {
        // 合并新数据
        const newPlayers = [...this.data.players, ...cachedNextPage]
        const totalPages = wx.getStorageSync(CACHE_KEY.TOTAL_PAGES) || 0
        
        this.setData({
          players: newPlayers,
          currentPage: nextPage,
          hasMore: nextPage < totalPages - 1,
          offset: this.data.offset + cachedNextPage.length // 更新offset
        })
        console.log(`从缓存加载第${nextPage}页数据成功`)
        return true
      }
      return false
    } catch (err) {
      console.error('加载更多缓存数据失败:', err)
      return false
    }
  },

  // 清除所有缓存
  clearAllCache: function() {
    try {
      // 获取总页数
      const totalPages = wx.getStorageSync(CACHE_KEY.TOTAL_PAGES) || 0
      
      // 清除所有页面缓存
      for (let i = 0; i < totalPages; i++) {
        wx.removeStorageSync(`${CACHE_KEY.PLAYERS_PREFIX}${i}`)
      }
      
      // 清除其他缓存
      wx.removeStorageSync(CACHE_KEY.STATS)
      wx.removeStorageSync(CACHE_KEY.TIMESTAMP)
      wx.removeStorageSync(CACHE_KEY.TOTAL_PAGES)
      
      console.log('清除所有缓存成功')
    } catch (err) {
      console.error('清除缓存失败:', err)
    }
  },

  // 加载玩家列表
  loadPlayers: function () {
    // 如果正在加载或没有更多数据，直接返回
    if (this.data.loading || !this.data.hasMore) return
    
    // 尝试从缓存加载更多数据
    if (this.data.offset > 0 && this.loadMoreFromCache()) {
      return
    }
    
    this.setData({ 
      loading: true,
      errorMsg: ''
    })
    
    // 记录当前offset，用于防止并发请求导致数据重复
    const currentOffset = this.data.offset
    const currentPage = Math.floor(currentOffset / this.data.pageSize)
    
    console.log(`加载玩家数据: offset=${currentOffset}, page=${currentPage}`)
    
    // 显示加载状态
    wx.showLoading({
      title: `加载第${currentPage + 1}页`,
      mask: false
    })
    
    api.getPlayers({
      offset: currentOffset,
      limit: this.data.pageSize
    })
      .then(result => {
        // 确保result是一个数组
        const players = Array.isArray(result) ? result : (result.players || result.data || [])
        
        console.log(`API返回数据: ${players.length}条`)
        
        // 如果返回的数据少于请求的数量，说明没有更多数据了
        const hasMore = players.length >= this.data.pageSize
        
        // 只处理必要的数据，减少转换操作
        const processedPlayers = players.map(player => {
          // 只处理必要的字段，其他字段保持原样
          return {
            ...player,
            avatar: player.avatar ? util.getOptimizedImagePath(player.avatar) : '/images/default-avatar.png',
            // 基本数据设置默认值，详细统计数据稍后再加载
            kd: player.kd || '0.00',
            win_rate: player.win_rate || '0.00%',
            total_kills: player.total_kills || 0,
            total_deaths: player.total_deaths || 0
          }
        })
        
        // 检查是否有重复数据
        const existingNicknames = currentOffset === 0 ? new Set() : new Set(this.data.players.map(p => p.nickname))
        const uniquePlayers = processedPlayers.filter(p => !existingNicknames.has(p.nickname))
        
        console.log(`过滤后唯一数据: ${uniquePlayers.length}条`)
        
        // 只有在没有重复数据时才更新列表
        if (uniquePlayers.length > 0) {
          // 合并新数据和旧数据
          const newPlayers = currentOffset === 0 ? uniquePlayers : [...this.data.players, ...uniquePlayers]
          
          this.setData({ 
            players: newPlayers,
            loading: false,
            retryCount: 0,
            hasMore: hasMore && uniquePlayers.length > 0,
            offset: currentOffset + uniquePlayers.length,
            currentPage
          })

          // 更新当前页的缓存
          this.updateCache(uniquePlayers, this.data.statsCache, currentPage)

          // 延迟加载玩家统计数据，优先展示基本信息
          setTimeout(() => {
            this.preloadPlayerStats(uniquePlayers)
          }, 500)
        } else {
          // 如果全是重复数据，标记没有更多数据了
          this.setData({
            loading: false,
            hasMore: false
          })
        }
        
        // 隐藏加载状态
        wx.hideLoading()
      })
      .catch(err => {
        console.error('加载玩家列表失败:', err)
        this.setData({ 
          loading: false,
          errorMsg: typeof err === 'string' ? err : '获取玩家列表失败'
        })
        
        // 隐藏加载状态
        wx.hideLoading()
        
        util.showToast('获取玩家列表失败')
      })
  },

  // 预加载玩家统计数据
  preloadPlayerStats: function(players) {
    // 只预加载未缓存的玩家数据，且限制数量
    const uncachedPlayers = players
      .filter(p => !this.data.statsCache[p.nickname])
      .slice(0, 50) // 限制最大处理数量为50

    if (uncachedPlayers.length === 0) return

    // 显示加载状态指示器
    if (uncachedPlayers.length > 5) { // 如果需要加载的数据较多，才显示加载提示
      wx.showLoading({
        title: '加载玩家数据...',
        mask: false // 不阻止用户操作
      })
    }

    // 获取未缓存玩家的昵称列表
    const nicknames = uncachedPlayers.map(player => player.nickname)
    
    console.log(`准备加载${nicknames.length}个玩家的统计数据`)

    // 使用批量API获取玩家统计数据
    api.getPlayersStatsBatch(nicknames)
      .then(statsMap => {
        // 更新统计数据缓存
        const statsCache = { ...this.data.statsCache }
        
        // 处理返回的批量数据
        Object.keys(statsMap).forEach(nickname => {
          const stats = statsMap[nickname]
          if (stats) {
            statsCache[nickname] = {
              kd: stats.kd !== undefined && stats.kd !== null && stats.kd !== '' ? stats.kd : '0.00',
              win_rate: stats.win_rate !== undefined && stats.win_rate !== null && stats.win_rate !== '' ? stats.win_rate : undefined,
              // 添加其他可能有用的字段
              total_kills: stats.total_kills,
              total_deaths: stats.total_deaths,
              total_wins: stats.total_wins,
              total_losses: stats.total_losses,
              player_rank: stats.player_rank
            }
          }
        })
        
        this.setData({ statsCache })
        
        // 更新统计数据缓存
        this.updateCache(null, statsCache, this.data.currentPage)
        
        // 更新显示的玩家数据，优先用接口返回的kd/win_rate，没有再用缓存
        const updatedPlayers = this.data.players.map(player => {
          const cache = statsCache[player.nickname] || {};
          return {
            ...player,
            kd: formatKD(getKD(player, cache)),
            win_rate: formatWinRate(getWinRate(player, cache)),
            // 更新其他字段
            player_rank: player.player_rank || cache.player_rank,
            total_kills: player.total_kills || cache.total_kills,
            total_deaths: player.total_deaths || cache.total_deaths
          }
        })
        
        this.setData({ players: updatedPlayers })
        
        // 隐藏加载状态指示器
        if (uncachedPlayers.length > 5) {
          wx.hideLoading()
        }
        
        console.log(`成功加载${Object.keys(statsMap).length}个玩家的统计数据`)
      })
      .catch(err => {
        console.error('批量获取玩家统计数据失败:', err)
        
        // 隐藏加载状态指示器
        if (uncachedPlayers.length > 5) {
          wx.hideLoading()
        }
        
        // 如果批量API失败，回退到逐个请求（但限制并发数量）
        this.fallbackToIndividualRequests(uncachedPlayers)
      })
  },
  
  // 批量API失败时的回退方法
  fallbackToIndividualRequests: function(players) {
    if (players.length === 0) return
    
    // 限制并发请求数量，每批最多5个
    const batchSize = 5
    const batches = []
    
    // 将玩家分组
    for (let i = 0; i < players.length; i += batchSize) {
      batches.push(players.slice(i, i + batchSize))
    }
    
    // 按批次依次处理
    const processBatch = (batchIndex) => {
      if (batchIndex >= batches.length) {
        // 所有批次处理完成
        return Promise.resolve()
      }
      
      const currentBatch = batches[batchIndex]
      
      // 处理当前批次
      return Promise.all(currentBatch.map(player => 
        api.getPlayerStats(player.nickname)
          .then(stats => {
            const statsCache = { ...this.data.statsCache }
            statsCache[player.nickname] = {
              kd: stats.kd !== undefined && stats.kd !== null && stats.kd !== '' ? stats.kd : '0.00',
              win_rate: stats.win_rate !== undefined && stats.win_rate !== null && stats.win_rate !== '' ? stats.win_rate : undefined,
              // 添加其他可能有用的字段
              total_kills: stats.total_kills,
              total_deaths: stats.total_deaths,
              total_wins: stats.total_wins,
              total_losses: stats.total_losses,
              player_rank: stats.player_rank
            }
            this.setData({ statsCache })
            
            return stats
          })
          .catch(err => {
            console.error(`获取玩家[${player.nickname}]统计数据失败:`, err)
            return null
          })
      ))
      .then(() => {
        // 处理下一批
        return processBatch(batchIndex + 1)
      })
    }
    
    // 开始处理第一批
    processBatch(0).then(() => {
      // 更新统计数据缓存
      this.updateCache(null, this.data.statsCache, this.data.currentPage)
      
      // 更新显示的玩家数据
      const updatedPlayers = this.data.players.map(player => {
        const cache = this.data.statsCache[player.nickname] || {};
        return {
          ...player,
          kd: formatKD(getKD(player, cache)),
          win_rate: formatWinRate(getWinRate(player, cache)),
          // 更新其他字段
          player_rank: player.player_rank || cache.player_rank,
          total_kills: player.total_kills || cache.total_kills,
          total_deaths: player.total_deaths || cache.total_deaths,
          total_wins: cache.total_wins,
          total_losses: cache.total_losses
        }
      })
      this.setData({ players: updatedPlayers })
    })
  },
  
  // 重试加载
  retryLoading: function() {
    var retryCount = this.data.retryCount;
    if (retryCount < 3) {
      this.setData({ retryCount: retryCount + 1 })
      this.loadPlayers()
    } else {
      util.showToast('多次重试失败，请检查网络连接')
    }
  },
  
  // 搜索框输入
  onSearchInput: function (e) {
    this.setData({
      searchKeyword: e.detail.value
    })
  },
  
  // 执行搜索
  doSearch: function () {
    var searchKeyword = this.data.searchKeyword;
    var searchHistory = this.data.searchHistory;
    if (!searchKeyword.trim()) {
      return
    }
    
    // 保存搜索历史（仅在内存中）
    if (searchHistory.indexOf(searchKeyword) === -1) {
      var newHistory = [searchKeyword];
      for (var i = 0; i < searchHistory.length && i < 9; i++) {
        newHistory.push(searchHistory[i]);
      }
      this.setData({ searchHistory: newHistory })
    }
    
    // 执行模糊搜索前重置状态
    this.setData({ 
      loading: true,
      errorMsg: '',
      offset: 0,
      hasMore: false, // 搜索模式下不使用分页
      players: [] // 清空当前列表
    })
    
    // 显示加载状态
    wx.showLoading({
      title: '搜索中...',
      mask: false
    })
    
    api.searchPlayers(searchKeyword)
      .then(result => {
        const players = Array.isArray(result) ? result : (result.players || result.data || [])
        
        if (players.length > 0) {
          // 先展示基本数据
          const processedPlayers = players.map(player => ({
            ...player,
            avatar: player.avatar ? util.getOptimizedImagePath(player.avatar) : '/images/default-avatar.png',
            kd: player.kd || '0.00',
            win_rate: player.win_rate || '0.00%',
            total_kills: player.total_kills || 0,
            total_deaths: player.total_deaths || 0,
          }));
          
          this.setData({ 
            players: processedPlayers,
            loading: false,
            hasMore: false // 搜索结果不支持分页加载
          });
          
          // 隐藏加载状态
          wx.hideLoading();
          
          // 获取所有玩家的昵称列表
          const nicknames = players.map(player => player.nickname);
          
          console.log(`准备加载${nicknames.length}个搜索结果玩家的统计数据`);
          
          // 使用批量API获取玩家统计数据
          api.getPlayersStatsBatch(nicknames)
            .then(statsMap => {
              // 更新玩家数据
              const updatedPlayers = this.data.players.map(player => {
                const stats = statsMap[player.nickname];
                if (stats) {
                  return {
                    ...player,
                    kd: formatKD(stats.kd !== undefined && stats.kd !== null && stats.kd !== '' ? stats.kd : player.kd),
                    win_rate: formatWinRate(stats.win_rate !== undefined && stats.win_rate !== null && stats.win_rate !== '' ? stats.win_rate : player.win_rate),
                    // 更新其他字段
                    player_rank: player.player_rank || stats.player_rank,
                    total_kills: player.total_kills || stats.total_kills,
                    total_deaths: player.total_deaths || stats.total_deaths,
                    total_wins: stats.total_wins,
                    total_losses: stats.total_losses
                  };
                }
                return player;
              });
              
              this.setData({ 
                players: updatedPlayers
              });
              
              console.log(`成功加载${Object.keys(statsMap).length}个搜索结果玩家的统计数据`);
            })
            .catch(err => {
              console.error('获取搜索玩家统计数据失败:', err);
              // 错误时不更新UI，保持原有数据
            });
        } else {
          this.setData({ 
            players: [], 
            loading: false,
            errorMsg: '未找到相关玩家',
            hasMore: false
          });
          
          // 隐藏加载状态
          wx.hideLoading();
          
          util.showToast('未找到相关玩家');
        }
      })
      .catch(err => {
        this.setData({ 
          loading: false,
          errorMsg: typeof err === 'string' ? err : '搜索失败',
          hasMore: false
        });
        
        // 隐藏加载状态
        wx.hideLoading();
        
        console.error('搜索玩家失败:', err);
        util.showToast('搜索失败');
      });
  },
  
  // 点击搜索历史
  onTapHistory: function (e) {
    var keyword = e.currentTarget.dataset.keyword
    this.setData({ searchKeyword: keyword })
    this.doSearch()
  },
  
  // 清除搜索历史
  clearHistory: function () {
    var that = this;
    util.showConfirm('提示', '确定要清空搜索历史吗？')
      .then(function() {
        that.setData({ searchHistory: [] })
      })
      .catch(function() {})
  },
  
  // 点击玩家卡片
  onTapPlayer: function (e) {
    var nickname = e.currentTarget.dataset.nickname
    wx.navigateTo({
      url: './player-detail/player-detail?nickname=' + encodeURIComponent(nickname)
    })
  },
  
  // 下拉刷新
  onPullDownRefresh: function () {
    // 清空搜索历史、重置搜索关键词和排序
    this.setData({ 
      searchHistory: [],
      searchKeyword: '',
      sortField: '',      // 重置排序字段
      sortOrder: 'desc',  // 重置排序顺序
      offset: 0,          // 重置偏移量
      hasMore: true,      // 重置加载状态
      players: [],        // 清空玩家列表
      currentPage: 0      // 重置当前页码
    })

    // 清除所有缓存
    this.clearAllCache()

    // 加载玩家列表
    this.loadPlayers()
    wx.stopPullDownRefresh()
  },

  // 处理排序
  onSort(e) {
    const field = e.currentTarget.dataset.field;
    let order = this.data.sortOrder;
    
    // 如果点击同一个字段，切换排序顺序
    if (field === this.data.sortField) {
      order = order === 'asc' ? 'desc' : 'asc';
    } else {
      // 新字段默认降序
      order = 'desc';
    }

    this.setData({
      sortField: field,
      sortOrder: order
    });

    // 执行排序
    this.sortPlayers(field, order);
  },

  // 排序玩家列表
  sortPlayers(field, order) {
    const players = [...this.data.players];
    
    players.sort((a, b) => {
      let valueA = a[field];
      let valueB = b[field];
      
      // 处理不同类型的数据
      if (field === 'win_rate' || field === 'kd') {
        // 移除百分号并转换为数字
        valueA = parseFloat(String(valueA).replace('%', '')) || 0;
        valueB = parseFloat(String(valueB).replace('%', '')) || 0;
      } else if (field === 'total_kills' || field === 'total_deaths') {
        valueA = parseInt(valueA) || 0;
        valueB = parseInt(valueB) || 0;
      }

      if (order === 'asc') {
        return valueA - valueB;
      } else {
        return valueB - valueA;
      }
    });

    this.setData({
      players: players
    });
  },

  // 触底加载更多
  onReachBottom: function() {
    console.log('触发触底加载更多', {
      searchKeyword: this.data.searchKeyword,
      hasMore: this.data.hasMore,
      loading: this.data.loading
    });
    
    // 只有在非搜索模式下才加载更多数据
    if (!this.data.searchKeyword && this.data.hasMore && !this.data.loading) {
      this.loadPlayers();
    }
  },

  // 分享功能
  onShareAppMessage: function () {
    return {
      title: '玩家排行榜',
      path: '/pages/match/match'
    }
  },

  // 分享到朋友圈
  onShareTimeline: function () {
    return {
      title: '玩家排行榜',
      path: '/pages/match/match',
      query: '',
      success: function(res) {
        wx.showToast({
          title: '分享成功',
          icon: 'success',
          duration: 2000
        });
      },
      fail: function(res) {
        wx.showToast({
          title: '分享失败',
          icon: 'none',
          duration: 2000
        });
      }
    }
  },
}) 