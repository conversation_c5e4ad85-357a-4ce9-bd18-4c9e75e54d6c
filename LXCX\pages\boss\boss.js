// 引入API请求
const api = require('../../utils/api')
const util = require('../../utils/util')

// 缓存相关的常量
const CACHE_KEY = {
  PLAYERS_PREFIX: 'cached_boss_players_page_', // 修改为分页缓存key
  STATS: 'cached_boss_player_stats',
  TIMESTAMP: 'boss_players_cache_timestamp',
  TOTAL_PAGES: 'cached_boss_total_pages' // 记录总页数
}
const CACHE_DURATION = 5 * 60 * 1000 // 缓存时间5分钟

// 工具函数：格式化KD
const formatKD = val => {
  const num = parseFloat(val);
  return isNaN(num) ? '0.00' : num.toFixed(2);
};

const getKD = (player, cache) => {
  if (player.kd !== undefined && player.kd !== null) return player.kd;
  if (cache.kd !== undefined && cache.kd !== null) return cache.kd;
  return undefined;
};

Page({
  data: {
    players: [],
    loading: true,
    errorMsg: '',
    searchKeyword: '',
    searchHistory: [],
    sortField: '',  // 默认按KD排序
    sortOrder: 'desc', // 默认降序排序
    retryCount: 0,
    hasMore: true, // 是否还有更多数据
    statsCache: {}, // 缓存玩家统计数据
    offset: 0, // 数据偏移量
    pageSize: 20, // 每页加载数量
    currentPage: 0, // 当前页码
  },

  onLoad: function() {
    // 获取全局数据
    const app = getApp();
    if (!app.globalData.apiBaseUrl || !app.globalData.apiKey) {
      console.error('全局数据未初始化:', app.globalData);
      this.setData({
        errorMsg: '应用初始化失败，请重试',
        loading: false
      });
      return;
    }

    // 初始化数据状态
    this.setData({
      hasMore: true,
      offset: 0,
      currentPage: 0,
      players: [],
      loading: true // 设置初始loading状态
    });
    
    // 直接加载数据，不使用缓存
    this.loadBossData();
    
    // 获取搜索历史
    const history = wx.getStorageSync('boss_searchHistory') || [];
    this.setData({ searchHistory: history });
  },

  onPullDownRefresh: function() {
    // 重置所有状态
    this.setData({ 
      searchHistory: [], // 重置搜索历史
      searchKeyword: '',
      offset: 0,          // 重置偏移量
      hasMore: true,      // 重置加载状态
      players: [],        // 清空玩家列表
      currentPage: 0,     // 重置当前页码
      loading: false,     // 重置加载状态
      sortField: '',      // 重置排序字段
      sortOrder: 'desc'   // 重置排序顺序为默认降序
    });

    // 清除所有缓存
    this.clearAllCache();
    
    // 清除搜索历史缓存
    wx.removeStorageSync('boss_searchHistory');

    // 重新加载盟主模式数据
    this.loadBossData();
    
    wx.stopPullDownRefresh();
  },

  // 从缓存加载数据
  loadFromCache: function() {
    try {
      // 获取缓存时间戳
      const timestamp = wx.getStorageSync(CACHE_KEY.TIMESTAMP)
      const now = Date.now()
      
      // 检查缓存是否过期
      if (timestamp && (now - timestamp) < CACHE_DURATION) {
        // 只加载第一页缓存
        const firstPageKey = `${CACHE_KEY.PLAYERS_PREFIX}0`
        const cachedFirstPage = wx.getStorageSync(firstPageKey)
        const cachedStats = wx.getStorageSync(CACHE_KEY.STATS)
        const totalPages = wx.getStorageSync(CACHE_KEY.TOTAL_PAGES) || 0
        
        if (cachedFirstPage && cachedFirstPage.length > 0) {
          // 使用缓存数据
          this.setData({
            players: cachedFirstPage,
            statsCache: cachedStats || {},
            loading: false,
            hasMore: totalPages > 1, // 如果有多页，则标记可以加载更多
            currentPage: 0
          })
          console.log('从缓存加载第一页暗杀模式数据成功')
          return true
        }
      }

      // 如果没有缓存或缓存已过期，加载首页数据
      this.loadBossData()
      return false
    } catch (err) {
      console.error('读取缓存失败:', err)
      // 发生错误时也尝试加载首页数据
      this.loadBossData()
      return false
    }
  },

  // 更新缓存
  updateCache: function(players, stats, currentPage) {
    try {
      // 只缓存前5页的数据
      if (currentPage >= 5) {
        console.log('超过最大缓存页数限制(5页)，不再缓存')
        return
      }

      // 计算当前页的key
      const pageKey = `${CACHE_KEY.PLAYERS_PREFIX}${currentPage}`
      
      // 存储当前页数据
      if (players) {
        wx.setStorageSync(pageKey, players)
      }
      
      // 更新统计数据缓存，限制缓存数量
      if (stats) {
        const currentStats = wx.getStorageSync(CACHE_KEY.STATS) || {}
        const allStats = { ...currentStats, ...stats }
        
        // 如果统计数据超出限制，只保留最新的100条
        const statsEntries = Object.entries(allStats)
        if (statsEntries.length > 100) {
          const limitedStats = Object.fromEntries(
            statsEntries.slice(-100)
          )
          wx.setStorageSync(CACHE_KEY.STATS, limitedStats)
          console.log('统计数据超出限制，已截取最新100条')
        } else {
          wx.setStorageSync(CACHE_KEY.STATS, allStats)
        }
      }
      
      // 更新缓存时间戳
      wx.setStorageSync(CACHE_KEY.TIMESTAMP, Date.now())
      
      // 更新总页数，但不超过最大缓存页数
      const totalPages = Math.min(
        Math.ceil(this.data.offset / this.data.pageSize),
        5
      )
      wx.setStorageSync(CACHE_KEY.TOTAL_PAGES, totalPages)
      
      console.log(`更新第${currentPage}页缓存成功`)
    } catch (err) {
      console.error('更新缓存失败:', err)
    }
  },

  // 从缓存加载更多数据
  loadMoreFromCache: function() {
    try {
      const nextPage = this.data.currentPage + 1
      const nextPageKey = `${CACHE_KEY.PLAYERS_PREFIX}${nextPage}`
      const cachedNextPage = wx.getStorageSync(nextPageKey)
      
      if (cachedNextPage && cachedNextPage.length > 0) {
        // 合并新数据
        const newPlayers = [...this.data.players, ...cachedNextPage]
        const totalPages = wx.getStorageSync(CACHE_KEY.TOTAL_PAGES) || 0
        
        this.setData({
          players: newPlayers,
          currentPage: nextPage,
          hasMore: nextPage < totalPages - 1
        })
        console.log(`从缓存加载第${nextPage}页数据成功`)
        return true
      }
      return false
    } catch (err) {
      console.error('加载更多缓存数据失败:', err)
      return false
    }
  },

  // 清除所有缓存
  clearAllCache: function() {
    try {
      // 获取总页数
      const totalPages = wx.getStorageSync(CACHE_KEY.TOTAL_PAGES) || 0
      
      // 清除所有页面缓存
      for (let i = 0; i < totalPages; i++) {
        wx.removeStorageSync(`${CACHE_KEY.PLAYERS_PREFIX}${i}`)
      }
      
      // 清除其他缓存
      wx.removeStorageSync(CACHE_KEY.STATS)
      wx.removeStorageSync(CACHE_KEY.TIMESTAMP)
      wx.removeStorageSync(CACHE_KEY.TOTAL_PAGES)
      
      console.log('清除所有缓存成功')
    } catch (err) {
      console.error('清除缓存失败:', err)
    }
  },

  loadBossData: function() {
    // 如果正在加载，直接返回
    if (this.data.loading && this.data.offset > 0) return;
    
    // 获取全局数据
    const app = getApp();
    const apiBaseUrl = app.globalData.apiBaseUrl;
    const apiKey = app.globalData.apiKey;

    // 检查必要的API配置
    if (!apiBaseUrl || !apiKey) {
      console.error('API配置缺失:', { apiBaseUrl, apiKey });
      this.setData({
        errorMsg: 'API配置错误，请检查配置',
        loading: false
      });
      return;
    }
    
    this.setData({ 
      loading: true,
      errorMsg: ''
    });
    
    console.log('开始加载数据:', {
      apiBaseUrl,
      offset: this.data.offset,
      currentPage: this.data.currentPage,
      hasMore: this.data.hasMore
    });
    
    // 调用API获取暗杀模式的玩家数据
    wx.request({
      url: `${apiBaseUrl}`,
      method: 'GET',
      data: {
        path: 'game_type_status',
        type: '盟主',
        page: this.data.currentPage + 1,
        search: this.data.searchKeyword,
        search_field: 'nickname',
        sort_by: this.data.sortField || '',
        sort_order: this.data.sortOrder || 'desc',
        api_key: apiKey,
        offset: this.data.offset,
        limit: this.data.pageSize || 20
      },
      success: (res) => {
        console.log('API响应:', res.data);
        
        if (res.data && res.data.status === 'success') {
          const players = res.data.data.players || [];
          
          // 如果返回的数据少于请求的数量，说明没有更多数据了
          const hasMore = players.length >= (this.data.pageSize || 20);
          
          // 优化图片路径并使用缓存的统计数据
          const optimizedPlayers = players.map(player => ({
            ...player,
            avatar: player.avatar ? util.getOptimizedImagePath(player.avatar) : '/images/default-avatar.png',
            kd: player.kd || '0.00',
            total_kills: player.total_kills || 0,
            total_deaths: player.total_deaths || 0,
            ...this.data.statsCache[player.nickname]
          }));
          
          // 更新数据
          this.setData({
            players: this.data.offset === 0 ? optimizedPlayers : [...this.data.players, ...optimizedPlayers],
            loading: false,
            hasMore: hasMore,
            offset: this.data.offset + optimizedPlayers.length,
            currentPage: this.data.currentPage + 1
          });
          
          // 更新缓存
          if (optimizedPlayers.length > 0) {
            this.updateCache(optimizedPlayers, this.data.statsCache, this.data.currentPage);
            // 预加载玩家统计数据
            this.preloadPlayerStats(optimizedPlayers);
          } else {
            this.setData({
              errorMsg: '暂无数据',
              loading: false
            });
          }
          
          console.log('数据加载完成:', {
            playerCount: optimizedPlayers.length,
            hasMore: hasMore,
            newOffset: this.data.offset,
            totalPlayers: this.data.players.length
          });
        } else {
          this.setData({
            errorMsg: res.data?.message || '获取数据失败',
            loading: false
          });
          console.error('API返回错误:', res.data);
        }
      },
      fail: (err) => {
        console.error('API请求失败:', err);
        this.setData({
          errorMsg: `网络请求失败: ${err.errMsg || '请检查网络连接'}`,
          loading: false
        });
      }
    });
  },

  // 预加载玩家统计数据
  preloadPlayerStats: function(players) {
    // 只预加载未缓存的玩家数据，且限制数量
    const uncachedPlayers = players
      .filter(p => !this.data.statsCache[p.nickname])
      .slice(0, 100 - Object.keys(this.data.statsCache).length);

    if (uncachedPlayers.length === 0) return;

    // 获取全局数据
    const app = getApp();
    const apiBaseUrl = app.globalData.apiBaseUrl;
    const apiKey = app.globalData.apiKey;

    // 使用Promise.all并行加载所有未缓存的玩家统计数据
    Promise.all(uncachedPlayers.map(player => {
      return new Promise((resolve, reject) => {
        wx.request({
          url: apiBaseUrl,
          data: {
            path: 'player_stats',
            nickname: player.nickname,
            api_key: apiKey
          },
          success: (res) => {
            if (res.data && res.data.status === 'success') {
              const stats = res.data.data;
              const statsCache = { ...this.data.statsCache };
              statsCache[player.nickname] = {
                kd: stats.kd !== undefined && stats.kd !== null && stats.kd !== '' ? stats.kd : '0.00'
              };
              this.setData({ statsCache });
              
              // 更新统计数据缓存
              this.updateCache(null, statsCache, this.data.currentPage);
              
              resolve(stats);
            } else {
              reject(res.data?.message || '获取玩家统计数据失败');
            }
          },
          fail: (err) => {
            reject(err);
          }
        });
      });
    }))
    .then(() => {
      // 更新显示的玩家数据，优先用接口返回的kd，没有再用缓存
      const updatedPlayers = this.data.players.map(player => {
        const cache = this.data.statsCache[player.nickname] || {};
        return {
          ...player,
          kd: formatKD(getKD(player, cache))
        };
      });
      this.setData({ players: updatedPlayers });
    })
    .catch(err => {
      console.error('预加载玩家统计数据失败:', err);
    });
  },

  onSearchInput: function(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  doSearch: function() {
    // 保存搜索历史
    if (this.data.searchKeyword.trim()) {
      let history = this.data.searchHistory;
      // 如果已存在相同关键词，先移除
      const index = history.indexOf(this.data.searchKeyword);
      if (index !== -1) {
        history.splice(index, 1);
      }
      // 添加到历史记录开头
      history.unshift(this.data.searchKeyword);
      // 限制历史记录数量
      if (history.length > 10) {
        history = history.slice(0, 10);
      }
      this.setData({ searchHistory: history });
      wx.setStorageSync('boss_searchHistory', history);
    }
    
    // 执行搜索前重置状态
    this.setData({ 
      loading: true,
      errorMsg: '',
      offset: 0,
      hasMore: false, // 搜索模式下不使用分页
      players: [], // 清空当前列表
      currentPage: 0
    });
    
    // 获取全局数据
    const app = getApp();
    const apiBaseUrl = app.globalData.apiBaseUrl;
    const apiKey = app.globalData.apiKey;
    
    // 调用API搜索暗杀模式的玩家数据
    wx.request({
      url: apiBaseUrl,
      data: {
        path: 'game_type_status',
        type: '盟主',
        search: this.data.searchKeyword,
        search_field: 'nickname', // 指定只搜索玩家昵称
        search_exact: false, // 添加模糊搜索参数
        search_fields: ['nickname'], // 明确指定只搜索nickname字段
        sort_by: this.data.sortField,
        sort_order: this.data.sortOrder,
        api_key: apiKey
      },
      success: (res) => {
        if (res.data && res.data.status === 'success') {
          const players = res.data.data.players || [];
          
          // 在前端再次过滤，确保只匹配昵称
          const filteredPlayers = players.filter(player => 
            player.nickname.toLowerCase().includes(this.data.searchKeyword.toLowerCase())
          );
          
          if (filteredPlayers.length > 0) {
            // 优化图片路径
            const optimizedPlayers = filteredPlayers.map(player => ({
              ...player,
              avatar: player.avatar ? util.getOptimizedImagePath(player.avatar) : '/images/default-avatar.png',
              kd: formatKD(player.kd),
              total_kills: player.total_kills || 0,
              total_deaths: player.total_deaths || 0
            }));
            
            this.setData({
              players: optimizedPlayers,
              loading: false,
              hasMore: false // 搜索结果不支持分页加载
            });
            
            // 预加载玩家统计数据
            this.preloadPlayerStats(optimizedPlayers);
          } else {
            this.setData({
              players: [],
              loading: false,
              errorMsg: '未找到相关玩家',
              hasMore: false
            });
            util.showToast('未找到相关玩家');
          }
        } else {
          this.setData({
            errorMsg: res.data?.message || '搜索失败',
            loading: false,
            hasMore: false
          });
        }
      },
      fail: (err) => {
        this.setData({
          errorMsg: '网络请求失败，请检查网络连接',
          loading: false,
          hasMore: false
        });
        console.error('API请求失败:', err);
      }
    });
  },

  onTapHistory: function(e) {
    const keyword = e.currentTarget.dataset.keyword;
    this.setData({ searchKeyword: keyword });
    this.doSearch();
  },

  clearHistory: function() {
    this.setData({ searchHistory: [] });
    wx.removeStorageSync('boss_searchHistory');
  },

  onSort: function(e) {
    const field = e.currentTarget.dataset.field;
    
    // 如果点击的是当前排序字段，则切换排序顺序
    if (field === this.data.sortField) {
      this.setData({
        sortOrder: this.data.sortOrder === 'asc' ? 'desc' : 'asc'
      });
    } else {
      // 如果点击的是新字段，设置为降序
      this.setData({
        sortField: field,
        sortOrder: 'desc'
      });
    }
    
    // 如果是搜索状态，直接重新搜索
    if (this.data.searchKeyword) {
      this.doSearch();
    } else {
      // 否则对当前列表进行排序
      this.sortPlayers(field, this.data.sortOrder);
    }
  },

  // 排序玩家列表
  sortPlayers(field, order) {
    const players = [...this.data.players];
    
    players.sort((a, b) => {
      let valueA = a[field];
      let valueB = b[field];
      
      // 处理不同类型的数据
      if (field === 'kd') {
        // 转换为数字
        valueA = parseFloat(String(valueA)) || 0;
        valueB = parseFloat(String(valueB)) || 0;
      } else if (field === 'total_kills' || field === 'total_deaths') {
        valueA = parseInt(valueA) || 0;
        valueB = parseInt(valueB) || 0;
      }

      if (order === 'asc') {
        return valueA - valueB;
      } else {
        return valueB - valueA;
      }
    });

    this.setData({
      players: players
    });
  },

  onTapPlayer: function(e) {
    const nickname = e.currentTarget.dataset.nickname;
    // 跳转到通用玩家详情页，传递游戏类型参数
    wx.navigateTo({
      url: '/pages/match/player-detail/player-detail?nickname=' + encodeURIComponent(nickname) + '&game_type=' + encodeURIComponent('盟主')
    });
  },

  retryLoading: function() {
    var retryCount = this.data.retryCount;
    if (retryCount < 3) {
      this.setData({ retryCount: retryCount + 1 });
      this.loadBossData();
    } else {
      util.showToast('多次重试失败，请检查网络连接');
    }
  },

  // 触底加载更多
  onReachBottom: function() {
    // 只有在非搜索模式下才加载更多数据
    if (!this.data.searchKeyword && this.data.hasMore) {
      this.loadBossData();
    }
  },

  // 分享功能
  onShareAppMessage: function () {
    return {
      title: '盟主模式排行榜',
      path: '/pages/boss/boss'
    };
  },

  // 分享到朋友圈
  onShareTimeline: function () {
    return {
      title: '盟主模式排行榜',
      path: '/pages/boss/boss',
      query: '',
      success: function(res) {
        wx.showToast({
          title: '分享成功',
          icon: 'success',
          duration: 2000
        });
      },
      fail: function(res) {
        wx.showToast({
          title: '分享失败',
          icon: 'none',
          duration: 2000
        });
      }
    }
  },

  // 返回上一页
  goBack: function() {
    wx.navigateBack();
  }
}) 