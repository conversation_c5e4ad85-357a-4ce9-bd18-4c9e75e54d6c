// 引入API请求
const api = require('../../utils/api')
const util = require('../../utils/util')

Page({
  data: {
    announcements: [],
    loading: true,
    errorMsg: '',
    retryCount: 0
  },

  onLoad: function () {
    this.loadAnnouncements()
  },
  
  // 加载公告列表
  loadAnnouncements: function () {
    var that = this;
    
    this.setData({ 
      loading: true,
      errorMsg: ''
    })
    
    api.getAnnouncements()
      .then(function(announcements) {
        // 处理日期显示
        var processedAnnouncements = [];
        
        for (var i = 0; i < announcements.length; i++) {
          var item = announcements[i];
          // 使用updated_at作为发布时间
          var publish_time = item.update_time ? util.formatTime(util.parseDate(item.update_time)) : '未知时间';
          
          // 检查是否为新公告（7天内）
          var isNew = item.created_at ? 
            (new Date().getTime() - util.parseDate(item.created_at).getTime()) / (1000 * 3600 * 24) <= 7 : 
            false;
            
          // 检查标题是否包含"置顶"
          var isTop = item.title && item.title.indexOf('置顶') !== -1;
          
          var newAnnouncement = {
            id: item.id,
            title: item.title,
            summary: item.summary,
            content: item.content,
            created_at: item.created_at,
            updated_at: item.updated_at,
            cover_image: item.cover_image,
            author: item.author,
            publish_time: publish_time,
            is_new: isNew,
            is_top: isTop
          };
          
          // 优化图片
          if (newAnnouncement.cover_image) {
            newAnnouncement.cover_image = util.getOptimizedImagePath(newAnnouncement.cover_image);
          }
          
          processedAnnouncements.push(newAnnouncement);
        }
        
        // 先按update_time进行排序（降序）
        processedAnnouncements.sort(function(a, b) {
          if (!a.updated_at) return 1;
          if (!b.updated_at) return -1;
          return util.parseDate(b.updated_at) - util.parseDate(a.updated_at);
        });
        
        // 置顶文章优先显示
        processedAnnouncements.sort(function(a, b) {
          if (a.is_top && !b.is_top) return -1;
          if (!a.is_top && b.is_top) return 1;
          return 0;
        });
        
        that.setData({
          announcements: processedAnnouncements,
          loading: false,
          retryCount: 0
        });
      })
      .catch(function(err) {
        console.error('加载公告列表失败:', err);
        that.setData({ 
          loading: false,
          errorMsg: typeof err === 'string' ? err : '获取公告列表失败'
        });
        util.showToast('获取公告列表失败');
      });
  },
  
  // 重试加载
  retryLoading: function() {
    var retryCount = this.data.retryCount;
    if (retryCount < 3) {
      this.setData({ retryCount: retryCount + 1 });
      this.loadAnnouncements();
    } else {
      util.showToast('多次重试失败，请检查网络连接');
    }
  },
  
  // 点击公告
  onTapAnnouncement: function (e) {
    var id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: '/pages/announcements/announcement-detail/announcement-detail?id=' + id
    });
  },
  
  // 下拉刷新
  onPullDownRefresh: function () {
    this.loadAnnouncements();
    wx.stopPullDownRefresh();
  },

  // 分享功能
  onShareAppMessage: function () {
    // 获取最新的置顶公告或第一条公告的标题
    const announcements = this.data.announcements;
    let shareTitle = '最新公告';
    
    if (announcements && announcements.length > 0) {
      // 查找置顶公告
      const topAnnouncement = announcements.find(a => a.is_top);
      if (topAnnouncement) {
        shareTitle = topAnnouncement.title;
      } else {
        // 如果没有置顶公告，使用第一条公告的标题
        shareTitle = announcements[0].title;
      }
    }
    
    return {
      title: shareTitle,
      path: '/pages/announcements/announcements'
    }
  },
  // 分享到朋友圈
  onShareTimeline: function () {
    return {
      title: '最新公告',
      path: '/pages/announcements/announcements'
    }
  } 
}) 