<?php
// 处理删除操作
if (isset($_POST['delete']) && isset($_POST['id'])) {
    require_once 'includes/db.php';
    $id = intval($_POST['id']);
    $query = "DELETE FROM rules WHERE id = ?";
    $stmt = $mysqli->prepare($query);
    $stmt->bind_param('i', $id);
    $stmt->execute();
    $stmt->close();
    header("Location: rules.php");
    exit;
}

// 加载页面模板和数据库连接
require_once 'includes/header.php';
require_once 'includes/db.php';

// 获取所有规则通知
$query = "SELECT * FROM rules ORDER BY created_at DESC";
$result = $mysqli->query($query);
$rules = [];
if ($result) {
    while ($row = $result->fetch_assoc()) {
        $rules[] = $row;
    }
    $result->free();
}
?>

<div class="card">
    <div class="card-header">
        <h3 class="card-title">规则通知列表</h3>
        <div class="card-actions">
            <a href="rule_edit.php" class="btn btn-primary">
                <i class="fas fa-plus"></i> 添加规则通知
            </a>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table data-table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>标题</th>
                        <th>状态</th>
                        <th>创建时间</th>
                        <th>更新时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($rules as $rule): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($rule['id']); ?></td>
                        <td><?php echo htmlspecialchars($rule['title']); ?></td>
                        <td>
                            <span class="badge <?php echo $rule['status'] ? 'badge-success' : 'badge-secondary'; ?>">
                                <?php echo $rule['status'] ? '已发布' : '草稿'; ?>
                            </span>
                        </td>
                        <td><?php echo date('Y-m-d H:i:s', strtotime($rule['created_at'])); ?></td>
                        <td><?php echo date('Y-m-d H:i:s', strtotime($rule['updated_at'])); ?></td>
                        <td>
                            <div class="btn-group">
                                <a href="rule_edit.php?id=<?php echo $rule['id']; ?>" class="btn btn-sm btn-info">
                                    <i class="fas fa-edit"></i> 编辑
                                </a>
                                <form method="post" style="display: inline;" onsubmit="return confirm('确定要删除这条规则通知吗？');">
                                    <input type="hidden" name="id" value="<?php echo $rule['id']; ?>">
                                    <button type="submit" name="delete" class="btn btn-sm btn-danger">
                                        <i class="fas fa-trash"></i> 删除
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<style>
.btn {
    display: inline-flex;
    align-items: center;
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    line-height: 1.5;
    border-radius: 0.25rem;
    color: #fff;
    text-decoration: none;
    transition: all 0.15s ease-in-out;
    border: none;
    cursor: pointer;
    margin: 0 0.25rem;
}

.btn-primary {
    background-color: var(--primary);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
}

.btn-info {
    background-color: var(--info);
}

.btn-info:hover {
    background-color: #2980b9;
}

.btn-danger {
    background-color: var(--danger);
}

.btn-danger:hover {
    background-color: #c0392b;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

.badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 600;
    border-radius: 0.25rem;
    text-align: center;
}

.badge-success {
    background-color: var(--success);
    color: white;
}

.badge-secondary {
    background-color: var(--secondary);
    color: white;
}

.btn i {
    margin-right: 0.25rem;
}

.table {
    width: 100%;
    margin-bottom: 1rem;
    background-color: transparent;
    border-collapse: collapse;
}

.table th,
.table td {
    padding: 0.75rem;
    vertical-align: middle;
    border-top: 1px solid var(--gray-200);
}

.table thead th {
    vertical-align: bottom;
    border-bottom: 2px solid var(--gray-200);
    background-color: var(--gray-100);
    font-weight: 600;
}

.table tbody tr:hover {
    background-color: var(--gray-100);
}

.table-responsive {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

.btn-group {
    display: flex;
    gap: 0.5rem;
}
</style>

<?php require_once 'includes/footer.php'; ?> 