<?php
require_once 'db.php';

class StatsCalculator {
    private $db;
    
    public function __construct($db) {
        $this->db = $db;
    }
    
    // 计算KD值
    public function calculateKD($kills, $deaths) {
        return $kills / max(1, $deaths);
    }
    
    // 计算胜率
    public function calculateWinRate($wins, $losses) {
        $total = $wins + $losses;
        if ($total == 0) {
            return 0;
        }
        return ($wins / $total) * 100;
    }
    
    // 获取所有玩家的统计数据汇总
    public function getAllPlayerStats() {
        // 从数据库获取所有玩家统计数据
        $sql = "SELECT 
                    p.nickname,
                    SUM(g.kills) as total_kills,
                    SUM(g.deaths) as total_deaths,
                    SUM(g.wins) as total_wins,
                    SUM(g.losses) as total_losses,
                    p.player_rank,
                    gm.game_type
                FROM 
                    players p
                LEFT JOIN 
                    game_records g ON p.id = g.player_id
                LEFT JOIN
                    games gm ON g.game_id = gm.id
                GROUP BY 
                    p.id, p.nickname, p.player_rank, gm.game_type
                ORDER BY 
                    SUM(g.kills) DESC";
                    
        $players = $this->db->getRows($sql);
        
        $results = [];
        foreach ($players as $player) {
            $kd = $this->calculateKD($player['total_kills'], $player['total_deaths']);
            $winRate = $this->calculateWinRate($player['total_wins'], $player['total_losses']);
            
            $results[] = [
                'nickname' => $player['nickname'],
                'total_kills' => $player['total_kills'],
                'total_deaths' => $player['total_deaths'],
                'kd' => number_format($kd, 2),
                'total_wins' => $player['total_wins'],
                'total_losses' => $player['total_losses'],
                'win_rate' => number_format($winRate, 2) . '%',
                'player_rank' => $player['player_rank'],
                'game_mode' => $player['game_type'] ?: '未知'
            ];
        }
        
        return $results;
    }
    
    // 获取特定玩家的统计数据汇总
    public function getPlayerStats($nickname) {
        $nickname = $this->db->escape($nickname);
        
        $sql = "SELECT 
                    p.nickname,
                    SUM(g.kills) as total_kills,
                    SUM(g.deaths) as total_deaths,
                    SUM(g.wins) as total_wins,
                    SUM(g.losses) as total_losses,
                    p.player_rank,
                    gm.game_type
                FROM 
                    players p
                LEFT JOIN 
                    game_records g ON p.id = g.player_id
                LEFT JOIN
                    games gm ON g.game_id = gm.id
                WHERE 
                    p.nickname = '{$nickname}'
                GROUP BY 
                    p.id, p.nickname, p.player_rank, gm.game_type";
                    
        $player = $this->db->getRow($sql);
        
        if (!$player) {
            return null;
        }
        
        $kd = $this->calculateKD($player['total_kills'], $player['total_deaths']);
        $winRate = $this->calculateWinRate($player['total_wins'], $player['total_losses']);
        
        return [
            'nickname' => $player['nickname'],
            'total_kills' => $player['total_kills'],
            'total_deaths' => $player['total_deaths'],
            'kd' => number_format($kd, 2),
            'total_wins' => $player['total_wins'],
            'total_losses' => $player['total_losses'],
            'win_rate' => number_format($winRate, 2) . '%',
            'player_rank' => $player['player_rank'],
            'game_mode' => $player['game_type'] ?: '未知'
        ];
    }
    
    // 按军衔分组获取统计数据
    public function getStatsByRank() {
        $sql = "SELECT 
                    p.player_rank,
                    COUNT(DISTINCT p.id) as player_count,
                    SUM(g.kills) as total_kills,
                    SUM(g.deaths) as total_deaths,
                    SUM(g.wins) as total_wins,
                    SUM(g.losses) as total_losses,
                    gm.game_type
                FROM 
                    players p
                LEFT JOIN 
                    game_records g ON p.id = g.player_id
                LEFT JOIN
                    games gm ON g.game_id = gm.id
                GROUP BY 
                    p.player_rank, gm.game_type
                ORDER BY 
                    player_count DESC";
                    
        $ranks = $this->db->getRows($sql);
        
        $results = [];
        foreach ($ranks as $rank) {
            $kd = $this->calculateKD($rank['total_kills'], $rank['total_deaths']);
            $winRate = $this->calculateWinRate($rank['total_wins'], $rank['total_losses']);
            
            $results[] = [
                'player_rank' => $rank['player_rank'],
                'player_count' => $rank['player_count'],
                'total_kills' => $rank['total_kills'],
                'total_deaths' => $rank['total_deaths'],
                'kd' => number_format($kd, 2),
                'total_wins' => $rank['total_wins'],
                'total_losses' => $rank['total_losses'],
                'win_rate' => number_format($winRate, 2) . '%',
                'game_mode' => $rank['game_type'] ?: '未知'
            ];
        }
        
        return $results;
    }
    
    // 计算游戏数据（从Excel提取的原始数据）
    public function processGameData($gameData, $gameId) {
        // 保存流星队和蝴蝶队的胜场数据
        $teamWins = [
            '流星' => 0,
            '蝴蝶' => 0
        ];
        
        // 首先确定各队的胜场
        foreach ($gameData as $player) {
            if (isset($player['team']) && isset($player['wins']) && isset($teamWins[$player['team']])) {
                // 假设每个队的胜场数是相同的
                $teamWins[$player['team']] = $player['wins'];
            }
        }
        
        // 处理每个玩家的数据
        $processedData = [];
        foreach ($gameData as $player) {
            $nickname = $player['nickname'];
            $team = $player['team'] ?? '';
            $wins = $player['wins'] ?? 0;
            
            // 确定对手队伍的胜场（即当前玩家的败场）
            $losses = 0;
            if ($team == '流星' && isset($teamWins['蝴蝶'])) {
                $losses = $teamWins['蝴蝶'];
            } elseif ($team == '蝴蝶' && isset($teamWins['流星'])) {
                $losses = $teamWins['流星'];
            }
            
            $processedData[] = [
                'player_name' => $nickname,
                'game_id' => $gameId,
                'kills' => $player['kills'] ?? 0,
                'deaths' => $player['deaths'] ?? 0,
                'team' => $team,
                'wins' => $wins,
                'losses' => $losses,
                'virtual_ip' => $player['virtual_ip'] ?? '',
                'player_rank' => $player['player_rank'] ?? ''
            ];
        }
        
        return $processedData;
    }
    
    // 获取特定玩家的游戏场次数量
    public function getPlayerGameCount($nickname) {
        $nickname = $this->db->escape($nickname);
        
        $sql = "SELECT 
                    COUNT(DISTINCT g.excel_file) as excel_count
                FROM 
                    players p
                JOIN 
                    game_records gr ON p.id = gr.player_id
                JOIN
                    games g ON gr.game_id = g.id
                WHERE 
                    p.nickname = '{$nickname}'";
                    
        $result = $this->db->getRow($sql);
        
        return [
            'nickname' => $nickname,
            'excel_count' => $result['excel_count'] ?? 0
        ];
    }
    
    // 获取所有可用的游戏模式
    public function getAvailableGameModes() {
        $sql = "SELECT DISTINCT game_type FROM games WHERE game_type IS NOT NULL AND game_type != ''";
        $modes = $this->db->getRows($sql);
        
        $results = [];
        foreach ($modes as $mode) {
            $results[] = $mode['game_type'];
        }
        
        return $results;
    }
    
    // 按游戏模式获取所有玩家的统计数据汇总
    public function getAllPlayerStatsByGameMode($game_mode) {
        $game_mode = $this->db->escape($game_mode);
        
        // 从数据库获取所有玩家在特定游戏模式下的统计数据
        $sql = "SELECT 
                    p.nickname,
                    SUM(g.kills) as total_kills,
                    SUM(g.deaths) as total_deaths,
                    SUM(g.wins) as total_wins,
                    SUM(g.losses) as total_losses,
                    p.player_rank
                FROM 
                    players p
                LEFT JOIN 
                    game_records g ON p.id = g.player_id
                LEFT JOIN
                    games gm ON g.game_id = gm.id
                WHERE 
                    gm.game_type = '{$game_mode}'
                GROUP BY 
                    p.id, p.nickname, p.player_rank
                ORDER BY 
                    SUM(g.kills) DESC";
                    
        $players = $this->db->getRows($sql);
        
        $results = [];
        foreach ($players as $player) {
            $kd = $this->calculateKD($player['total_kills'], $player['total_deaths']);
            $winRate = $this->calculateWinRate($player['total_wins'], $player['total_losses']);
            
            $results[] = [
                'nickname' => $player['nickname'],
                'total_kills' => $player['total_kills'],
                'total_deaths' => $player['total_deaths'],
                'kd' => number_format($kd, 2),
                'total_wins' => $player['total_wins'],
                'total_losses' => $player['total_losses'],
                'win_rate' => number_format($winRate, 2) . '%',
                'player_rank' => $player['player_rank'],
                'game_mode' => $game_mode
            ];
        }
        
        return $results;
    }
    
    // 按游戏模式和军衔分组获取统计数据
    public function getStatsByRankAndGameMode($game_mode) {
        $game_mode = $this->db->escape($game_mode);
        
        $sql = "SELECT 
                    p.player_rank,
                    COUNT(DISTINCT p.id) as player_count,
                    SUM(g.kills) as total_kills,
                    SUM(g.deaths) as total_deaths,
                    SUM(g.wins) as total_wins,
                    SUM(g.losses) as total_losses
                FROM 
                    players p
                LEFT JOIN 
                    game_records g ON p.id = g.player_id
                LEFT JOIN
                    games gm ON g.game_id = gm.id
                WHERE 
                    gm.game_type = '{$game_mode}'
                GROUP BY 
                    p.player_rank
                ORDER BY 
                    player_count DESC";
                    
        $ranks = $this->db->getRows($sql);
        
        $results = [];
        foreach ($ranks as $rank) {
            $kd = $this->calculateKD($rank['total_kills'], $rank['total_deaths']);
            $winRate = $this->calculateWinRate($rank['total_wins'], $rank['total_losses']);
            
            $results[] = [
                'player_rank' => $rank['player_rank'],
                'player_count' => $rank['player_count'],
                'total_kills' => $rank['total_kills'],
                'total_deaths' => $rank['total_deaths'],
                'kd' => number_format($kd, 2),
                'total_wins' => $rank['total_wins'],
                'total_losses' => $rank['total_losses'],
                'win_rate' => number_format($winRate, 2) . '%',
                'game_mode' => $game_mode
            ];
        }
        
        return $results;
    }
    
    // 导出所有玩家统计数据
    public function exportPlayerStats($format = 'excel') {
        $stats = $this->getAllPlayerStats();
        $prefix = 'player_stats';
        return $this->exportToExcel($stats, $prefix);
    }
    
    // 导出军衔分布统计数据
    public function exportRankStats($format = 'excel') {
        $stats = $this->getStatsByRank();
        $prefix = 'rank_stats';
        return $this->exportToExcel($stats, $prefix);
    }
    
    // 导出为Excel
    private function exportToExcel($data, $prefix = 'stats') {
        require_once 'excel_handler.php';
        $excelHandler = new ExcelHandler();
        $excelHandler->createNew();
        $spreadsheet = $excelHandler->getSpreadsheet();
        
        // 按游戏模式分组数据
        $dataByGameMode = [];
        foreach ($data as $item) {
            $gameMode = $item['game_mode'] ?: '未知';
            if (!isset($dataByGameMode[$gameMode])) {
                $dataByGameMode[$gameMode] = [];
            }
            $dataByGameMode[$gameMode][] = $item;
        }
        
        $sheetIndex = 0;
        
        // 为每个游戏模式创建单独的工作表
        foreach ($dataByGameMode as $gameMode => $modeData) {
            if ($sheetIndex > 0) {
                // 创建新的工作表
                $spreadsheet->createSheet();
                $spreadsheet->setActiveSheetIndex($sheetIndex);
            }
            
            // 设置工作表名称
            $sheetName = str_replace(['[', ']', '*', ':', '/', '\\', '?'], '_', $gameMode);
            $sheetName = substr($sheetName, 0, 31); // Excel限制工作表名最长31字符
            $spreadsheet->getActiveSheet()->setTitle($sheetName);
            
            // 获取当前工作表
            $sheet = $spreadsheet->getActiveSheet();
            
            if ($prefix == 'player_stats') {
                // 设置表头
                $headers = [
                    'A1' => '玩家昵称',
                    'B1' => '总狙杀数',
                    'C1' => '总死亡数',
                    'D1' => 'KD值',
                    'E1' => '总胜场',
                    'F1' => '总败场',
                    'G1' => '胜场率',
                    'H1' => '军衔'
                ];
                
                // 设置列宽
                $sheet->getColumnDimension('A')->setWidth(20);
                $sheet->getColumnDimension('B')->setWidth(12);
                $sheet->getColumnDimension('C')->setWidth(12);
                $sheet->getColumnDimension('D')->setWidth(12);
                $sheet->getColumnDimension('E')->setWidth(12);
                $sheet->getColumnDimension('F')->setWidth(12);
                $sheet->getColumnDimension('G')->setWidth(12);
                $sheet->getColumnDimension('H')->setWidth(15);
                
                // 写入表头
                foreach ($headers as $cell => $value) {
                    $sheet->setCellValue($cell, $value);
                }
                
                // 写入数据
                $row = 2;
                foreach ($modeData as $player) {
                    $sheet->setCellValue('A'.$row, $player['nickname']);
                    $sheet->setCellValue('B'.$row, $player['total_kills']);
                    $sheet->setCellValue('C'.$row, $player['total_deaths']);
                    $sheet->setCellValue('D'.$row, $player['kd']);
                    $sheet->setCellValue('E'.$row, $player['total_wins']);
                    $sheet->setCellValue('F'.$row, $player['total_losses']);
                    $sheet->setCellValue('G'.$row, $player['win_rate']);
                    $sheet->setCellValue('H'.$row, $player['player_rank']);
                    $row++;
                }
                
                // 设置所有单元格的样式
                $styleArray = [
                    'alignment' => [
                        'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
                        'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER,
                    ],
                    'borders' => [
                        'allBorders' => [
                            'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                        ],
                    ],
                ];
                
                $sheet->getStyle('A1:H'.$row)->applyFromArray($styleArray);
                
                // 设置表头样式
                $headerStyle = [
                    'font' => [
                        'bold' => true,
                        'size' => 11,
                    ],
                    'fill' => [
                        'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                        'startColor' => [
                            'rgb' => 'CCCCCC',
                        ],
                    ],
                ];
                
                $sheet->getStyle('A1:H1')->applyFromArray($headerStyle);
            } else {
                // 设置表头
                $headers = [
                    'A1' => '军衔',
                    'B1' => '玩家数量',
                    'C1' => '总狙杀数',
                    'D1' => '总死亡数',
                    'E1' => '平均KD值',
                    'F1' => '总胜场',
                    'G1' => '总败场',
                    'H1' => '胜率'
                ];
                
                // 设置列宽
                $sheet->getColumnDimension('A')->setWidth(15);
                $sheet->getColumnDimension('B')->setWidth(12);
                $sheet->getColumnDimension('C')->setWidth(12);
                $sheet->getColumnDimension('D')->setWidth(12);
                $sheet->getColumnDimension('E')->setWidth(12);
                $sheet->getColumnDimension('F')->setWidth(12);
                $sheet->getColumnDimension('G')->setWidth(12);
                $sheet->getColumnDimension('H')->setWidth(12);
                
                // 写入表头
                foreach ($headers as $cell => $value) {
                    $sheet->setCellValue($cell, $value);
                }
                
                // 写入数据
                $row = 2;
                foreach ($modeData as $rank) {
                    $sheet->setCellValue('A'.$row, $rank['player_rank'] ?: '未知');
                    $sheet->setCellValue('B'.$row, $rank['player_count']);
                    $sheet->setCellValue('C'.$row, $rank['total_kills']);
                    $sheet->setCellValue('D'.$row, $rank['total_deaths']);
                    $sheet->setCellValue('E'.$row, $rank['kd']);
                    $sheet->setCellValue('F'.$row, $rank['total_wins']);
                    $sheet->setCellValue('G'.$row, $rank['total_losses']);
                    $sheet->setCellValue('H'.$row, $rank['win_rate']);
                    $row++;
                }
                
                // 设置所有单元格的样式
                $styleArray = [
                    'alignment' => [
                        'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
                        'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER,
                    ],
                    'borders' => [
                        'allBorders' => [
                            'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                        ],
                    ],
                ];
                
                $sheet->getStyle('A1:H'.$row)->applyFromArray($styleArray);
                
                // 设置表头样式
                $headerStyle = [
                    'font' => [
                        'bold' => true,
                        'size' => 11,
                    ],
                    'fill' => [
                        'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                        'startColor' => [
                            'rgb' => 'CCCCCC',
                        ],
                    ],
                ];
                
                $sheet->getStyle('A1:H1')->applyFromArray($headerStyle);
            }
            
            $sheetIndex++;
        }
        
        // 激活第一个工作表
        $spreadsheet->setActiveSheetIndex(0);
        
        // 生成文件名
        $filename = $prefix . '_' . date('Ymd_His') . '.xlsx';
        $filepath = EXCEL_DIR . $filename;
        
        // 保存文件
        try {
            $writer = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($spreadsheet, 'Xlsx');
            $writer->setIncludeCharts(false);
            $writer->setPreCalculateFormulas(false);
            
            // 确保目录存在且可写
            if (!is_dir(EXCEL_DIR)) {
                mkdir(EXCEL_DIR, 0755, true);
            }
            
            // 设置文件权限
            if (file_exists($filepath)) {
                chmod($filepath, 0644);
                unlink($filepath);
            }
            
            $writer->save($filepath);
            chmod($filepath, 0644);
            
            return $filename;
        } catch (\Exception $e) {
            error_log("Excel导出失败: " . $e->getMessage());
            return false;
        }
    }
}

// 使用示例
// $stats = new StatsCalculator($db);
// $playerStats = $stats->getAllPlayerStats();