<?php
session_start();
require_once 'includes/utils.php';

// 记录登出日志
if (isset($_SESSION['admin_username'])) {
    Utils::logActivity('登出', '管理员 ' . $_SESSION['admin_username'] . ' 登出系统');
}

// 清除所有会话变量
$_SESSION = [];

// 销毁会话
if (ini_get("session.use_cookies")) {
    $params = session_get_cookie_params();
    setcookie(session_name(), '', time() - 42000,
        $params["path"], $params["domain"],
        $params["secure"], $params["httponly"]
    );
}

session_destroy();

// 重定向到登录页面
header('Location: login.php');
exit; 