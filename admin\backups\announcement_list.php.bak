<?php
require_once 'config.php';
require_once 'includes/utils.php';
require_once 'includes/db.php';

// 检查用户是否已登录且为管理员
Utils::checkLogin();

// 分页设置
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$items_per_page = 10;
$offset = ($page - 1) * $items_per_page;

// 处理删除操作
if (isset($_GET['action']) && $_GET['action'] == 'delete' && isset($_GET['id'])) {
    $id = intval($_GET['id']);
    
    // 从数据库中删除公告
    $where = "id = " . $db->escape($id);
    $db->delete('announcements', $where);
    
    // 设置成功消息并重定向
    $_SESSION['success_message'] = "公告已成功删除";
    header("Location: announcement_list.php");
    exit;
}

// 获取公告总数
$row = $db->getRow("SELECT COUNT(*) as total FROM announcements");
$total_items = $row['total'];
$total_pages = ceil($total_items / $items_per_page);

// 获取公告列表
$sql = "SELECT * FROM announcements ORDER BY priority DESC, created_at DESC LIMIT {$items_per_page} OFFSET {$offset}";
$announcements = $db->getRows($sql);

$page_title = "管理公告";
include 'includes/header.php';
?>

<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">管理公告</h1>
        <a href="announcement_edit.php" class="btn btn-primary">
            <i class="fas fa-plus"></i> 添加新公告
        </a>
    </div>

    <?php if (isset($_SESSION['success_message'])): ?>
        <div class="alert alert-success">
            <?php 
                echo $_SESSION['success_message']; 
                unset($_SESSION['success_message']);
            ?>
        </div>
    <?php endif; ?>

    <div class="card">
        <div class="card-body">
            <?php if (count($announcements) > 0): ?>
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="thead-dark">
                            <tr>
                                <th>ID</th>
                                <th>标题</th>
                                <th>状态</th>
                                <th>优先级</th>
                                <th>有效期</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($announcements as $announcement): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($announcement['id']); ?></td>
                                    <td><?php echo htmlspecialchars($announcement['title']); ?></td>
                                    <td>
                                        <?php if ($announcement['status'] == 1): ?>
                                            <span class="badge bg-success">激活</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">草稿</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($announcement['priority'] == 1): ?>
                                            <span class="badge bg-danger">高</span>
                                        <?php else: ?>
                                            <span class="badge bg-info">普通</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php 
                                            echo date('Y-m-d', strtotime($announcement['valid_from'])); 
                                            echo " 至 ";
                                            echo date('Y-m-d', strtotime($announcement['valid_to']));
                                        ?>
                                    </td>
                                    <td><?php echo date('Y-m-d H:i', strtotime($announcement['created_at'])); ?></td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="announcement_edit.php?id=<?php echo $announcement['id']; ?>" class="btn btn-sm btn-primary">
                                                <i class="fas fa-edit"></i> 编辑
                                            </a>
                                            <a href="announcement_list.php?action=delete&id=<?php echo $announcement['id']; ?>" 
                                               class="btn btn-sm btn-danger" 
                                               onclick="return confirm('确定要删除这条公告吗？此操作不可撤销。')">
                                                <i class="fas fa-trash"></i> 删除
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页 -->
                <?php if ($total_pages > 1): ?>
                    <div class="text-center mt-4">
                        <div style="display: flex; justify-content: center; margin-bottom: 10px;">
                            <a href="announcement_list.php?page=1" style="text-decoration: none; color: #6c757d; margin: 0 10px;">首页</a>
                            <a href="announcement_list.php?page=<?php echo max(1, $page - 1); ?>" style="text-decoration: none; color: #6c757d; margin: 0 10px;">上一页</a>
                            <a href="announcement_list.php?page=<?php echo min($total_pages, $page + 1); ?>" style="text-decoration: none; color: #6c757d; margin: 0 10px;">下一页</a>
                            <a href="announcement_list.php?page=<?php echo $total_pages; ?>" style="text-decoration: none; color: #6c757d; margin: 0 10px;">末页</a>
                        </div>
                    </div>
                <?php endif; ?>
            <?php else: ?>
                <div class="alert alert-info">
                    <p class="mb-0">目前没有公告。点击上方的"添加新公告"按钮创建一条公告。</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php
include 'includes/footer.php';
?> 