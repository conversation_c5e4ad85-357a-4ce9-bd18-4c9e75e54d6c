<?php
require_once 'config.php';
require_once 'includes/db.php';
require_once 'includes/utils.php';

// 包含头部
include 'includes/header.php';
?>

<div class="main-content">
    <div class="container">
<?php
// 获取分类列表
$categories_sql = "SELECT * FROM article_categories ORDER BY name ASC";
$categories = $db->getRows($categories_sql);

// 获取分类筛选参数
$category_filter = isset($_GET['category']) ? (int)$_GET['category'] : 0;

// 获取分页参数
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$page = max(1, $page);
$per_page = 10;
$offset = ($page - 1) * $per_page;

// 构建查询条件
$where_clause = "";
if ($category_filter > 0) {
    $where_clause = " WHERE a.category_id = {$category_filter}";
}

// 获取总记录数
$count_sql = "SELECT COUNT(*) as total FROM articles a{$where_clause}";
$result = $db->getRow($count_sql);
$total_records = $result['total'];
$total_pages = ceil($total_records / $per_page);

// 获取文章列表
$sql = "SELECT a.*, c.name as category_name 
        FROM articles a 
        LEFT JOIN article_categories c ON a.category_id = c.id{$where_clause} 
        ORDER BY create_time DESC LIMIT {$offset}, {$per_page}";
$articles = $db->getRows($sql);
?>
<h1 class="page-title">文章管理</h1>

<div class="article-dashboard">
    <div class="card article-card">
        <div class="card-header">
            <div class="card-title">文章列表</div>
            <div class="card-actions">
                <a href="article_edit.php" class="btn btn-primary btn-sm"><i class="fas fa-plus"></i> 新增文章</a>
                <a href="import_export.php?type=article" class="btn btn-secondary btn-sm"><i class="fas fa-file-import"></i> 导入/导出</a>
                <form action="articles.php" method="GET" class="category-filter">
                    <select name="category" id="category" class="form-control form-control-sm" onchange="this.form.submit()">
                        <option value="0"<?php echo $category_filter == 0 ? ' selected' : ''; ?>>所有分类</option>
                        <?php foreach ($categories as $category): ?>
                        <option value="<?php echo $category['id']; ?>"<?php echo $category_filter == $category['id'] ? ' selected' : ''; ?>>
                            <?php echo $category['name']; ?>
                        </option>
                        <?php endforeach; ?>
                    </select>
                </form>
            </div>
        </div>
        
        <?php if (empty($articles)): ?>
            <div class="alert alert-info mt-3">暂无文章</div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-striped table-hover article-table">
                    <thead>
                        <tr>
                            <th width="5%">ID</th>
                            <th width="20%">标题</th>
                            <th width="25%">摘要</th>
                            <th width="10%">分类</th>
                            <th width="8%">状态</th>
                            <th width="12%">更新时间</th>
                            <th width="20%">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($articles as $article): ?>
                            <tr>
                                <td><?php echo $article['id']; ?></td>
                                <td class="article-title"><?php echo $article['title']; ?></td>
                                <td class="article-summary"><?php echo !empty($article['summary']) ? $article['summary'] : '<span class="text-muted">无摘要</span>'; ?></td>
                                <td><span class="badge badge-info"><?php echo $article['category_name'] ?? '默认分类'; ?></span></td>
                                <td>
                                    <?php if ($article['published']): ?>
                                        <span class="badge badge-success">已发布</span>
                                    <?php else: ?>
                                        <span class="badge badge-secondary">草稿</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo date('Y-m-d H:i', strtotime($article['update_time'])); ?></td>
                                <td class="article-actions">
                                    <a href="article_preview.php?id=<?php echo $article['id']; ?>" class="btn btn-info btn-sm" title="预览" target="_blank"><i class="fas fa-eye"></i></a>
                                    <a href="article_edit.php?id=<?php echo $article['id']; ?>" class="btn btn-primary btn-sm" title="编辑"><i class="fas fa-edit"></i></a>
                                    <a href="javascript:void(0);" onclick="confirmDelete('确定要删除此文章吗？', function() { window.location.href='delete.php?id=<?php echo $article['id']; ?>&type=article'; })" class="btn btn-danger btn-sm" title="删除"><i class="fas fa-trash"></i></a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <?php if ($total_pages > 1): ?>
                <div class="pagination-container">
                    <ul class="pagination">
                        <li class="page-item <?php echo $page <= 1 ? 'disabled' : ''; ?>">
                            <a class="page-link" href="articles.php?page=1<?php echo $category_filter > 0 ? '&category='.$category_filter : ''; ?>">首页</a>
                        </li>
                        <li class="page-item <?php echo $page <= 1 ? 'disabled' : ''; ?>">
                            <a class="page-link" href="articles.php?page=<?php echo max(1, $page - 1); ?><?php echo $category_filter > 0 ? '&category='.$category_filter : ''; ?>">上一页</a>
                        </li>
                        <li class="page-info">
                            <span class="page-text"><?php echo $page; ?>/<?php echo $total_pages; ?></span>
                        </li>
                        <li class="page-item <?php echo $page >= $total_pages ? 'disabled' : ''; ?>">
                            <a class="page-link" href="articles.php?page=<?php echo min($total_pages, $page + 1); ?><?php echo $category_filter > 0 ? '&category='.$category_filter : ''; ?>">下一页</a>
                        </li>
                        <li class="page-item <?php echo $page >= $total_pages ? 'disabled' : ''; ?>">
                            <a class="page-link" href="articles.php?page=<?php echo $total_pages; ?><?php echo $category_filter > 0 ? '&category='.$category_filter : ''; ?>">末页</a>
                        </li>
                    </ul>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>

    <div class="card help-card">
        <div class="card-header">
            <div class="card-title">文章管理说明</div>
            <div class="toggle-help"><i class="fas fa-chevron-down"></i></div>
        </div>
        <div class="help-content">
            <div class="help-grid">
                <div class="help-item">
                    <div class="help-icon"><i class="fas fa-plus-circle"></i></div>
                    <div class="help-text">
                        <h4>新增文章</h4>
                        <p>创建新的文章内容</p>
                    </div>
                </div>
                <div class="help-item">
                    <div class="help-icon"><i class="fas fa-edit"></i></div>
                    <div class="help-text">
                        <h4>编辑</h4>
                        <p>修改已有文章内容和状态</p>
                    </div>
                </div>
                <div class="help-item">
                    <div class="help-icon"><i class="fas fa-eye"></i></div>
                    <div class="help-text">
                        <h4>预览</h4>
                        <p>在新窗口中预览文章</p>
                    </div>
                </div>
                <div class="help-item">
                    <div class="help-icon"><i class="fas fa-trash"></i></div>
                    <div class="help-text">
                        <h4>删除</h4>
                        <p>永久删除文章（不可恢复）</p>
                    </div>
                </div>
                <div class="help-item">
                    <div class="help-icon"><i class="fas fa-file-import"></i></div>
                    <div class="help-text">
                        <h4>导入/导出</h4>
                        <p>批量导入或导出文章数据</p>
                    </div>
                </div>
                <div class="help-item">
                    <div class="help-icon"><i class="fas fa-filter"></i></div>
                    <div class="help-text">
                        <h4>分类筛选</h4>
                        <p>按分类查看文章</p>
                    </div>
                </div>
            </div>
            <div class="help-footer">
                <p>文章支持Markdown格式，可添加格式化文本、链接和图片等。新增摘要功能，未填写时自动使用文章前30个字作为摘要。</p>
            </div>
        </div>
    </div>
</div>

<style>
/* 文章管理页面专用样式 */
.article-dashboard {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.article-card, .help-card {
    margin-bottom: 0;
    box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
    border-radius: 4px;
    overflow: hidden;
    padding: 0;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.card-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #495057;
}

.card-actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

.category-filter {
    margin-left: 10px;
    min-width: 150px;
}

.article-table {
    margin-bottom: 0;
}

.article-table th {
    font-size: 13px;
    font-weight: 600;
    padding: 12px 15px;
    background-color: #f1f3f5;
    border-top: none;
    white-space: nowrap;
}

.article-table td {
    padding: 10px 15px;
    vertical-align: middle;
    font-size: 14px;
}

.article-title {
    font-weight: 500;
}

.article-summary {
    color: #6c757d;
    max-width: 250px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.article-actions {
    display: flex;
    gap: 5px;
}

.badge {
    padding: 5px 10px;
    border-radius: 3px;
    font-weight: 500;
    font-size: 12px;
}

.badge-info {
    background-color: #17a2b8;
    color: white;
}

.pagination-container {
    padding: 15px;
    display: flex;
    justify-content: center;
    border-top: 1px solid #e9ecef;
}

.pagination {
    display: flex;
    gap: 5px;
    list-style: none;
    padding: 0;
    margin: 0;
}

.page-item {
    display: inline-block;
}

.page-link {
    display: block;
    padding: 6px 12px;
    border-radius: 3px;
    text-decoration: none;
    color: #007bff;
    background-color: #fff;
    border: 1px solid #dee2e6;
    transition: all 0.2s;
}

.page-link:hover {
    color: #0056b3;
    background-color: #e9ecef;
    border-color: #dee2e6;
}

.page-item.disabled .page-link {
    color: #6c757d;
    pointer-events: none;
    cursor: not-allowed;
    background-color: #fff;
    border-color: #dee2e6;
}

.page-info {
    display: flex;
    align-items: center;
    margin: 0 8px;
}

.page-text {
    padding: 6px 12px;
    border-radius: 3px;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    color: #495057;
    font-size: 14px;
}

.help-card {
    background-color: white;
}

.toggle-help {
    cursor: pointer;
    color: #6c757d;
    transition: all 0.3s;
}

.toggle-help:hover {
    color: #007bff;
}

.help-content {
    padding: 15px 20px;
}

.help-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
    margin-bottom: 15px;
}

.help-item {
    display: flex;
    gap: 12px;
    align-items: center;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 4px;
    transition: all 0.2s;
}

.help-item:hover {
    background-color: #e9ecef;
    transform: translateY(-2px);
}

.help-icon {
    font-size: 20px;
    color: #007bff;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: rgba(0, 123, 255, 0.1);
}

.help-text h4 {
    margin: 0 0 5px 0;
    font-size: 14px;
    font-weight: 600;
}

.help-text p {
    margin: 0;
    font-size: 12px;
    color: #6c757d;
}

.help-footer {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
}

.help-footer p {
    margin: 0;
    font-size: 14px;
    color: #495057;
}

@media (max-width: 768px) {
    .help-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .card-actions {
        flex-wrap: wrap;
    }
    
    .article-summary {
        max-width: 150px;
    }
}

@media (max-width: 576px) {
    .help-grid {
        grid-template-columns: 1fr;
    }
    
    .article-table th:nth-child(3),
    .article-table td:nth-child(3),
    .article-table th:nth-child(4),
    .article-table td:nth-child(4),
    .article-table th:nth-child(6),
    .article-table td:nth-child(6) {
        display: none;
    }
    
    .card-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .card-actions {
        margin-top: 10px;
        width: 100%;
    }
    
    .category-filter {
        margin-left: 0;
        margin-top: 10px;
        width: 100%;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 帮助卡片切换
    const toggleHelp = document.querySelector('.toggle-help');
    const helpContent = document.querySelector('.help-content');
    
    toggleHelp.addEventListener('click', function() {
        helpContent.style.display = helpContent.style.display === 'none' ? 'block' : 'none';
        toggleHelp.querySelector('i').classList.toggle('fa-chevron-down');
        toggleHelp.querySelector('i').classList.toggle('fa-chevron-up');
    });
    
    // 默认展开帮助
    helpContent.style.display = 'block';
});
</script>

    </div><!-- 结束 .container -->
</div><!-- 结束 .main-content -->

<?php
// 包含底部
include 'includes/footer.php';
?> 