# 导入导出功能使用说明

## 导入功能

### 支持的数据类型
- **文章**：可批量导入多篇文章
- **公告**：可批量导入多条公告

### 导入格式要求
所有导入必须使用TXT文本文件，并遵循以下格式规范：

#### 文章导入格式
```
标题: 文章标题1
内容:
这里是文章正文内容，可以包含多行文本。
支持Markdown格式。

可以包含多个段落。

标题: 文章标题2
内容:
这是第二篇文章的内容。
可以导入多篇文章。
```

#### 公告导入格式
```
标题: 公告标题1
优先级: 高
内容:
这里是公告内容，可以包含多行文本。
支持Markdown格式。

可以包含多个段落。

标题: 公告标题2
优先级: 普通
内容:
这是第二条公告的内容。
可以导入多条公告。
```

### 导入注意事项
1. 导入的文章默认状态为"草稿"
2. 导入的公告默认状态为"草稿"
3. 公告优先级只能为"高"或"普通"
4. 批量导入时，各个条目之间必须使用正确格式分隔

## 导出功能

### 支持的数据类型
- **文章**：可导出全部文章或单篇文章
- **公告**：可导出全部公告或单条公告

### 导出格式选项
- **TXT文本**：纯文本格式，适合简单阅读和解析
- **Markdown**：支持格式化的文本，适合在支持Markdown的编辑器中查看
- **HTML**：格式化的网页文档，适合在浏览器中直接查看

### 导出方式
所有导出内容将以ZIP压缩包形式下载，文件名包含导出时间戳以便区分。

### 导出步骤
1. 选择要导出的数据类型（文章或公告）
2. 选择要导出的具体项目（全部或单个项目）
3. 选择导出格式（TXT、Markdown或HTML）
4. 点击相应按钮进行导出
5. 浏览器将自动下载ZIP压缩包

## 技术说明

### 导入流程
1. 上传TXT文件
2. 系统解析文件内容，提取各个条目
3. 将解析后的数据批量插入数据库
4. 返回导入结果和统计信息

### 导出流程
1. 根据选择的数据类型和具体项目从数据库获取数据
2. 按照选择的格式生成文件内容
3. 创建临时文件和ZIP压缩包
4. 提供ZIP下载并自动清理临时文件 