<?php
require_once '../config.php';
require_once '../includes/db.php';
require_once '../includes/utils.php';

// 检查是否是AJAX请求或API请求
Utils::checkLoginAjax();

// 设置响应头
header('Content-Type: application/json');

// 获取游戏类型统计数据
$sql = "SELECT 
            gm.game_type,
            COUNT(DISTINCT gm.id) as game_count,
            COUNT(DISTINCT gr.player_id) as player_count,
            SUM(gr.kills) as total_kills,
            SUM(gr.deaths) as total_deaths,
            SUM(gr.wins) as total_wins,
            SUM(gr.losses) as total_losses
        FROM 
            games gm
        LEFT JOIN 
            game_records gr ON gm.id = gr.game_id
        WHERE 
            gm.game_type IS NOT NULL AND gm.game_type != ''
        GROUP BY 
            gm.game_type";

$stats = $db->getRows($sql);

// 计算KD和胜率
foreach ($stats as &$type) {
    $type['kd'] = ($type['total_deaths'] > 0) ? 
                  number_format($type['total_kills'] / $type['total_deaths'], 2) : 
                  number_format($type['total_kills'], 2);
                  
    $total_games = $type['total_wins'] + $type['total_losses'];
    $type['win_rate'] = ($total_games > 0) ? 
                       number_format(($type['total_wins'] / $total_games) * 100, 2) . '%' : 
                       '0.00%';
                       
    // 计算平均每场击杀和死亡
    $type['avg_kills_per_game'] = ($type['game_count'] > 0) ?
                                 number_format($type['total_kills'] / $type['game_count'], 2) :
                                 '0.00';
                                 
    $type['avg_deaths_per_game'] = ($type['game_count'] > 0) ?
                                  number_format($type['total_deaths'] / $type['game_count'], 2) :
                                  '0.00';
                                  
    // 计算平均每名玩家击杀和死亡
    $type['avg_kills_per_player'] = ($type['player_count'] > 0) ?
                                   number_format($type['total_kills'] / $type['player_count'], 2) :
                                   '0.00';
                                   
    $type['avg_deaths_per_player'] = ($type['player_count'] > 0) ?
                                    number_format($type['total_deaths'] / $type['player_count'], 2) :
                                    '0.00';
}

// 获取总体汇总数据
$summary_sql = "SELECT 
                    COUNT(DISTINCT gm.id) as total_games,
                    COUNT(DISTINCT gr.player_id) as total_players,
                    SUM(gr.kills) as total_kills,
                    SUM(gr.deaths) as total_deaths,
                    SUM(gr.wins) as total_wins,
                    SUM(gr.losses) as total_losses
                FROM 
                    games gm
                LEFT JOIN 
                    game_records gr ON gm.id = gr.game_id";

$summary = $db->getRow($summary_sql);

if ($summary) {
    $summary['kd'] = ($summary['total_deaths'] > 0) ? 
                    number_format($summary['total_kills'] / $summary['total_deaths'], 2) : 
                    number_format($summary['total_kills'], 2);
                    
    $total_games = $summary['total_wins'] + $summary['total_losses'];
    $summary['win_rate'] = ($total_games > 0) ? 
                         number_format(($summary['total_wins'] / $total_games) * 100, 2) . '%' : 
                         '0.00%';
                         
    // 计算平均每场击杀和死亡
    $summary['avg_kills_per_game'] = ($summary['total_games'] > 0) ?
                                   number_format($summary['total_kills'] / $summary['total_games'], 2) :
                                   '0.00';
                                   
    $summary['avg_deaths_per_game'] = ($summary['total_games'] > 0) ?
                                    number_format($summary['total_deaths'] / $summary['total_games'], 2) :
                                    '0.00';
                                    
    // 计算平均每名玩家击杀和死亡
    $summary['avg_kills_per_player'] = ($summary['total_players'] > 0) ?
                                     number_format($summary['total_kills'] / $summary['total_players'], 2) :
                                     '0.00';
                                     
    $summary['avg_deaths_per_player'] = ($summary['total_players'] > 0) ?
                                      number_format($summary['total_deaths'] / $summary['total_players'], 2) :
                                      '0.00';
}

// 构建响应
$response = [
    'status' => 'success',
    'message' => '获取游戏类型汇总统计成功',
    'data' => [
        'game_types' => $stats,
        'summary' => $summary
    ]
];

echo json_encode($response, JSON_UNESCAPED_UNICODE);