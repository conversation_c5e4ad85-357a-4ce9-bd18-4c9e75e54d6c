<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>移动端文章列表测试</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <style>
        body {
            font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f7fa;
        }
        
        .test-container {
            max-width: 100%;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .test-header {
            background: #4361ee;
            color: white;
            padding: 15px 20px;
            text-align: center;
        }
        
        .device-info {
            padding: 15px 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            font-size: 14px;
        }
        
        .table-responsive {
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }
        
        .article-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            margin: 0;
            table-layout: fixed;
        }
        
        .article-table th,
        .article-table td {
            padding: 12px 8px;
            border-bottom: 1px solid #e9ecef;
            text-align: left;
            vertical-align: middle;
        }
        
        .article-table th {
            background-color: #f1f3f5;
            font-weight: 600;
            font-size: 13px;
        }
        
        .article-title {
            font-weight: 500;
            color: #333;
            word-break: break-word;
        }
        
        .badge {
            padding: 4px 8px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: 500;
        }
        
        .badge-info {
            background-color: #17a2b8;
            color: white;
        }
        
        .badge-success {
            background-color: #28a745;
            color: white;
        }
        
        .btn {
            padding: 4px 8px;
            border: none;
            border-radius: 3px;
            font-size: 12px;
            cursor: pointer;
            margin: 1px;
        }
        
        .btn-info { background-color: #17a2b8; color: white; }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-danger { background-color: #dc3545; color: white; }
        
        /* 移动端样式 */
        @media (max-width: 768px) {
            .article-table {
                font-size: 13px;
            }
            
            .article-table th,
            .article-table td {
                padding: 8px 4px;
            }
        }
        
        @media (max-width: 576px) {
            /* 移动端隐藏摘要列和状态列，保留标题列 */
            .article-table th:nth-child(4),
            .article-table td:nth-child(4),
            .article-table th:nth-child(6),
            .article-table td:nth-child(6) {
                display: none !important;
            }

            /* 重新分配列宽度，确保充分利用空间 */
            .article-table th:nth-child(1),
            .article-table td:nth-child(1) {
                width: 6% !important;
                min-width: 30px;
                padding: 8px 2px !important;
            }

            .article-table th:nth-child(2),
            .article-table td:nth-child(2) {
                width: 8% !important;
                min-width: 35px;
                font-size: 12px;
                padding: 8px 3px !important;
            }

            /* 标题列 - 充分利用可用空间 */
            .article-table th:nth-child(3),
            .article-table td:nth-child(3) {
                width: 45% !important;
                max-width: none !important;
                min-width: 120px;
                padding: 8px 6px !important;
            }

            .article-table th:nth-child(5),
            .article-table td:nth-child(5) {
                width: 12% !important;
                min-width: 50px;
                padding: 8px 3px !important;
            }

            .article-table th:nth-child(7),
            .article-table td:nth-child(7) {
                width: 14% !important;
                min-width: 60px;
                font-size: 11px;
                padding: 8px 3px !important;
            }

            .article-table th:nth-child(8),
            .article-table td:nth-child(8) {
                width: 15% !important;
                min-width: 65px;
                padding: 8px 2px !important;
            }

            /* 优化标题显示 - 移除限制，允许完整显示 */
            .article-title {
                font-size: 13px !important;
                line-height: 1.3 !important;
                word-break: break-word;
                word-wrap: break-word;
                white-space: normal !important;
                overflow: visible !important;
                text-overflow: unset !important;
                display: block !important;
                -webkit-line-clamp: unset !important;
                -webkit-box-orient: unset !important;
                max-height: none !important;
                padding: 2px 0;
            }

            /* 确保标题容器能够自适应高度 */
            .article-table td:nth-child(3) {
                vertical-align: top !important;
                height: auto !important;
                min-height: 40px;
            }
        }
        
        @media (max-width: 320px) {
            .article-table th:nth-child(2),
            .article-table td:nth-child(2) {
                display: none;
            }
            
            .article-table th:nth-child(3),
            .article-table td:nth-child(3) {
                width: 50% !important;
                max-width: none;
            }
            
            .article-table th:nth-child(7),
            .article-table td:nth-child(7) {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h2>移动端文章列表显示测试</h2>
        </div>
        
        <div class="device-info">
            <strong>设备信息：</strong>
            <span id="device-info"></span>
        </div>
        
        <div class="table-responsive">
            <table class="article-table">
                <thead>
                    <tr>
                        <th width="3%">
                            <input type="checkbox">
                        </th>
                        <th width="5%">ID</th>
                        <th width="20%">标题</th>
                        <th width="22%">摘要</th>
                        <th width="10%">分类</th>
                        <th width="8%">状态</th>
                        <th width="12%">更新时间</th>
                        <th width="20%">操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><input type="checkbox"></td>
                        <td>51</td>
                        <td class="article-title">视频测试 - 这是一个很长的文章标题用来测试移动端显示效果和文字换行处理</td>
                        <td>视频测试摘要内容</td>
                        <td><span class="badge badge-info">默认分类</span></td>
                        <td><span class="badge badge-success">已发布</span></td>
                        <td>2025-07-14 21:27</td>
                        <td>
                            <button class="btn btn-info" title="预览"><i class="fas fa-eye"></i></button>
                            <button class="btn btn-primary" title="编辑"><i class="fas fa-edit"></i></button>
                            <button class="btn btn-danger" title="删除"><i class="fas fa-trash"></i></button>
                        </td>
                    </tr>
                    <tr>
                        <td><input type="checkbox"></td>
                        <td>49</td>
                        <td class="article-title">关于win11系统无法正常运行流星蝴蝶剑的详细解决方案和技术分析报告</td>
                        <td>关于win11系统无法正常运行流星蝴蝶剑的详细解决方案...</td>
                        <td><span class="badge badge-info">流星资料</span></td>
                        <td><span class="badge badge-success">已发布</span></td>
                        <td>2025-07-14 21:27</td>
                        <td>
                            <button class="btn btn-info" title="预览"><i class="fas fa-eye"></i></button>
                            <button class="btn btn-primary" title="编辑"><i class="fas fa-edit"></i></button>
                            <button class="btn btn-danger" title="删除"><i class="fas fa-trash"></i></button>
                        </td>
                    </tr>
                    <tr>
                        <td><input type="checkbox"></td>
                        <td>48</td>
                        <td class="article-title">关于互动免费大厅游戏的一些设置</td>
                        <td>这是测试摘要</td>
                        <td><span class="badge badge-info">流星资料</span></td>
                        <td><span class="badge badge-success">已发布</span></td>
                        <td>2025-07-14 21:27</td>
                        <td>
                            <button class="btn btn-info" title="预览"><i class="fas fa-eye"></i></button>
                            <button class="btn btn-primary" title="编辑"><i class="fas fa-edit"></i></button>
                            <button class="btn btn-danger" title="删除"><i class="fas fa-trash"></i></button>
                        </td>
                    </tr>
                    <tr>
                        <td><input type="checkbox"></td>
                        <td>47</td>
                        <td class="article-title">卜算子·咏梅</td>
                        <td>六尺锦金刀，一副...</td>
                        <td><span class="badge badge-info">流星资料</span></td>
                        <td><span class="badge badge-success">已发布</span></td>
                        <td>2025-07-14 21:27</td>
                        <td>
                            <button class="btn btn-info" title="预览"><i class="fas fa-eye"></i></button>
                            <button class="btn btn-primary" title="编辑"><i class="fas fa-edit"></i></button>
                            <button class="btn btn-danger" title="删除"><i class="fas fa-trash"></i></button>
                        </td>
                    </tr>
                    <tr>
                        <td><input type="checkbox"></td>
                        <td>1</td>
                        <td class="article-title">就技术而言,舍道总结出流星三好玩系.(另附网招详细论述)</td>
                        <td>无摘要</td>
                        <td><span class="badge badge-info">流星资料</span></td>
                        <td><span class="badge badge-success">已发布</span></td>
                        <td>2025-07-14 21:27</td>
                        <td>
                            <button class="btn btn-info" title="预览"><i class="fas fa-eye"></i></button>
                            <button class="btn btn-primary" title="编辑"><i class="fas fa-edit"></i></button>
                            <button class="btn btn-danger" title="删除"><i class="fas fa-trash"></i></button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <script>
        // 显示设备信息
        function updateDeviceInfo() {
            const width = window.innerWidth;
            const height = window.innerHeight;
            const userAgent = navigator.userAgent;
            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
            
            document.getElementById('device-info').innerHTML = 
                `屏幕宽度: ${width}px, 高度: ${height}px, 移动设备: ${isMobile ? '是' : '否'}`;
        }
        
        updateDeviceInfo();
        window.addEventListener('resize', updateDeviceInfo);
        window.addEventListener('orientationchange', function() {
            setTimeout(updateDeviceInfo, 100);
        });
    </script>
</body>
</html>
