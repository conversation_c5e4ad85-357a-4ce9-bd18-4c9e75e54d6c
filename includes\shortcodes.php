<?php
/**
 * 短代码处理文件
 * 支持在内容中使用短代码，格式为[shortcode param="value"]content[/shortcode]
 */

// 确保formatting.php已加载
require_once __DIR__ . '/formatting.php';

/**
 * 注册的短代码列表
 */
$shortcodes = [];

/**
 * 注册短代码
 *
 * @param string $tag 短代码名称
 * @param callable $callback 处理短代码的回调函数
 * @return void
 */
function add_shortcode($tag, $callback) {
    global $shortcodes;
    $shortcodes[$tag] = $callback;
}

/**
 * 处理内容中的短代码
 *
 * @param string $content 包含短代码的内容
 * @return string 处理后的内容
 */
function do_shortcode($content) {
    // 直接调用formatting.php中的parse_shortcodes函数处理短代码
    return parse_shortcodes($content);
}

/**
 * 解析短代码属性
 *
 * @param string $text 短代码属性文本
 * @return array 解析后的属性数组
 */
function parse_shortcode_atts($text) {
    $atts = [];
    $pattern = '/([a-zA-Z0-9_-]+)=[\'"]([^\'"]*)[\'"](\s|$)/';
    
    if (preg_match_all($pattern, $text, $matches, PREG_SET_ORDER)) {
        foreach ($matches as $match) {
            $atts[$match[1]] = $match[2];
        }
    }
    
    return $atts;
}

/**
 * 注册基本短代码
 */

// 提示框短代码
add_shortcode('alert', function($atts, $content, $tag) {
    $type = isset($atts['type']) ? $atts['type'] : 'info';
    $allowed_types = ['info', 'success', 'warning', 'danger', 'primary', 'secondary'];
    
    if (!in_array($type, $allowed_types)) {
        $type = 'info';
    }
    
    return sprintf(
        '<div class="alert alert-%s">%s</div>',
        $type,
        $content
    );
});

// 按钮短代码
add_shortcode('button', function($atts, $content, $tag) {
    $type = isset($atts['type']) ? $atts['type'] : 'primary';
    $allowed_types = ['primary', 'secondary', 'success', 'danger', 'warning', 'info', 'light', 'dark'];
    
    if (!in_array($type, $allowed_types)) {
        $type = 'primary';
    }
    
    $url = isset($atts['url']) ? $atts['url'] : '#';
    $size = isset($atts['size']) ? $atts['size'] : '';
    $size_class = in_array($size, ['sm', 'lg']) ? " btn-$size" : '';
    
    return sprintf(
        '<a href="%s" class="btn btn-%s%s">%s</a>',
        htmlspecialchars($url),
        $type,
        $size_class,
        $content
    );
});

// 代码高亮短代码
add_shortcode('code', function($atts, $content, $tag) {
    $language = isset($atts['lang']) ? $atts['lang'] : '';
    $class = !empty($language) ? " class=\"language-$language\"" : '';
    
    return sprintf(
        '<pre><code%s>%s</code></pre>',
        $class,
        htmlspecialchars($content)
    );
});

// 表格短代码
add_shortcode('table', function($atts, $content, $tag) {
    $class = isset($atts['class']) ? $atts['class'] : 'table';
    
    return sprintf(
        '<div class="table-responsive"><table class="%s">%s</table></div>',
        $class,
        $content
    );
});

// 引用短代码
add_shortcode('quote', function($atts, $content, $tag) {
    $author = isset($atts['author']) ? $atts['author'] : '';
    $footer = !empty($author) ? "<footer class=\"blockquote-footer\">$author</footer>" : '';
    
    return sprintf(
        '<blockquote class="blockquote"><p>%s</p>%s</blockquote>',
        $content,
        $footer
    );
});

// 卡片短代码
add_shortcode('card', function($atts, $content, $tag) {
    $title = isset($atts['title']) ? $atts['title'] : '';
    $header = !empty($title) ? "<div class=\"card-header\">$title</div>" : '';
    
    return sprintf(
        '<div class="card mb-3">%s<div class="card-body">%s</div></div>',
        $header,
        $content
    );
});

// 图像短代码
add_shortcode('image', function($atts, $content, $tag) {
    if (!isset($atts['src'])) {
        return '';
    }
    
    $src = $atts['src'];
    $alt = isset($atts['alt']) ? $atts['alt'] : '';
    $class = isset($atts['class']) ? $atts['class'] : 'img-fluid';
    
    return sprintf(
        '<img src="%s" alt="%s" class="%s">',
        htmlspecialchars($src),
        htmlspecialchars($alt),
        $class
    );
}); 