<view class="container">
  <!-- 加载中 -->
  <view class="loading" wx:if="{{loading}}">
    <text>加载中...</text>
  </view>

  <block wx:if="{{!loading}}">
    <!-- 作弊封禁遮罩层 -->
    <view class="cheat-ban{{player.ban_type === 'violation' ? ' violation' : ''}}" wx:if="{{player.is_banned}}">
      <image 
        class="ban-icon" 
        src="{{player.ban_type === 'cheat' ? 'https://img1.lxbl.online/zuobifengjin.png' : 'https://img1.lxbl.online/weiguifengjin2.png'}}" 
        mode="aspectFit">
      </image>
      <view class="ban-text">
        {{player.ban_type === 'cheat' ? '作弊封禁' : '违规封禁'}}
      </view>
    </view>
    
    <!-- 玩家基本信息 -->
    <view class="player-header">
      <image class="player-avatar" src="{{player.avatar || '/images/default-avatar.png'}}"></image>
      <view class="player-basic">
        <view class="player-name"> {{player.nickname}} </view>
        <view class="player-rank">互动军衔： <text class="vip-tag" wx:if="{{player.player_rank}}">{{player.player_rank}}</text><text wx:else>未知</text></view>
      </view>
    </view>
    
    <!-- 分段控件 -->
    <view class="tabs">
      <view 
        wx:for="{{tabs}}" 
        wx:key="index" 
        class="tab-item {{currentTab === index ? 'active' : ''}}" 
        bindtap="switchTab" 
        data-index="{{index}}"
      >
        {{item}}
      </view>
    </view>
    
    <!-- 内容区域 -->
    <view class="content-box">
      <!-- 总览 -->
      <view class="tab-content" hidden="{{currentTab !== 0}}">
        <view class="overview-card">
          <view class="card-title">战绩概览</view>
          <view class="stat-row">
            <view class="stat-item">
              <view class="stat-value">{{player.total_kills || 0}}</view>
              <view class="stat-label">总击杀</view>
            </view>
            <view class="stat-item">
              <view class="stat-value">{{player.total_deaths || '0%'}}</view>
              <view class="stat-label">总死亡</view>
            </view>
            <view class="stat-item">
              <view class="stat-value">{{player.kd || '0'}}</view>
              <view class="stat-label">KDA</view>
            </view>
             <view class="stat-item">
              <view class="stat-value">{{player.excel_count || '0'}}</view>
              <view class="stat-label">游戏场次</view>
            </view>


          </view>
        </view>


 <view class="overview-card">
          <view class="card-title">战绩统计</view>
          <view class="stat-row">
            <view class="stat-item">
              <view class="stat-value">{{player.total_wins || 0}}</view>
              <view class="stat-label">胜场</view>
            </view>
            <view class="stat-item">
              <view class="stat-value">{{player.total_losses || '0%'}}</view>
              <view class="stat-label">败场</view>
            </view>
            <view class="stat-item">
              <view class="stat-value">{{player.win_rate || '0'}}</view>
              <view class="stat-label">胜场率</view>
            </view>
             
          </view>
        </view>

        
        <!-- 玩家数据面板 -->
        <view class="overview-card">
          <view class="card-title">玩家卡片数据分析</view>
          <view class="data-panel">
            <view class="data-item">
              <view class="data-label">
                <text>Impact 影响力</text>
                <text class="data-value">{{player.impact || '0'}}</text>
              </view>
              <view class="data-bar-container">
                <view class="data-bar-baseline"></view>
                <view class="data-bar" style="width: {{player.impact * 50}}%; {{player.impact >= 1 ? 'background-color: #4CAF50;' : 'background-color: #FF5252;'}}"></view>
              </view>
            </view>
            
            <view class="data-item">
              <view class="data-label">
                <text>KPR 平均每局击杀（数值越高越好）</text>
                <text class="data-value">{{player.kpr || '0'}}</text>
              </view>
              <view class="data-bar-container">
                <view class="data-bar-baseline"></view>
                <view class="data-bar" style="width: {{player.kpr * 100}}%; {{player.kpr >= 0.5 ? 'background-color: #4CAF50;' : 'background-color: #FF5252;'}}"></view>
              </view>
            </view>
            
            <view class="data-item">
              <view class="data-label">
                <text>DPR 平均每局死亡（数值越小越好）</text>
                <text class="data-value">{{player.dpr || '0'}}</text>
              </view>
              <view class="data-bar-container">
                <view class="data-bar-baseline"></view>
                <view class="data-bar" style="width: {{player.dpr * 100}}%; {{player.dpr >= 0.6 ? 'background-color: #FF5252;' : 'background-color: #4CAF50;'}}"></view>
              </view>
            </view>
          </view>
          <view class="show-tip" bindtap="toggleTip">
            <text>{{showTip ? '收起说明 ↑' : '查看数据指标说明 ↓'}}</text>
          </view>
          <view class="tip-box" wx:if="{{showTip}}">
            <view class="tip-title">数据指标说明</view>
            <view class="tip-content">
              Impact指标反映了该玩家相对于整体玩家影响力的表现。
              <view class="tip-item">等于1：表示玩家影响力处于上游水平</view>
              <view class="tip-item">大于1：表示玩家影响力处于中高上游水平</view>
              <view class="tip-item">小于1：表示玩家影响力处于偏下游水平</view>
              <view class="tip-highlight">Impact值越大，影响力表现极为出色。</view>
            </view>
          </view>
        </view>

        
      </view>
      
      <!-- 对战历史数据 -->
      <view class="tab-content" hidden="{{currentTab !== 1}}">
        <view class="hero-card">
          <view class="card-title">游戏场次</view>
          
          <!-- 游戏场次列表表格 -->
          <view class="game-history-table">
            <!-- 表头 -->
            <view class="table-header">
              <view class="th">更新时间</view>
              <view class="th">队伍</view>
              <view class="th">击杀</view>
              <view class="th">死亡</view>
              <view class="th">胜场</view>
              <view class="th">败场</view>
            </view>
            
            <!-- 表格内容 -->
            <block wx:if="{{playerGames && playerGames.length > 0}}">
              <view class="table-row" wx:for="{{playerGames}}" wx:key="index" bindtap="previewGameImage" 
                data-game-id="{{item.game_id || item.id}}" data-nickname="{{player.nickname}}" data-index="{{index}}">
                <view class="td">{{item.game_time || '未知'}}</view> 
                <view class="td">{{item.team || '未知'}}</view>
                <view class="td">{{item.kills || '0'}}</view>
                <view class="td">{{item.deaths || '0'}}</view>
                <view class="td">{{item.wins || '0'}}</view>
                <view class="td">{{item.losses || '0'}}</view>
              </view>
            </block>
            
            <!-- 无数据提示 -->
            <view class="no-data" wx:if="{{!playerGames || playerGames.length === 0}}">
              <text>暂无游戏记录</text>
            </view>
          </view>
          <view class="card-title">游戏时间见战绩截图水印</view>
        </view>
      </view>
      
      
    </view>
  </block>
</view>

<!-- 图片预览弹窗 -->
<view class="image-preview-modal" wx:if="{{showImagePreview}}" bindtap="closeImagePreview">
  <view class="preview-content" catchtap="stopPropagation">
    <view class="preview-header">
      <text class="preview-title">游戏截图</text>
      <view class="close-icon" bindtap="closeImagePreview">×</view>
    </view>
    
    <image class="preview-image" src="{{previewImageUrl}}" mode="aspectFit" bindtap="previewFullImage"></image>
    
    <view class="game-info" wx:if="{{previewGameInfo}}">
      <view class="info-row">
        <text class="info-label">游戏类型:</text>
        <text class="info-value">{{previewGameInfo.game_type || '未知'}}</text>
      </view>
      
      <view class="info-row">
        <text class="info-label">游戏队伍:</text>
        <text class="info-value">{{previewGameInfo.team || '未知'}}</text>
      </view>
      <view class="info-row">
        <text class="info-label">击杀/死亡:</text>
        <text class="info-value">{{previewGameInfo.kills || '0'}} / {{previewGameInfo.deaths || '0'}}</text>
      </view>
      <view class="info-row">
        <text class="info-label">更新时间:</text>
        <text class="info-value">{{previewGameInfo.game_time || '未知'}}</text>
      </view>
    </view>
    
    <view class="preview-footer">
      <view class="action-btn" bindtap="previewFullImage">全屏查看</view>
      <view class="action-btn" bindtap="closeImagePreview">关闭</view>
    </view>
  </view>
</view> 