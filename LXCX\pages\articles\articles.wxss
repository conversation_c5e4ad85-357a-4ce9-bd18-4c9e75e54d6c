/* 文章页面样式 */
.articles-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: linear-gradient(135deg, #f5ecd7 0%, #e9d8b4 100%);
  font-family: 'STKaiti', 'KaiTi', 'FZShuTi', 'FZKai-Z03', '楷体', serif;
  color: #4a3b22; /* 全局主字体色，柔和深棕 */
}

/* 分类选项卡样式 */
.category-tabs {
  white-space: nowrap;
  height: 80rpx;
  background: linear-gradient(90deg, #6b3a1c 0%, #bfa76a 100%);
  border-bottom: 4rpx solid #bfa76a;
  box-shadow: 0 2rpx 8rpx rgba(107, 58, 28, 0.08);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  padding: 0 10rpx;
}

.category-tabs .tab-item {
  display: inline-block;
  padding: 0 30rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  transition: all 0.3s;
  position: relative;
  color: #e9d8b4;
  font-family: inherit;
  letter-spacing: 2rpx;
}

/* 活动标签样式 */
.category-tabs .tab-item.tab-active {
  color: #fffbe6 !important;
  text-shadow: 0 2rpx 8rpx #bfa76a;
  font-weight: bold !important;
}

/* 活动指示线 */
.active-line {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 6rpx;
  background: linear-gradient(90deg, #bfa76a 0%, #fffbe6 100%);
  border-radius: 2rpx;
}

/* 移除旧的下划线实现 */
.articles-container .category-tabs .tab-item.active::after {
  content: none;
}

/* 文章列表样式 */
.articles-list {
  margin-top: 80rpx; /* 为固定定位的导航栏留出空间 */
  padding: 30rpx 10rpx 10rpx 10rpx;
  overflow-y: auto;
}

.article-item {
  display: flex;
  background: linear-gradient(120deg, #fffbe6 60%, #e9d8b4 100%);
  border: 2rpx solid #bfa76a;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(107, 58, 28, 0.10);
  position: relative;
}

.article-item::before {
  content: '';
  position: absolute;
  left: 0; top: 0; right: 0; bottom: 0;
  
  opacity: 0.04;
  pointer-events: none;
  z-index: 0;
}

/* 左侧封面图 */
.article-cover {
  width: 200rpx;
  height: 191rpx;
  flex-shrink: 0;
  object-fit: cover;
  border-right: 2rpx solid #bfa76a;
  border-radius: 0 12rpx 12rpx 0;
  background: #f5ecd7;
}

/* 右侧内容区 */
.article-content {
  flex: 1;
  padding: 15rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background: transparent;
  color: #000; /* 主体内容色，柔和深棕 */
  font-family: inherit;
  z-index: 1;
}

/* 上部标题 */
.article-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #000; /* 标题色，略深更聚焦 */
  line-height: 1.3;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  text-shadow: 0 2rpx 8rpx #e9d8b4;
  letter-spacing: 2rpx;
}

/* 中部分类和日期 */
.article-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 6rpx 0;
}

.article-category {
  font-size: 22rpx;
  color: #000;
  background: linear-gradient(90deg, #f7e7b4 0%, #e9d8b4 100%);
  padding: 2rpx 16rpx;
  border-radius: 12rpx;
  border: 1rpx solid #e9d8b4;
  max-width: 160rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-family: inherit;
}

.article-date {
  font-size: 20rpx;
  color: #a08c5b;
  text-align: right;
}

/* 下部摘要 */
.article-summary {
  font-size: 24rpx;
  color: #000; /* 摘要色，柔和棕色 */
  line-height: 1.5;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  margin-top: 10rpx;
  font-family: inherit;
}

.article-summary.empty-summary {
  color: #bfa76a;
  font-style: italic;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  background: none;
}

.empty-icon {
  font-size: 40rpx;
  color: #bfa76a;
  margin-bottom: 20rpx;
  border: 4rpx dashed #bfa76a;
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  background: #f5ecd7;
}

.empty-text {
  font-size: 28rpx;
  color: #a08c5b;
  font-family: inherit;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30rpx 0;
  background: none;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e9d8b4;
  border-radius: 50%;
  border-top-color: #bfa76a;
  animation: spin 0.8s linear infinite;
  margin-bottom: 10rpx;
}

.loading-text {
  font-size: 24rpx;
  color: #a08c5b;
  font-family: inherit;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 骨架屏样式 */
.skeleton-item {
  display: flex;
  background: linear-gradient(120deg, #fffbe6 60%, #e9d8b4 100%);
  border: 2rpx solid #bfa76a;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  padding: 20rpx;
  opacity: 0.7;
}

.skeleton-cover {
  width: 200rpx;
  height: 151rpx;
  background: linear-gradient(90deg, #e9d8b4 25%, #f5ecd7 50%, #e9d8b4 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 8rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.skeleton-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.skeleton-title {
  height: 40rpx;
  background: linear-gradient(90deg, #e9d8b4 25%, #f5ecd7 50%, #e9d8b4 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4rpx;
  width: 80%;
}

.skeleton-meta {
  display: flex;
  gap: 20rpx;
}

.skeleton-category {
  height: 24rpx;
  width: 80rpx;
  background: linear-gradient(90deg, #e9d8b4 25%, #f5ecd7 50%, #e9d8b4 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4rpx;
}

.skeleton-date {
  height: 24rpx;
  width: 120rpx;
  background: linear-gradient(90deg, #e9d8b4 25%, #f5ecd7 50%, #e9d8b4 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4rpx;
}

.skeleton-summary {
  height: 28rpx;
  background: linear-gradient(90deg, #e9d8b4 25%, #f5ecd7 50%, #e9d8b4 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4rpx;
  width: 100%;
}

.skeleton-summary.short {
  width: 60%;
}

@keyframes skeleton-loading {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* 空状态优化 */
.empty-tip {
  font-size: 24rpx;
  color: #bfa76a;
  margin-top: 10rpx;
  font-family: inherit;
}

/* 加载更多状态样式 */
.load-more-container {
  padding: 30rpx 0 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.load-more-loading {
  display: flex;
  align-items: center;
  gap: 20rpx;
  color: #8b7355;
}

.loading-spinner.small {
  width: 40rpx;
  height: 40rpx;
  border-width: 3rpx;
}

.load-more-text {
  font-size: 28rpx;
  color: #8b7355;
  font-family: inherit;
}

.load-more-end {
  display: flex;
  align-items: center;
  gap: 20rpx;
  color: #a0926b;
}

.end-line {
  width: 60rpx;
  height: 2rpx;
  background: #bfa76a;
  opacity: 0.5;
}

.end-text {
  font-size: 24rpx;
  color: #a0926b;
  font-family: inherit;
}

.load-more-error {
  display: flex;
  align-items: center;
  gap: 15rpx;
  padding: 20rpx 40rpx;
  background: rgba(255, 107, 107, 0.1);
  border: 2rpx solid rgba(255, 107, 107, 0.3);
  border-radius: 40rpx;
  color: #d63031;
  cursor: pointer;
}

.error-text {
  font-size: 28rpx;
  font-family: inherit;
}

.retry-icon {
  font-size: 32rpx;
  animation: rotate-hint 2s ease-in-out infinite;
}

@keyframes rotate-hint {
  0%, 100% { transform: rotate(0deg); }
  50% { transform: rotate(180deg); }
}
