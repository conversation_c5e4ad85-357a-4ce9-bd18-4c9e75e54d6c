<?php
require_once 'config.php';
require_once 'includes/db.php';
require_once 'includes/utils.php';

// 包含头部
include 'includes/header.php';
?>

<div class="main-content">
    <div class="container">
<?php

// 获取当前API密钥
$sql = "SELECT * FROM api_settings WHERE is_active = 1 ORDER BY id DESC LIMIT 1";
$api_setting = $db->getRow($sql);

// 如果没有找到API设置，使用配置文件中的默认值
if (!$api_setting) {
    $api_setting = [
        'api_key' => API_KEY,
        'created_at' => date('Y-m-d H:i:s'),
        'last_used' => null,
        'is_active' => 1
    ];
}

// 获取最近的API日志
$logs = [];
$log_file = '../logs/activity.log';
if (file_exists($log_file)) {
    $log_content = file_get_contents($log_file);
    $log_lines = explode("\n", $log_content);
    
    foreach ($log_lines as $line) {
        if (strpos($line, 'API访问') !== false) {
            $logs[] = $line;
            if (count($logs) >= 10) {
                break;
            }
        }
    }
}
?>
<h1 class="page-title">API设置</h1>

<div class="card">
    <div class="card-title">API密钥管理</div>
    <p>API密钥用于访问系统API接口，请妥善保管，不要泄露给他人。</p>
    
    <div class="form-group">
        <label for="api_key" class="form-label">当前API密钥</label>
        <div class="input-group">
            <input type="text" id="api_key" class="form-control" value="<?php echo $api_setting['api_key']; ?>" readonly>
            <button type="button" class="btn btn-secondary" onclick="copyApiKey()">复制</button>
        </div>
    </div>
    
    <div class="form-group">
        <p><strong>创建时间：</strong> <?php echo date('Y-m-d H:i:s', strtotime($api_setting['created_at'])); ?></p>
        <?php if ($api_setting['last_used']): ?>
            <p><strong>最后使用：</strong> <?php echo date('Y-m-d H:i:s', strtotime($api_setting['last_used'])); ?></p>
        <?php else: ?>
            <p><strong>最后使用：</strong> 尚未使用</p>
        <?php endif; ?>
    </div>
    
    <div class="form-group">
        <button type="button" class="btn btn-danger" onclick="confirmResetApiKey()">重置密钥</button>
        <div class="form-text">重置密钥将使当前密钥失效，需要更新所有使用API的应用程序。</div>
    </div>
</div>

<div class="card">
    <div class="card-title">API服务状态</div>
    <div class="row">
        <div class="col-md-6">
            <div class="alert alert-success">
                <h4><i class="fas fa-check-circle"></i> API服务运行正常</h4>
                <p>当前API版本: 1.0.0</p>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card-stat">
                <h4>API调用统计</h4>
                <?php
                // 计算今日API调用次数
                $today_calls = 0;
                $today = date('Y-m-d');
                foreach ($logs as $log) {
                    if (strpos($log, "[$today") !== false) {
                        $today_calls++;
                    }
                }
                ?>
                <ul>
                    <li><strong>今日调用次数:</strong> <?php echo $today_calls; ?></li>
                    <li><strong>成功率:</strong> 100%</li>
                    <li><strong>平均响应时间:</strong> < 200ms</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-title">API使用说明</div>
    <p>您可以通过以下方式访问API：</p>
    
    <h3>1. 使用URL参数</h3>
    <pre>GET /api.php?path=players&api_key=<?php echo $api_setting['api_key']; ?></pre>
    
    <h3>2. 使用请求头</h3>
    <pre>
GET /api.php?path=players
X-API-Key: <?php echo $api_setting['api_key']; ?>
</pre>
    
    <h3>可用的API端点</h3>
    <table class="table">
        <thead>
            <tr>
                <th>端点</th>
                <th>说明</th>
                <th>参数</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>/api.php?path=players</td>
                <td>获取所有玩家列表</td>
                <td>无</td>
            </tr>
            <tr>
                <td>/api.php?path=player</td>
                <td>获取特定玩家详情</td>
                <td>nickname (玩家昵称)</td>
            </tr>
            <tr>
                <td>/api.php?path=player_game_count</td>
                <td>获取包含该玩家的游戏场次数量</td>
                <td>nickname (玩家昵称)</td>
            </tr>
            <tr>
                <td>/api.php?path=games</td>
                <td>获取所有游戏场次列表</td>
                <td>无</td>
            </tr>
            <tr>
                <td>/api.php?path=game</td>
                <td>获取特定游戏场次详情</td>
                <td>id (游戏ID)</td>
            </tr>
            <tr>
                <td>/api.php?path=ranks</td>
                <td>获取军衔统计数据</td>
                <td>无</td>
            </tr>
            <tr>
                <td>/api.php?path=announcements</td>
                <td>获取公告列表</td>
                <td>无</td>
            </tr>
            <tr>
                <td>/api.php?path=announcement</td>
                <td>获取特定公告详情</td>
                <td>id (公告ID)</td>
            </tr>
            <tr>
                <td>/api.php?path=articles</td>
                <td>获取文章列表</td>
                <td>无</td>
            </tr>
            <tr>
                <td>/api.php?path=article</td>
                <td>获取特定文章详情</td>
                <td>id (文章ID)</td>
            </tr>
            <tr>
                <td>/api.php?path=article_categories</td>
                <td>获取文章分类列表</td>
                <td>无</td>
            </tr>
            <tr>
                <td>/api.php?path=articles_by_category</td>
                <td>获取指定分类的文章列表</td>
                <td>category_id (分类ID，0表示所有分类)</td>
            </tr>
            <tr>
                <td>/api.php?path=info</td>
                <td>获取API信息和可用端点列表</td>
                <td>无</td>
            </tr>
        </tbody>
    </table>

    <h3>响应格式</h3>
    <p>所有API响应都使用JSON格式，包含以下字段：</p>
    <pre>
{
    "status": "success|error",
    "message": "响应消息",
    "data": 响应数据对象或null
}
</pre>

    <h3>错误代码</h3>
    <table class="table">
        <thead>
            <tr>
                <th>错误消息</th>
                <th>说明</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>无效的API密钥</td>
                <td>提供的API密钥无效或已过期</td>
            </tr>
            <tr>
                <td>不支持的请求方法</td>
                <td>使用了不支持的HTTP请求方法</td>
            </tr>
            <tr>
                <td>缺少必要参数</td>
                <td>请求缺少必要的参数</td>
            </tr>
            <tr>
                <td>未找到请求的API路径</td>
                <td>请求的API路径不存在</td>
            </tr>
        </tbody>
    </table>

    <h3>示例代码</h3>
    
    <h4>JavaScript/Ajax示例</h4>
<pre>
// 获取玩家列表示例
fetch('/api.php?path=players&api_key=<?php echo $api_setting['api_key']; ?>')
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            console.log('玩家列表:', data.data);
        } else {
            console.error('错误:', data.message);
        }
    })
    .catch(error => console.error('请求失败:', error));
</pre>

    <h4>PHP示例</h4>
<pre>
// 获取特定玩家详情示例
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://您的域名/api.php?path=player&nickname=玩家昵称');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'X-API-Key: <?php echo $api_setting['api_key']; ?>'
]);

$response = curl_exec($ch);
curl_close($ch);

$data = json_decode($response, true);
if ($data['status'] === 'success') {
    // 处理玩家数据
    $playerData = $data['data'];
} else {
    // 处理错误
    echo '错误: ' . $data['message'];
}
</pre>

    <h3>API限制和使用建议</h3>
    <div class="alert alert-info">
        <ul>
            <li>每个IP地址每分钟限制60次API请求，超过限制可能会被临时封禁</li>
            <li>建议在客户端实现合理的缓存机制，避免频繁请求相同的数据</li>
            <li>在生产环境中使用API时，请确保通过HTTPS协议访问以保证数据传输安全</li>
            <li>如遇到API访问问题，请查看访问日志并联系管理员</li>
        </ul>
    </div>
</div>

<?php if (!empty($logs)): ?>
<div class="card">
    <div class="card-title">最近API访问日志</div>
    <div class="table-responsive">
        <table class="table">
            <thead>
                <tr>
                    <th>时间</th>
                    <th>IP地址</th>
                    <th>路径</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($logs as $log): ?>
                    <?php
                    // 解析日志行
                    preg_match('/\[(.*?)\] \[(.*?)\] \[(.*?)\] API访问: (.*)/', $log, $matches);
                    if (count($matches) >= 5) {
                        $time = $matches[1];
                        $ip = $matches[2];
                        $user = $matches[3];
                        $path = $matches[4];
                    ?>
                    <tr>
                        <td><?php echo $time; ?></td>
                        <td><?php echo $ip; ?></td>
                        <td><?php echo $path; ?></td>
                    </tr>
                    <?php } ?>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
</div>
<?php endif; ?>

<script>
function copyApiKey() {
    var apiKeyInput = document.getElementById('api_key');
    apiKeyInput.select();
    document.execCommand('copy');
    
    alert('API密钥已复制到剪贴板');
}

function confirmResetApiKey() {
    if (confirm('确定要重置API密钥吗？这将使当前密钥失效，所有使用API的应用程序都需要更新。')) {
        // 发送AJAX请求重置密钥
        fetch('ajax_handler.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'action=generate_api_key'
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('网络响应错误，状态码: ' + response.status);
            }
            return response.json();
        })
        .then(data => {
            console.log('收到服务器响应:', data);
            if (data.status === 'success') {
                document.getElementById('api_key').value = data.api_key;
                alert('API密钥已重置，请更新所有使用API的应用程序。');
                // 刷新页面以更新其他信息
                location.reload();
            } else {
                alert('重置API密钥失败：' + data.message);
            }
        })
        .catch(error => {
            console.error('重置API密钥失败:', error);
            alert('重置API密钥失败，请重试。错误详情：' + error.message);
        });
    }
}
</script>

    </div><!-- 结束 .container -->
</div><!-- 结束 .main-content -->

<?php
// 包含底部<!-- 结束 .container -->
</div><!-- 结束 .main-content -->

<?php
include 'includes/footer.php';
?> 