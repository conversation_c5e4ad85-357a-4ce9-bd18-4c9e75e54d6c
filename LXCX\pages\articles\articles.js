// 导入API工具
const api = require('../../utils/api.js')

// 加载工具函数
const util = require('../../utils/util.js');

// 缓存相关常量
const CACHE_KEY = {
  CATEGORIES: 'article_categories_cache',
  ARTICLES: 'articles_cache_',  // 将根据分类ID拼接完整key
  TIMESTAMP: 'cache_timestamp_'
};

const CACHE_EXPIRE_TIME = 3 * 60 * 1000; // 缓存过期时间：3分钟

// 缓存工具函数
const cacheUtil = {
  // 获取缓存
  get: function(key) {
    const data = wx.getStorageSync(key);
    if (!data) return null;
    
    // 检查缓存是否过期
    const timestamp = wx.getStorageSync(CACHE_KEY.TIMESTAMP + key);
    if (timestamp && Date.now() - timestamp > CACHE_EXPIRE_TIME) {
      this.remove(key);
      return null;
    }
    return data;
  },
  
  // 设置缓存
  set: function(key, data) {
    wx.setStorageSync(key, data);
    wx.setStorageSync(CACHE_KEY.TIMESTAMP + key, Date.now());
  },
  
  // 删除缓存
  remove: function(key) {
    wx.removeStorageSync(key);
    wx.removeStorageSync(CACHE_KEY.TIMESTAMP + key);
  },
  
  // 获取文章列表缓存key
  getArticlesCacheKey: function(categoryId) {
    return CACHE_KEY.ARTICLES + categoryId;
  }
};

Page({
  data: {
    // 分类列表
    categories: [],
    // 文章列表
    articles: [],
    // 当前选中的分类ID，0表示全部
    currentCategoryId: 0,
    // 是否正在加载
    isLoading: false,
    // 是否正在加载更多
    isLoadingMore: false,
    // 是否已加载全部数据
    hasMore: true,
    // 加载错误状态
    loadError: false,
    // 当前页码
    page: 1,
    // 每页数量
    pageSize: 10,
    // 默认图片路径前缀
    imagePath: '/images/articleimg/',
    // 备用默认图片
    defaultImage: '/images/articleimg/default.png',
    // 文章图片文件名数组
    articleImages: [
      'p001.jpg', 'p002.jpg', 'p003.jpg', 'p004.jpg', 'p005.jpg',
      'p006.jpg', 'p007.jpg', 'p008.jpg', 'p009.jpg', 'p0010.jpg',
      'p0011.jpg', 'p0012.jpg', 'p0013.jpg', 'p0014.jpg', 'p0015.jpg',
      'p0016.jpg', 'p0017.jpg', 'p0018.jpg', 'p0019.jpg', 'p0020.jpg'
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 确保currentCategoryId的初始值为数字0
    this.setData({
      currentCategoryId: 0
    });

    // 优化初始加载：先显示骨架屏，然后并行加载数据
    this.setData({ isLoading: true });

    // 并行加载分类和文章，提升加载速度
    Promise.all([
      this.loadCategories(),
      this.loadArticles()
    ]).then(() => {
      // 预加载下一页数据，提升用户体验
      if (this.data.articles.length >= this.data.pageSize) {
        setTimeout(() => {
          this.preloadNextPage();
        }, 1000);
      }


    }).catch(err => {
      console.error('初始加载失败:', err);
    }).finally(() => {
      this.setData({ isLoading: false });
    });
  },

  /**
   * 预加载下一页数据
   */
  preloadNextPage: function() {
    if (this.data.hasMore && !this.data.isLoading && !this.data.isLoadingMore) {
      const nextPage = this.data.page + 1;

      // 静默预加载下一页
      const app = getApp();
      const params = {
        api_key: app.globalData.apiKey,
        page: nextPage,
        limit: this.data.pageSize,
        include_content: true,
        include_summary: true
      };

      if (this.data.currentCategoryId === 0) {
        params.path = 'articles';
      } else {
        params.path = 'articles_by_category';
        params.category_id = this.data.currentCategoryId;
      }

      const queryParams = Object.keys(params)
        .map(key => `${key}=${encodeURIComponent(params[key])}`)
        .join('&');

      const url = `${app.globalData.apiBaseUrl}?${queryParams}`;

      wx.request({
        url: url,
        method: 'GET',
        success: (res) => {
          if (res.statusCode === 200 && res.data && res.data.status === 'success') {
            // 预加载成功，缓存数据
            const cacheKey = `preload_${this.data.currentCategoryId}_${nextPage}`;
            wx.setStorageSync(cacheKey, {
              data: res.data.data,
              timestamp: Date.now()
            });
          }
        }
      });
    }
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    // 下拉刷新强制更新数据，忽略缓存
    this.setData({
      page: 1,
      hasMore: true,
      articles: [],
      loadError: false
    });

    // 强制刷新分类和文章数据
    Promise.all([
      this.loadCategories(true),
      this.loadArticles(() => {
        wx.stopPullDownRefresh();
      }, true)
    ]).catch(err => {
      console.error('下拉刷新失败:', err);
      wx.stopPullDownRefresh();
      wx.showToast({
        title: '刷新失败，请重试',
        icon: 'none'
      });
    });
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    // 防抖处理，避免频繁触发
    if (this.loadMoreTimer) {
      clearTimeout(this.loadMoreTimer);
    }

    this.loadMoreTimer = setTimeout(() => {
      // 上拉加载更多
      if (this.data.hasMore && !this.data.isLoading && !this.data.isLoadingMore) {
        this.loadMoreArticles();
      }
    }, 100);
  },

  /**
   * 页面滚动事件处理（优化无限滚动体验）
   */
  onPageScroll: function(e) {
    // 当滚动到接近底部时，提前开始加载
    const scrollTop = e.scrollTop;
    const threshold = 1000; // 距离底部1000rpx时开始预加载

    // 获取页面高度信息
    if (!this.pageHeight) {
      wx.getSystemInfo({
        success: (res) => {
          this.pageHeight = res.windowHeight;
        }
      });
      return;
    }

    // 计算是否接近底部
    wx.createSelectorQuery().select('.articles-list').boundingClientRect((rect) => {
      if (rect) {
        const distanceToBottom = rect.height - scrollTop - this.pageHeight;

        // 当距离底部小于阈值且满足加载条件时，开始预加载
        if (distanceToBottom < threshold &&
            this.data.hasMore &&
            !this.data.isLoading &&
            !this.data.isLoadingMore &&
            !this.preloadingNext) {

          this.preloadingNext = true;
          setTimeout(() => {
            this.preloadingNext = false;
          }, 2000);

          this.loadMoreArticles();
        }
      }
    }).exec();
  },

  /**
   * 加载文章分类
   * @param {boolean} forceRefresh - 是否强制刷新，忽略缓存
   */
  loadCategories: function (forceRefresh = false) {
    // 尝试从缓存获取分类数据
    if (!forceRefresh) {
      const cachedCategories = cacheUtil.get(CACHE_KEY.CATEGORIES);
      if (cachedCategories) {
        this.setData({ categories: cachedCategories });
        return Promise.resolve(cachedCategories);
      }
    }

    // 显示加载状态
    this.setData({ isLoading: true });
    
    // 调用API获取文章分类
    return api.getArticleCategories()
      .then(data => {
        const categories = data || [];
        this.setData({ categories });
        // 缓存分类数据
        cacheUtil.set(CACHE_KEY.CATEGORIES, categories);
        return categories;
      })
      .catch(err => {
        console.error('获取文章分类失败', err);
        wx.showToast({
          title: '获取分类失败',
          icon: 'none'
        });
      })
      .finally(() => {
        this.setData({ isLoading: false });
      });
  },

  /**
   * 获取文章封面图片路径
   * @param {number} id - 文章ID，用于确保相同文章总是使用相同图片
   * @returns {string} 图片完整路径
   */
  getArticleImage: function(id) {
    // 确保有一个有效的ID
    const safeId = id || Math.floor(Math.random() * 1000);
    
    // 使用文章ID来确定使用哪张图片
    const imageIndex = safeId % this.data.articleImages.length;
    const imageName = this.data.articleImages[imageIndex];
    return this.data.imagePath + imageName;
  },

  /**
   * 批量获取文章摘要（后端已支持）
   * @param {Array} articleIds - 文章ID数组
   * @returns {Promise} Promise对象，成功时返回摘要对象 {id: summary}
   */
  fetchArticleSummariesBatch: function(articleIds) {
    return new Promise(async (resolve, reject) => {
      if (!articleIds || articleIds.length === 0) {
        resolve({});
        return;
      }



      // 如果文章数量很多，分批处理以避免URL过长
      const batchSize = 20; // 每批最多20篇文章
      if (articleIds.length > batchSize) {
        try {
          const allSummaries = {};
          for (let i = 0; i < articleIds.length; i += batchSize) {
            const batch = articleIds.slice(i, i + batchSize);
            const batchSummaries = await this.fetchArticleSummariesBatch(batch);
            Object.assign(allSummaries, batchSummaries);
          }
          resolve(allSummaries);
          return;
        } catch (error) {
          reject(error);
          return;
        }
      }

      // 获取全局数据
      const app = getApp();

      // 构建批量请求URL，支持逗号分隔的ID列表
      const url = `${app.globalData.apiBaseUrl}?path=article_summaries_batch&ids=${articleIds.join(',')}&api_key=${app.globalData.apiKey}`;

      // 发起批量请求
      wx.request({
        url: url,
        method: 'GET',
        success: (res) => {

          if (res.statusCode === 200) {
            if (res.data && res.data.status === 'success') {
              const summaries = {};
              const data = res.data.data || {};

              // 检查后端返回的数据结构
              if (data.found_articles && Array.isArray(data.found_articles)) {

                // 处理新的数据结构：found_articles数组
                data.found_articles.forEach(article => {
                  const id = article.id;
                  let summary = article.summary;

                  if (summary && typeof summary === 'string' && summary.trim()) {
                    // 处理得到的文本，去除HTML标签
                    summary = summary.replace(/<[^>]+>/g, '').trim();
                    if (summary.length > 80) {
                      summary = summary.substring(0, 80) + '...';
                    }
                    summaries[id] = summary;
                  } else {
                    summaries[id] = '暂无内容摘要';
                  }
                });

                // 确保所有请求的ID都有对应的返回值
                articleIds.forEach(id => {
                  if (!summaries[id]) {
                    summaries[id] = '暂无内容摘要';
                  }
                });
              } else {
                // 处理原有的数据结构：{id: summary}
                articleIds.forEach(id => {
                  let summary = data[id];
                  if (summary && typeof summary === 'string' && summary.trim()) {
                    // 处理得到的文本，去除HTML标签
                    summary = summary.replace(/<[^>]+>/g, '').trim();
                    if (summary.length > 80) {
                      summary = summary.substring(0, 80) + '...';
                    }
                    summaries[id] = summary;
                  } else {
                    summaries[id] = '暂无内容摘要';
                  }
                });
              }

              resolve(summaries);
            } else {
              reject(new Error(res.data?.message || '批量获取摘要失败'));
            }
          } else {
            reject(new Error(`HTTP错误: ${res.statusCode}`));
          }
        },
        fail: (err) => {
          reject(err);
        }
      });
    });
  },



  /**
   * 单个获取文章摘要（备用方法）
   * @param {number} id - 文章ID
   * @returns {Promise} Promise对象，成功时返回摘要内容
   */
  fetchArticleSummary: function(id) {
    return new Promise((resolve) => {
      // 获取全局数据
      const app = getApp();

      // 构建API请求URL
      const url = `${app.globalData.apiBaseUrl}?path=article_summary&id=${id}&api_key=${app.globalData.apiKey}`;

      // 发起请求
      wx.request({
        url: url,
        method: 'GET',
        success: (res) => {
          if (res.statusCode === 200) {
            let summary = '暂无内容摘要';

            // 直接尝试提取文本内容
            if (typeof res.data === 'string') {
              summary = res.data;
            } else if (res.data && typeof res.data.data === 'string') {
              summary = res.data.data;
            } else if (res.data && res.data.data && typeof res.data.data.summary === 'string') {
              summary = res.data.data.summary;
            }

            // 处理得到的文本，去除HTML标签
            if (summary !== '暂无内容摘要') {
              summary = summary.replace(/<[^>]+>/g, '').trim();
              if (summary.length > 50) {
                summary = summary.substring(0, 50) + '...';
              }
            }

            resolve(summary);
          } else {
            resolve('暂无内容摘要');
          }
        },
        fail: () => {
          resolve('暂无内容摘要');
        }
      });
    });
  },

  /**
   * 加载文章列表
   * @param {Function} callback - 加载完成后的回调函数
   * @param {boolean} forceRefresh - 是否强制刷新，忽略缓存
   */
  loadArticles: function (callback, forceRefresh = false) {
    // 如果是第一页且非强制刷新，尝试从缓存获取
    if (this.data.page === 1 && !forceRefresh) {
      const cacheKey = cacheUtil.getArticlesCacheKey(this.data.currentCategoryId);
      const cachedArticles = cacheUtil.get(cacheKey);
      if (cachedArticles) {
        this.setData({ 
          articles: cachedArticles,
          hasMore: cachedArticles.length >= this.data.pageSize
        });
        if (typeof callback === 'function') {
          callback();
        }
        return;
      }
    }

    // 显示加载状态
    this.setData({ isLoading: true });
    
    // 获取全局数据
    const app = getApp();
    
    // 构建基础URL和参数
    let url = app.globalData.apiBaseUrl;
    const params = {
      api_key: app.globalData.apiKey,
      page: this.data.page,
      limit: this.data.pageSize,
      include_content: true,
      include_summary: true
    };
    
    // 根据不同分类使用不同的API路径
    if (this.data.currentCategoryId === 0) {
      params.path = 'articles';
    } else {
      params.path = 'articles_by_category';
      params.category_id = this.data.currentCategoryId;
    }
    
    // 构建完整的URL
    const queryParams = Object.keys(params)
      .map(key => `${key}=${encodeURIComponent(params[key])}`)
      .join('&');
    
    url = `${url}?${queryParams}`;
    
    // 发起请求
    wx.request({
      url: url,
      method: 'GET',
      success: (res) => {
        if (res.statusCode === 200 && res.data && res.data.status === 'success') {
          let newArticles = [];
          const data = res.data.data;
          
          // 处理不同类型的响应数据
          if (Array.isArray(data)) {
            newArticles = data;
          } else if (data && data.items && Array.isArray(data.items)) {
            newArticles = data.items;
          } else if (data && typeof data === 'object') {
            newArticles = Object.values(data).filter(item => 
              item && typeof item === 'object' && item.id && item.title
            );
          }

          // 处理文章数据
          const processArticles = async () => {
            // 首先处理基本信息
            newArticles.forEach(article => {
              article.cover = this.getArticleImage(article.id);

              if (!article.publish_time || article.publish_time === '') {
                let timestamp = article.created_at || article.create_time || new Date().getTime();
                let date = typeof timestamp === 'string' ? util.parseDate(timestamp) : new Date(timestamp);
                if (isNaN(date.getTime())) date = new Date();
                article.publish_time = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
              }
            });

            // 批量获取需要摘要的文章ID
            const articlesNeedingSummary = newArticles.filter(article =>
              !article.summary || article.summary === '' || typeof article.summary === 'object'
            );

            if (articlesNeedingSummary.length > 0) {
              const articleIds = articlesNeedingSummary.map(article => article.id);
              try {
                // 优先使用批量获取（后端已支持）
                const summaries = await this.fetchArticleSummariesBatch(articleIds);

                // 批量获取成功，分配摘要
                articlesNeedingSummary.forEach(article => {
                  article.summary = summaries[article.id] || '暂无内容摘要';
                });
              } catch (error) {
                // 批量获取失败时，回退到优化的单个获取方式
                await this.fetchSummariesIndividually(articlesNeedingSummary);
              }
            }

            const finalArticles = this.data.page === 1 ? newArticles : [...this.data.articles, ...newArticles];

            // 更新数据
            this.setData({
              articles: finalArticles,
              hasMore: newArticles.length >= this.data.pageSize
            });

            // 如果是第一页，缓存文章列表
            if (this.data.page === 1) {
              const cacheKey = cacheUtil.getArticlesCacheKey(this.data.currentCategoryId);
              cacheUtil.set(cacheKey, finalArticles);
            }
          };
          
          // 执行异步处理
          processArticles().then(() => {
            if (typeof callback === 'function') {
              callback();
            }
          });
        }
      },
      fail: (err) => {
        console.error('请求文章列表接口失败:', err);

        // 如果是加载更多失败，显示重试按钮
        if (this.data.page > 1) {
          this.setData({
            loadError: true,
            page: this.data.page - 1 // 回退页码
          });
        } else {
          // 首次加载失败，显示toast
          wx.showToast({
            title: '网络错误，请重试',
            icon: 'none'
          });
        }
      },
      complete: () => {
        this.setData({
          isLoading: false,
          isLoadingMore: false
        });
      }
    });
  },

  /**
   * 加载更多文章
   */
  loadMoreArticles: function () {
    const nextPage = this.data.page + 1;
    const cacheKey = `preload_${this.data.currentCategoryId}_${nextPage}`;

    this.setData({
      page: nextPage,
      isLoadingMore: true,
      loadError: false
    });

    // 尝试使用预加载的数据
    const preloadedData = wx.getStorageSync(cacheKey);
    if (preloadedData && Date.now() - preloadedData.timestamp < 60000) { // 1分钟内有效
      // 使用预加载的数据
      this.processPreloadedData(preloadedData.data);
      wx.removeStorageSync(cacheKey); // 清除已使用的预加载数据
    } else {
      // 正常加载
      this.loadArticles(() => {
        this.setData({
          isLoadingMore: false
        });

        // 预加载下一页
        setTimeout(() => {
          this.preloadNextPage();
        }, 500);
      });
    }
  },

  /**
   * 处理预加载的数据
   */
  processPreloadedData: function(data) {
    let newArticles = [];

    // 处理不同类型的响应数据
    if (Array.isArray(data)) {
      newArticles = data;
    } else if (data && data.items && Array.isArray(data.items)) {
      newArticles = data.items;
    } else if (data && typeof data === 'object') {
      newArticles = Object.values(data).filter(item =>
        item && typeof item === 'object' && item.id && item.title
      );
    }

    // 处理文章数据
    const processArticles = async () => {
      // 处理基本信息
      newArticles.forEach(article => {
        article.cover = this.getArticleImage(article.id);

        if (!article.publish_time || article.publish_time === '') {
          let timestamp = article.created_at || article.create_time || new Date().getTime();
          let date = typeof timestamp === 'string' ? util.parseDate(timestamp) : new Date(timestamp);
          if (isNaN(date.getTime())) date = new Date();
          article.publish_time = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
        }
      });

      // 批量获取摘要
      const articlesNeedingSummary = newArticles.filter(article =>
        !article.summary || article.summary === '' || typeof article.summary === 'object'
      );

      if (articlesNeedingSummary.length > 0) {
        const articleIds = articlesNeedingSummary.map(article => article.id);
        try {
          // 优先使用批量获取（后端已支持）
          const summaries = await this.fetchArticleSummariesBatch(articleIds);
          articlesNeedingSummary.forEach(article => {
            article.summary = summaries[article.id] || '暂无内容摘要';
          });
        } catch (error) {
          // 批量获取失败时，回退到优化的单个获取方式
          await this.fetchSummariesIndividually(articlesNeedingSummary);
        }
      }

      const finalArticles = [...this.data.articles, ...newArticles];

      // 更新数据
      this.setData({
        articles: finalArticles,
        hasMore: newArticles.length >= this.data.pageSize,
        isLoadingMore: false
      });
    };

    processArticles();
  },

  /**
   * 重试加载更多
   */
  retryLoadMore: function () {
    this.setData({
      loadError: false
    })
    this.loadMoreArticles()
  },

  /**
   * 切换文章分类
   * @param {Object} e - 事件对象
   */
  switchCategory: function (e) {
    const categoryId = parseInt(e.currentTarget.dataset.id);
    
    // 如果点击的是当前分类，不做任何操作
    if (categoryId === this.data.currentCategoryId) {
      return;
    }
    
    // 先将页面滚动到顶部
    wx.pageScrollTo({
      scrollTop: 0,
      duration: 0
    });
    
    // 更新当前分类并重置页码
    this.setData({
      currentCategoryId: categoryId,
      page: 1,
      hasMore: true,
      articles: [],
      isLoading: true  // 立即显示加载状态
    });
    
    // 延迟一下再加载数据，确保UI更新完成
    setTimeout(() => {
      // 尝试从缓存加载新分类的文章
      this.loadArticles(() => {
        this.setData({
          isLoading: false
        });
      });
      
      // 预加载下一个分类的数据
      this.preloadNextCategory(categoryId);
    }, 100);
  },

  /**
   * 预加载下一个分类的数据
   * @param {number} currentCategoryId - 当前分类ID
   */
  preloadNextCategory: function(currentCategoryId) {
    // 找到当前分类在列表中的位置
    const categoryIndex = this.data.categories.findIndex(c => c.id === currentCategoryId);
    if (categoryIndex === -1) return;

    // 获取下一个分类的ID
    const nextCategory = this.data.categories[categoryIndex + 1] || this.data.categories[0];
    if (!nextCategory) return;

    // 检查下一个分类是否已经有缓存
    const nextCacheKey = cacheUtil.getArticlesCacheKey(nextCategory.id);
    if (!cacheUtil.get(nextCacheKey)) {
      // 构建预加载请求参数
      const app = getApp();
      const params = {
        api_key: app.globalData.apiKey,
        page: 1,
        limit: this.data.pageSize,
        path: 'articles_by_category',
        category_id: nextCategory.id,
        include_content: true,
        include_summary: true
      };

      // 静默预加载
      wx.request({
        url: `${app.globalData.apiBaseUrl}?${Object.keys(params)
          .map(key => `${key}=${encodeURIComponent(params[key])}`)
          .join('&')}`,
        method: 'GET',
        success: (res) => {
          if (res.statusCode === 200 && res.data && res.data.status === 'success') {
            const articles = Array.isArray(res.data.data) ? res.data.data : 
              (res.data.data && res.data.data.items) || [];
            
            // 缓存预加载的数据
            if (articles.length > 0) {
              cacheUtil.set(nextCacheKey, articles);
            }
          }
        }
      });
    }
  },

  /**
   * 跳转到文章详情页
   * @param {Object} e - 事件对象
   */
  goToDetail: function (e) {
    const articleId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/articles/article-detail/article-detail?id=${articleId}`
    })
  },

  // 分享功能
  onShareAppMessage: function () {
    const currentCategory = this.data.categories.find(c => c.id === this.data.currentCategoryId);
    const categoryName = currentCategory ? currentCategory.name : '全部';
    
    return {
      title: `${categoryName}文章列表`,
      path: `/pages/articles/articles?category=${this.data.currentCategoryId}`
    }
  },
  // 分享到朋友圈
  onShareTimeline: function () {
    const currentCategory = this.data.categories.find(c => c.id === this.data.currentCategoryId);
    const categoryName = currentCategory ? currentCategory.name : '全部';

    return {
      title: `${categoryName}文章列表`,
      path: `/pages/articles/articles?category=${this.data.currentCategoryId}`
    }
  },

  /**
   * 页面卸载时的清理工作
   */
  onUnload: function() {
    // 清理定时器
    if (this.loadMoreTimer) {
      clearTimeout(this.loadMoreTimer);
      this.loadMoreTimer = null;
    }

    // 清理预加载标志
    this.preloadingNext = false;
    this.pageHeight = null;
  }
})
