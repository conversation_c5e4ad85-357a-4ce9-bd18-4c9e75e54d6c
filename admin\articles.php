<?php
require_once 'config.php';
require_once 'includes/db.php';
require_once 'includes/utils.php';

// AJAX请求处理 - 获取所有文章ID
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'get_all_article_ids') {
    header('Content-Type: application/json');

    // 获取筛选参数
    $category_filter = isset($_POST['category']) ? (int)$_POST['category'] : 0;
    $status_filter = isset($_POST['status']) ? (int)$_POST['status'] : -1;
    $search_query = isset($_POST['search']) ? trim($_POST['search']) : '';

    // 构建查询条件（与主查询逻辑保持一致）
    $where_clause = "";
    $conditions = array();

    if ($category_filter > 0) {
        $conditions[] = "a.category_id = {$category_filter}";
    }

    if ($status_filter !== -1) {
        $conditions[] = "a.published = {$status_filter}";
    }

    if (!empty($search_query)) {
        $search_query = $db->escape($search_query);
        $conditions[] = "(a.title LIKE '%{$search_query}%' OR a.content LIKE '%{$search_query}%' OR a.summary LIKE '%{$search_query}%')";
    }

    if (!empty($conditions)) {
        $where_clause = " WHERE " . implode(" AND ", $conditions);
    }

    // 获取所有符合条件的文章ID
    $sql = "SELECT a.id FROM articles a{$where_clause} ORDER BY create_time DESC";
    $result = $db->getRows($sql);

    $article_ids = array();
    if ($result) {
        foreach ($result as $row) {
            $article_ids[] = (int)$row['id'];
        }
    }

    echo json_encode([
        'success' => true,
        'article_ids' => $article_ids,
        'total_count' => count($article_ids)
    ]);
    exit;
}

// 批量操作处理
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['batch_action']) && isset($_POST['article_ids'])) {
    $batch_action = $_POST['batch_action'];
    $article_ids = $_POST['article_ids'];
    
    if (!empty($article_ids) && is_array($article_ids)) {
        $ids = implode(',', array_map('intval', $article_ids));
        
        if ($batch_action === 'delete') {
            // 批量删除
            $delete_sql = "DELETE FROM articles WHERE id IN ({$ids})";
            if ($db->query($delete_sql)) {
                $_SESSION['success_message'] = "已成功删除所选文章";
            } else {
                $_SESSION['error_message'] = "删除文章时出错";
            }
        } elseif ($batch_action === 'publish') {
            // 批量发布
            $publish_sql = "UPDATE articles SET published = 1, update_time = NOW() WHERE id IN ({$ids})";
            if ($db->query($publish_sql)) {
                $_SESSION['success_message'] = "已成功发布所选文章";
            } else {
                $_SESSION['error_message'] = "发布文章时出错";
            }
        } elseif ($batch_action === 'draft') {
            // 批量设为草稿
            $draft_sql = "UPDATE articles SET published = 0, update_time = NOW() WHERE id IN ({$ids})";
            if ($db->query($draft_sql)) {
                $_SESSION['success_message'] = "已成功将所选文章设为草稿";
            } else {
                $_SESSION['error_message'] = "更新文章状态时出错";
            }
        } elseif (strpos($batch_action, 'category_') === 0) {
            // 批量修改分类
            $category_id = (int)substr($batch_action, 9);
            $update_sql = "UPDATE articles SET category_id = {$category_id}, update_time = NOW() WHERE id IN ({$ids})";
            if ($db->query($update_sql)) {
                $_SESSION['success_message'] = "已成功更新所选文章的分类";
            } else {
                $_SESSION['error_message'] = "更新文章分类时出错";
            }
        }
        
        // 重定向以避免表单重复提交
        header("Location: articles.php");
        exit;
    }
}

// 包含头部
include 'includes/header.php';
?>
<?php
// 获取分类列表
$categories_sql = "SELECT * FROM article_categories ORDER BY name ASC";
$categories = $db->getRows($categories_sql);

// 获取分类筛选参数
$category_filter = isset($_GET['category']) ? (int)$_GET['category'] : 0;

// 获取状态筛选参数
$status_filter = isset($_GET['status']) ? (int)$_GET['status'] : -1; // -1表示全部，0表示草稿，1表示已发布

// 获取分页参数
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$page = max(1, $page);
$per_page = 10;
$offset = ($page - 1) * $per_page;

// 获取搜索参数
$search_query = isset($_GET['search']) ? trim($_GET['search']) : '';

// 构建查询条件
$where_clause = "";
$conditions = array();

if ($category_filter > 0) {
    $conditions[] = "a.category_id = {$category_filter}";
}

if ($status_filter !== -1) {
    $conditions[] = "a.published = {$status_filter}";
}

// 添加搜索条件
if (!empty($search_query)) {
    $search_query = $db->escape($search_query);
    $conditions[] = "(a.title LIKE '%{$search_query}%' OR a.content LIKE '%{$search_query}%' OR a.summary LIKE '%{$search_query}%')";
}

if (!empty($conditions)) {
    $where_clause = " WHERE " . implode(" AND ", $conditions);
}

// 获取总记录数
$count_sql = "SELECT COUNT(*) as total FROM articles a{$where_clause}";
$result = $db->getRow($count_sql);
$total_records = $result['total'];
$total_pages = ceil($total_records / $per_page);

// 获取文章列表
$sql = "SELECT a.*, c.name as category_name 
        FROM articles a 
        LEFT JOIN article_categories c ON a.category_id = c.id{$where_clause} 
        ORDER BY create_time DESC LIMIT {$offset}, {$per_page}";
$articles = $db->getRows($sql);
?>
<h1 class="page-title">文章管理</h1>

<?php
// 显示成功或错误消息
if (isset($_SESSION['success_message'])) {
    echo '<div class="alert alert-success">' . $_SESSION['success_message'] . '</div>';
    unset($_SESSION['success_message']);
}
if (isset($_SESSION['error_message'])) {
    echo '<div class="alert alert-danger">' . $_SESSION['error_message'] . '</div>';
    unset($_SESSION['error_message']);
}
?>

<div class="article-dashboard">
    <div class="card article-card">
        <div class="card-header">
            <div class="card-title">文章列表</div>
            <div class="card-actions">
                <a href="article_edit.php" class="btn btn-primary btn-sm"><i class="fas fa-plus"></i> 新增文章</a>
                <a href="import_export.php?type=article" class="btn btn-secondary btn-sm"><i class="fas fa-file-import"></i> 导入/导出</a>
                <div class="batch-operation">
                    <select id="batch_action_top" class="form-control form-control-sm">
                        <option value="">批量操作...</option>
                        <option value="publish">设为已发布</option>
                        <option value="draft">设为草稿</option>
                        <option value="delete">删除</option>
                        <optgroup label="修改分类">
                            <?php foreach ($categories as $category): ?>
                            <option value="category_<?php echo $category['id']; ?>">移至: <?php echo $category['name']; ?></option>
                            <?php endforeach; ?>
                        </optgroup>
                    </select>
                    <button type="button" class="btn btn-secondary btn-sm" id="apply-batch-top">
                        <i class="fas fa-check"></i> 应用 <span id="selected-count" class="badge badge-light ml-1" style="display: none;">0</span>
                    </button>
                    <div class="selection-controls ml-3">
                        <button type="button" class="btn btn-outline-primary btn-sm" id="select-all-global" title="选择所有符合筛选条件的文章">
                            <i class="fas fa-globe"></i> 选择所有文章
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm" id="clear-selection" style="display: none;" title="清除所有选择">
                            <i class="fas fa-times"></i> 清除选择
                        </button>
                    </div>
                    <div class="selection-info ml-2" id="selection-info" style="display: none;">
                        <small class="text-muted">
                            <i class="fas fa-info-circle"></i> <span id="selection-scope">当前页面</span>
                        </small>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="filter-section">
            <form action="articles.php" method="GET" class="search-filter">
                <div class="filter-row">
                    <div class="search-box">
                        <div class="input-group">
                            <input type="text" name="search" class="form-control" placeholder="搜索文章..." value="<?php echo htmlspecialchars($search_query); ?>">
                        </div>
                    </div>
                    <div class="category-box">
                        <select name="category" id="category" class="form-control">
                            <option value="0"<?php echo $category_filter == 0 ? ' selected' : ''; ?>>所有分类</option>
                            <?php foreach ($categories as $category): ?>
                            <option value="<?php echo $category['id']; ?>"<?php echo $category_filter == $category['id'] ? ' selected' : ''; ?>>
                                <?php echo $category['name']; ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="status-box">
                        <select name="status" id="status" class="form-control">
                            <option value="-1"<?php echo $status_filter == -1 ? ' selected' : ''; ?>>所有状态</option>
                            <option value="0"<?php echo $status_filter === 0 ? ' selected' : ''; ?>>草稿</option>
                            <option value="1"<?php echo $status_filter === 1 ? ' selected' : ''; ?>>已发布</option>
                        </select>
                    </div>
                    <div class="filter-btn">
                        <button type="submit" class="btn btn-info"><i class="fas fa-search"></i> 筛选</button>
                    </div>
                </div>
            </form>
        </div>
        
        <?php if (empty($articles)): ?>
            <div class="alert alert-info mt-3">暂无文章</div>
        <?php else: ?>
            <form id="batch-form" method="POST" action="articles.php">
                <input type="hidden" name="batch_action" id="batch_action_hidden" value="">
                
                <div class="table-responsive">
                    <table class="table table-striped table-hover article-table">
                        <thead>
                            <tr>
                                <th width="3%">
                                    <input type="checkbox" id="select-all" title="全选/取消全选所有符合筛选条件的文章">
                                </th>
                                <th width="5%">ID</th>
                                <th width="20%">标题</th>
                                <th width="22%">摘要</th>
                                <th width="10%">分类</th>
                                <th width="8%">状态</th>
                                <th width="12%">更新时间</th>
                                <th width="20%">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($articles as $article): ?>
                                <tr>
                                    <td><input type="checkbox" name="article_ids[]" value="<?php echo $article['id']; ?>" class="article-checkbox"></td>
                                    <td><?php echo $article['id']; ?></td>
                                    <td class="article-title"><?php echo $article['title']; ?></td>
                                    <td class="article-summary"><?php echo !empty($article['summary']) ? $article['summary'] : '<span class="text-muted">无摘要</span>'; ?></td>
                                    <td><span class="badge badge-info"><?php echo $article['category_name'] ?? '默认分类'; ?></span></td>
                                    <td>
                                        <?php if ($article['published']): ?>
                                            <span class="badge badge-success">已发布</span>
                                        <?php else: ?>
                                            <span class="badge badge-secondary">草稿</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo date('Y-m-d H:i', strtotime($article['update_time'])); ?></td>
                                    <td class="article-actions">
                                        <a href="article_preview.php?id=<?php echo $article['id']; ?>" class="btn btn-info btn-sm" title="预览" target="_blank"><i class="fas fa-eye"></i></a>
                                        <a href="article_edit.php?id=<?php echo $article['id']; ?>" class="btn btn-primary btn-sm" title="编辑"><i class="fas fa-edit"></i></a>
                                        <a href="javascript:void(0);" onclick="confirmDelete('确定要删除此文章吗？', function() { window.location.href='delete.php?id=<?php echo $article['id']; ?>&type=article'; })" class="btn btn-danger btn-sm" title="删除"><i class="fas fa-trash"></i></a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </form>
            
            <?php if ($total_pages > 1): ?>
                <div class="pagination-container">
                    <ul class="pagination">
                        <li class="page-item <?php echo $page <= 1 ? 'disabled' : ''; ?>">
                            <a class="page-link" href="articles.php?page=1<?php 
                                echo $category_filter > 0 ? '&category='.$category_filter : ''; 
                                echo $status_filter !== -1 ? '&status='.$status_filter : '';
                                echo !empty($search_query) ? '&search='.urlencode($search_query) : '';
                            ?>">首页</a>
                        </li>
                        <li class="page-item <?php echo $page <= 1 ? 'disabled' : ''; ?>">
                            <a class="page-link" href="articles.php?page=<?php echo max(1, $page - 1); ?><?php 
                                echo $category_filter > 0 ? '&category='.$category_filter : '';
                                echo $status_filter !== -1 ? '&status='.$status_filter : '';
                                echo !empty($search_query) ? '&search='.urlencode($search_query) : '';
                            ?>">上一页</a>
                        </li>
                        <li class="page-info">
                            <span class="page-text"><?php echo $page; ?>/<?php echo $total_pages; ?></span>
                        </li>
                        <li class="page-item <?php echo $page >= $total_pages ? 'disabled' : ''; ?>">
                            <a class="page-link" href="articles.php?page=<?php echo min($total_pages, $page + 1); ?><?php 
                                echo $category_filter > 0 ? '&category='.$category_filter : '';
                                echo $status_filter !== -1 ? '&status='.$status_filter : '';
                                echo !empty($search_query) ? '&search='.urlencode($search_query) : '';
                            ?>">下一页</a>
                        </li>
                        <li class="page-item <?php echo $page >= $total_pages ? 'disabled' : ''; ?>">
                            <a class="page-link" href="articles.php?page=<?php echo $total_pages; ?><?php 
                                echo $category_filter > 0 ? '&category='.$category_filter : '';
                                echo $status_filter !== -1 ? '&status='.$status_filter : '';
                                echo !empty($search_query) ? '&search='.urlencode($search_query) : '';
                            ?>">末页</a>
                        </li>
                    </ul>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>

    <div class="card help-card">
        <div class="card-header">
            <div class="card-title">文章管理说明</div>
            <div class="toggle-help"><i class="fas fa-chevron-down"></i></div>
        </div>
        <div class="help-content">
            <div class="help-grid">
                <div class="help-item">
                    <div class="help-icon"><i class="fas fa-plus-circle"></i></div>
                    <div class="help-text">
                        <h4>新增文章</h4>
                        <p>创建新的文章内容</p>
                    </div>
                </div>
                <div class="help-item">
                    <div class="help-icon"><i class="fas fa-edit"></i></div>
                    <div class="help-text">
                        <h4>编辑</h4>
                        <p>修改已有文章内容和状态</p>
                    </div>
                </div>
                <div class="help-item">
                    <div class="help-icon"><i class="fas fa-eye"></i></div>
                    <div class="help-text">
                        <h4>预览</h4>
                        <p>在新窗口中预览文章</p>
                    </div>
                </div>
                <div class="help-item">
                    <div class="help-icon"><i class="fas fa-trash"></i></div>
                    <div class="help-text">
                        <h4>删除</h4>
                        <p>永久删除文章（不可恢复）</p>
                    </div>
                </div>
                <div class="help-item">
                    <div class="help-icon"><i class="fas fa-file-import"></i></div>
                    <div class="help-text">
                        <h4>导入/导出</h4>
                        <p>批量导入或导出文章数据</p>
                    </div>
                </div>
                <div class="help-item">
                    <div class="help-icon"><i class="fas fa-filter"></i></div>
                    <div class="help-text">
                        <h4>筛选搜索</h4>
                        <p>按关键词、分类和状态筛选文章</p>
                    </div>
                </div>
                <div class="help-item">
                    <div class="help-icon"><i class="fas fa-tasks"></i></div>
                    <div class="help-text">
                        <h4>批量操作</h4>
                        <p>批量修改文章状态和分类</p>
                    </div>
                </div>
            </div>
            <div class="help-footer">
                <p>文章支持Markdown格式，可添加格式化文本、链接和图片等。新增摘要功能，未填写时自动使用文章前30个字作为摘要。</p>
            </div>
        </div>
    </div>
</div>

<style>
/* 文章管理页面专用样式 */
.article-dashboard {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.article-card, .help-card {
    margin-bottom: 0;
    box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
    border-radius: 4px;
    overflow: hidden;
    padding: 0;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.card-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #495057;
}

.card-actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

.batch-operation {
    display: flex;
    gap: 5px;
    align-items: center;
    margin-left: 15px;
}

.batch-operation select {
    width: 150px;
}

/* 选择控制按钮样式 */
.selection-controls {
    display: flex;
    gap: 5px;
    align-items: center;
}

.selection-info {
    display: flex;
    align-items: center;
}

.btn-outline-primary {
    color: #007bff;
    border-color: #007bff;
    background-color: transparent;
}

.btn-outline-primary:hover {
    color: #fff;
    background-color: #007bff;
    border-color: #007bff;
}

.btn-outline-secondary {
    color: #6c757d;
    border-color: #6c757d;
    background-color: transparent;
}

.btn-outline-secondary:hover {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d;
}

.filter-section {
    padding: 15px 20px;
    border-bottom: 1px solid #e9ecef;
    background-color: #fff;
}

.search-filter {
    width: 100%;
}

.filter-row {
    display: flex;
    align-items: center;
    gap: 10px;
}

.search-box {
    flex: 1;
    min-width: 200px;
}

.category-box, .status-box {
    width: 150px;
}

.filter-btn {
    width: auto;
}

.article-table {
    margin-bottom: 0;
    min-width: 100%;
    table-layout: fixed; /* 固定表格布局，提高移动端性能 */
}

/* 确保表格容器在移动端正确显示 */
.table-responsive {
    -webkit-overflow-scrolling: touch; /* iOS平滑滚动 */
    overflow-x: auto;
    overflow-y: hidden;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

/* 移动端表格容器优化 */
@media (max-width: 768px) {
    .table-responsive {
        margin: 0 -10px;
        border-radius: 0;
        box-shadow: none;
        border-top: 1px solid #e9ecef;
        border-bottom: 1px solid #e9ecef;
    }
}

.article-table th {
    font-size: 13px;
    font-weight: 600;
    padding: 12px 15px;
    background-color: #f1f3f5;
    border-top: none;
    white-space: nowrap;
}

/* 全选复选框样式 */
#select-all {
    cursor: pointer;
    transform: scale(1.1);
    margin: 0;
}

#select-all:indeterminate {
    background-color: #007bff;
    border-color: #007bff;
}

/* 文章复选框样式 */
.article-checkbox {
    cursor: pointer;
    transform: scale(1.1);
    margin: 0;
}

/* 复选框容器样式 */
.article-table th:first-child,
.article-table td:first-child {
    text-align: center;
    vertical-align: middle;
}

/* 操作列居中样式 */
.article-table th:last-child,
.article-table td:last-child {
    text-align: center;
    vertical-align: middle;
}

/* 选中行的高亮效果 */
.article-table tbody tr.selected {
    background-color: rgba(0, 123, 255, 0.05);
}

.article-table tbody tr.selected:hover {
    background-color: rgba(0, 123, 255, 0.1);
}

.article-table td {
    padding: 10px 15px;
    vertical-align: middle;
    font-size: 14px;
    min-height: 50px;
}

.article-table tbody tr {
    min-height: 50px;
}

.article-title {
    font-weight: 500;
    color: #333;
    text-decoration: none;
}

.article-title:hover {
    color: #007bff;
}

.article-summary {
    color: #6c757d;
    max-width: 250px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.article-actions {
    display: flex;
    gap: 5px;
    justify-content: center;
    align-items: center;
    height: 100%;
    min-height: 40px;
}

.article-actions .btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.badge {
    padding: 5px 10px;
    border-radius: 3px;
    font-weight: 500;
    font-size: 12px;
}

.badge-info {
    background-color: #17a2b8;
    color: white;
}

.badge-light {
    background-color: #f8f9fa;
    color: #495057;
    border: 1px solid #dee2e6;
}

/* 选中数量徽章样式 */
#selected-count {
    font-size: 11px;
    padding: 2px 6px;
    border-radius: 10px;
    margin-left: 5px !important;
}

.pagination-container {
    padding: 15px;
    display: flex;
    justify-content: center;
    border-top: 1px solid #e9ecef;
}

.pagination {
    display: flex;
    gap: 5px;
    list-style: none;
    padding: 0;
    margin: 0;
}

.page-item {
    display: inline-block;
}

.page-link {
    display: block;
    padding: 6px 12px;
    border-radius: 3px;
    text-decoration: none;
    color: #007bff;
    background-color: #fff;
    border: 1px solid #dee2e6;
    transition: all 0.2s;
}

.page-link:hover {
    color: #0056b3;
    background-color: #e9ecef;
    border-color: #dee2e6;
}

.page-item.disabled .page-link {
    color: #6c757d;
    pointer-events: none;
    cursor: not-allowed;
    background-color: #fff;
    border-color: #dee2e6;
}

.page-info {
    display: flex;
    align-items: center;
    margin: 0 8px;
}

.page-text {
    padding: 6px 12px;
    border-radius: 3px;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    color: #495057;
    font-size: 14px;
}

.help-card {
    background-color: white;
}

.toggle-help {
    cursor: pointer;
    color: #6c757d;
    transition: all 0.3s;
}

.toggle-help:hover {
    color: #007bff;
}

.help-content {
    padding: 15px 20px;
}

.help-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
    margin-bottom: 15px;
}

.help-item {
    display: flex;
    gap: 12px;
    align-items: center;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 4px;
    transition: all 0.2s;
}

.help-item:hover {
    background-color: #e9ecef;
    transform: translateY(-2px);
}

.help-icon {
    font-size: 20px;
    color: #007bff;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: rgba(0, 123, 255, 0.1);
}

.help-text h4 {
    margin: 0 0 5px 0;
    font-size: 14px;
    font-weight: 600;
}

.help-text p {
    margin: 0;
    font-size: 12px;
    color: #6c757d;
}

.help-footer {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
}

.help-footer p {
    margin: 0;
    font-size: 14px;
    color: #495057;
}

.alert {
    margin: 15px;
    border-radius: 4px;
}

@media (max-width: 768px) {
    .help-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .card-actions {
        flex-wrap: wrap;
    }

    .batch-operation {
        margin-left: 0;
        margin-top: 10px;
        width: 100%;
        flex-wrap: wrap;
    }

    .batch-operation select {
        flex: 1;
        min-width: 150px;
    }

    .selection-controls {
        margin-top: 10px;
        margin-left: 0 !important;
        flex-wrap: wrap;
        width: 100%;
    }

    .selection-controls .btn {
        margin-bottom: 5px;
    }

    .selection-info {
        margin-top: 5px;
        margin-left: 0 !important;
        width: 100%;
    }

    .article-summary {
        max-width: 150px;
    }

    /* 移动端表格优化 */
    .article-table {
        font-size: 13px;
    }

    .article-table th,
    .article-table td {
        padding: 8px 4px;
        vertical-align: middle;
    }

    .article-title {
        font-size: 13px;
        line-height: 1.3;
        word-break: break-word;
    }

    .badge {
        font-size: 10px;
        padding: 3px 6px;
    }

    .btn-sm {
        padding: 3px 6px;
        font-size: 11px;
    }

    .filter-row {
        flex-wrap: wrap;
    }

    .search-box {
        flex: 1 0 100%;
        margin-bottom: 10px;
    }

    .category-box, .status-box {
        flex: 1;
        width: auto;
    }

    .filter-btn {
        margin-top: 10px;
        width: 100%;
    }

    .filter-btn button {
        width: 100%;
    }
}

@media (max-width: 576px) {
    .help-grid {
        grid-template-columns: 1fr;
    }

    /* 移动端隐藏摘要列和状态列，保留标题列 */
    .article-table th:nth-child(4),
    .article-table td:nth-child(4),
    .article-table th:nth-child(6),
    .article-table td:nth-child(6) {
        display: none !important;
    }

    /* 重新分配列宽度，确保充分利用空间 */
    .article-table th:nth-child(1),
    .article-table td:nth-child(1) {
        width: 6% !important;
        min-width: 30px;
        padding: 8px 2px !important;
    }

    .article-table th:nth-child(2),
    .article-table td:nth-child(2) {
        width: 8% !important;
        min-width: 35px;
        font-size: 12px;
        padding: 8px 3px !important;
    }

    /* 标题列 - 充分利用可用空间 */
    .article-table th:nth-child(3),
    .article-table td:nth-child(3) {
        width: 45% !important;
        max-width: none !important;
        min-width: 120px;
        padding: 8px 6px !important;
    }

    .article-table th:nth-child(5),
    .article-table td:nth-child(5) {
        width: 12% !important;
        min-width: 50px;
        padding: 8px 3px !important;
    }

    .article-table th:nth-child(7),
    .article-table td:nth-child(7) {
        width: 14% !important;
        min-width: 60px;
        font-size: 11px;
        padding: 8px 3px !important;
    }

    .article-table th:nth-child(8),
    .article-table td:nth-child(8) {
        width: 15% !important;
        min-width: 65px;
        padding: 8px 2px !important;
    }

    /* 优化标题显示 - 移除限制，允许完整显示 */
    .article-title {
        font-size: 13px !important;
        line-height: 1.3 !important;
        word-break: break-word;
        word-wrap: break-word;
        white-space: normal !important;
        overflow: visible !important;
        text-overflow: unset !important;
        display: block !important;
        -webkit-line-clamp: unset !important;
        -webkit-box-orient: unset !important;
        max-height: none !important;
        padding: 2px 0;
    }

    /* 确保标题容器能够自适应高度 */
    .article-table td:nth-child(3) {
        vertical-align: top !important;
        height: auto !important;
        min-height: 40px;
    }

    /* 移动端表格整体优化 */
    .article-table {
        table-layout: fixed !important;
        width: 100% !important;
        font-size: 12px;
    }

    .article-table th,
    .article-table td {
        padding: 6px 4px !important;
        vertical-align: top !important;
        border-bottom: 1px solid #e9ecef;
    }

    /* 操作列保持垂直居中 */
    .article-table td:last-child {
        vertical-align: middle !important;
    }

    .article-table th {
        font-size: 11px !important;
        font-weight: 600;
        background-color: #f8f9fa !important;
        position: sticky;
        top: 0;
        z-index: 10;
    }

    /* 优化分类标签显示 */
    .badge {
        font-size: 9px !important;
        padding: 2px 4px !important;
        border-radius: 2px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 100%;
        display: inline-block;
    }

    /* 优化操作按钮 */
    .article-actions {
        display: flex;
        flex-direction: column;
        gap: 2px;
        align-items: center;
        justify-content: center;
        height: 100%;
        min-height: 40px;
    }

    /* 确保操作列在移动端也垂直居中 */
    .article-table td:nth-child(8) {
        vertical-align: middle !important;
    }

    .article-actions .btn {
        padding: 3px 4px !important;
        font-size: 10px !important;
        min-width: 28px;
        height: 24px;
        border-radius: 2px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .article-actions .btn i {
        font-size: 10px;
    }

    /* 时间显示优化 */
    .article-table td:nth-child(7) {
        font-size: 10px !important;
        line-height: 1.2;
        white-space: nowrap;
    }

    .card-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .card-actions {
        margin-top: 10px;
        width: 100%;
    }
}

/* 中等移动设备优化（480px以下） */
@media (max-width: 480px) {
    .article-table th:nth-child(3),
    .article-table td:nth-child(3) {
        width: 42% !important;
    }

    .article-title {
        font-size: 12px !important;
        line-height: 1.25 !important;
    }

    .article-table th:nth-child(7),
    .article-table td:nth-child(7) {
        width: 16% !important;
    }
}

/* 极小屏幕优化（360px以下） */
@media (max-width: 360px) {
    .article-table th:nth-child(2),
    .article-table td:nth-child(2) {
        display: none !important;
    }

    .article-table th:nth-child(7),
    .article-table td:nth-child(7) {
        display: none !important;
    }

    /* 重新分配剩余列的宽度 */
    .article-table th:nth-child(1),
    .article-table td:nth-child(1) {
        width: 8% !important;
    }

    .article-table th:nth-child(3),
    .article-table td:nth-child(3) {
        width: 55% !important;
    }

    .article-table th:nth-child(5),
    .article-table td:nth-child(5) {
        width: 17% !important;
    }

    .article-table th:nth-child(8),
    .article-table td:nth-child(8) {
        width: 20% !important;
    }

    /* 确保操作列在360px以下屏幕也垂直居中 */
    .article-table td:nth-child(8) {
        vertical-align: middle !important;
    }

    .article-title {
        font-size: 12px !important;
        line-height: 1.2 !important;
    }

    .article-actions .btn {
        padding: 2px 3px !important;
        font-size: 9px !important;
        min-width: 24px;
        height: 20px;
    }
}

/* 超小屏幕优化（320px以下） */
@media (max-width: 320px) {
    .article-table th:nth-child(5),
    .article-table td:nth-child(5) {
        display: none !important;
    }

    .article-table th:nth-child(3),
    .article-table td:nth-child(3) {
        width: 65% !important;
    }

    .article-table th:nth-child(8),
    .article-table td:nth-child(8) {
        width: 27% !important;
    }

    .article-actions {
        flex-direction: row;
        flex-wrap: wrap;
        gap: 1px;
        justify-content: center;
        align-items: center;
        height: 100%;
        min-height: 30px;
    }

    /* 确保操作列在极小屏幕也垂直居中 */
    .article-table td:nth-child(8) {
        vertical-align: middle !important;
    }

    .article-actions .btn {
        flex: 1;
        min-width: 20px;
        height: 18px;
        padding: 1px 2px !important;
        font-size: 8px !important;
    }

    .article-title {
        font-size: 11px !important;
        line-height: 1.15 !important;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 帮助卡片切换
    const toggleHelp = document.querySelector('.toggle-help');
    const helpContent = document.querySelector('.help-content');
    
    toggleHelp.addEventListener('click', function() {
        helpContent.style.display = helpContent.style.display === 'none' ? 'block' : 'none';
        toggleHelp.querySelector('i').classList.toggle('fa-chevron-down');
        toggleHelp.querySelector('i').classList.toggle('fa-chevron-up');
    });
    
    // 默认展开帮助
    helpContent.style.display = 'block';
    
    // 两级选择功能
    const selectAll = document.getElementById('select-all');
    const articleCheckboxes = document.querySelectorAll('.article-checkbox');
    const selectAllGlobal = document.getElementById('select-all-global');
    const clearSelection = document.getElementById('clear-selection');
    const selectionInfo = document.getElementById('selection-info');
    const selectionScope = document.getElementById('selection-scope');

    // 存储所有文章ID和选中状态
    let allArticleIds = [];
    let selectedArticleIds = new Set();
    let isGlobalSelection = false; // 标记是否为全局选择

    // 获取当前筛选条件
    function getCurrentFilters() {
        const urlParams = new URLSearchParams(window.location.search);
        return {
            category: urlParams.get('category') || '0',
            status: urlParams.get('status') || '-1',
            search: urlParams.get('search') || ''
        };
    }

    // 获取所有文章ID
    async function getAllArticleIds() {
        const filters = getCurrentFilters();

        try {
            const formData = new FormData();
            formData.append('action', 'get_all_article_ids');
            formData.append('category', filters.category);
            formData.append('status', filters.status);
            formData.append('search', filters.search);

            const response = await fetch('articles.php', {
                method: 'POST',
                body: formData
            });

            const data = await response.json();
            if (data.success) {
                allArticleIds = data.article_ids;
                return data.article_ids;
            }
        } catch (error) {
            console.error('获取文章ID失败:', error);
        }

        return [];
    }

    // 获取当前页面的文章ID
    function getCurrentPageArticleIds() {
        return Array.from(articleCheckboxes).map(checkbox => parseInt(checkbox.value));
    }

    // 更新选中数量显示和选择信息
    function updateSelectedCount() {
        const selectedCountElement = document.getElementById('selected-count');
        if (!selectedCountElement) return;

        const totalSelected = selectedArticleIds.size;
        const currentPageIds = getCurrentPageArticleIds();
        const currentPageSelected = currentPageIds.filter(id => selectedArticleIds.has(id)).length;

        if (totalSelected > 0) {
            // 显示选中数量
            if (isGlobalSelection && totalSelected === allArticleIds.length) {
                selectedCountElement.textContent = `${totalSelected} 篇（全部）`;
            } else if (totalSelected > currentPageIds.length) {
                selectedCountElement.textContent = `${totalSelected} 篇（跨页面）`;
            } else {
                selectedCountElement.textContent = `${totalSelected} 篇`;
            }
            selectedCountElement.style.display = 'inline';

            // 显示选择信息
            updateSelectionInfo();

            // 显示清除选择按钮
            clearSelection.style.display = 'inline-block';
        } else {
            selectedCountElement.style.display = 'none';
            selectionInfo.style.display = 'none';
            clearSelection.style.display = 'none';
            isGlobalSelection = false;
        }
    }

    // 更新选择范围信息
    function updateSelectionInfo() {
        const totalSelected = selectedArticleIds.size;
        const currentPageIds = getCurrentPageArticleIds();

        if (totalSelected > 0) {
            selectionInfo.style.display = 'inline-block';

            if (isGlobalSelection && totalSelected === allArticleIds.length) {
                selectionScope.textContent = '已选择所有符合条件的文章';
                selectionScope.className = 'text-success';
            } else if (totalSelected > currentPageIds.length) {
                selectionScope.textContent = '跨页面选择';
                selectionScope.className = 'text-info';
            } else {
                selectionScope.textContent = '当前页面选择';
                selectionScope.className = 'text-muted';
            }
        } else {
            selectionInfo.style.display = 'none';
        }
    }

    // 更新页面全选复选框状态（仅针对当前页面）
    function updateSelectAllState() {
        if (!selectAll) return;

        const currentPageIds = getCurrentPageArticleIds();
        const currentPageSelected = currentPageIds.filter(id => selectedArticleIds.has(id)).length;
        const currentPageTotal = currentPageIds.length;

        if (currentPageSelected === 0) {
            // 当前页面没有选中任何项
            selectAll.checked = false;
            selectAll.indeterminate = false;
        } else if (currentPageSelected === currentPageTotal) {
            // 当前页面全部选中
            selectAll.checked = true;
            selectAll.indeterminate = false;
        } else {
            // 当前页面部分选中
            selectAll.checked = false;
            selectAll.indeterminate = true;
        }

        // 更新选中数量显示
        updateSelectedCount();
    }

    // 同步当前页面复选框状态
    function syncCurrentPageCheckboxes() {
        articleCheckboxes.forEach(checkbox => {
            const articleId = parseInt(checkbox.value);
            checkbox.checked = selectedArticleIds.has(articleId);
        });
        updateRowHighlight();
    }

    // 更新行高亮效果
    function updateRowHighlight() {
        articleCheckboxes.forEach(checkbox => {
            const row = checkbox.closest('tr');
            if (checkbox.checked) {
                row.classList.add('selected');
            } else {
                row.classList.remove('selected');
            }
        });
    }

    // 页面全选复选框点击事件（仅选择当前页面）
    if (selectAll) {
        selectAll.addEventListener('change', function() {
            const shouldCheck = selectAll.checked;
            const currentPageIds = getCurrentPageArticleIds();

            if (shouldCheck) {
                // 页面全选：添加当前页面所有文章ID
                currentPageIds.forEach(id => selectedArticleIds.add(id));
                isGlobalSelection = false; // 重置全局选择标记
            } else {
                // 取消页面全选：移除当前页面所有文章ID
                currentPageIds.forEach(id => selectedArticleIds.delete(id));
            }

            // 清除半选中状态
            selectAll.indeterminate = false;

            // 同步当前页面复选框状态
            syncCurrentPageCheckboxes();
            updateSelectAllState();
        });
    }

    // 全局选择按钮点击事件
    if (selectAllGlobal) {
        selectAllGlobal.addEventListener('click', async function() {
            // 获取所有文章ID
            if (allArticleIds.length === 0) {
                await getAllArticleIds();
            }

            // 选择所有文章
            allArticleIds.forEach(id => selectedArticleIds.add(id));
            isGlobalSelection = true;

            // 更新按钮文本
            selectAllGlobal.innerHTML = '<i class="fas fa-check"></i> 已选择全部';
            selectAllGlobal.disabled = true;

            // 同步当前页面状态
            syncCurrentPageCheckboxes();
            updateSelectAllState();
        });
    }

    // 清除选择按钮点击事件
    if (clearSelection) {
        clearSelection.addEventListener('click', function() {
            // 清空所有选择
            selectedArticleIds.clear();
            isGlobalSelection = false;

            // 重置全局选择按钮
            selectAllGlobal.innerHTML = '<i class="fas fa-globe"></i> 选择所有文章';
            selectAllGlobal.disabled = false;

            // 同步当前页面状态
            syncCurrentPageCheckboxes();
            updateSelectAllState();
        });
    }

    // 单个复选框点击事件
    articleCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const articleId = parseInt(checkbox.value);

            if (checkbox.checked) {
                selectedArticleIds.add(articleId);
            } else {
                selectedArticleIds.delete(articleId);
                // 如果取消选择，重置全局选择状态
                if (isGlobalSelection) {
                    isGlobalSelection = false;
                    selectAllGlobal.innerHTML = '<i class="fas fa-globe"></i> 选择所有文章';
                    selectAllGlobal.disabled = false;
                }
            }

            updateSelectAllState();
            updateRowHighlight();
        });
    });

    // 初始化功能
    async function initializeSelectAll() {
        // 获取所有文章ID（用于全局选择功能）
        await getAllArticleIds();

        // 根据当前页面已选中的复选框初始化选中状态
        articleCheckboxes.forEach(checkbox => {
            if (checkbox.checked) {
                selectedArticleIds.add(parseInt(checkbox.value));
            }
        });

        // 更新状态
        updateSelectAllState();
        updateRowHighlight();
    }

    // 启动初始化
    initializeSelectAll();

    // 移动端优化：添加触摸友好的交互
    if (window.innerWidth <= 768) {
        // 为移动端添加更大的点击区域
        const tableRows = document.querySelectorAll('.article-table tbody tr');
        tableRows.forEach(row => {
            row.style.minHeight = '50px';
        });

        // 优化移动端的按钮点击
        const actionButtons = document.querySelectorAll('.article-actions .btn');
        actionButtons.forEach(btn => {
            btn.style.minWidth = '36px';
            btn.style.minHeight = '36px';
        });
    }

    // 监听屏幕方向变化
    window.addEventListener('orientationchange', function() {
        setTimeout(function() {
            // 重新计算表格布局
            const table = document.querySelector('.article-table');
            if (table) {
                table.style.display = 'none';
                table.offsetHeight; // 强制重排
                table.style.display = '';
            }
        }, 100);
    });
    
    // 批量操作表单提交
    const batchForm = document.getElementById('batch-form');
    const batchActionHidden = document.getElementById('batch_action_hidden');
    const batchActionTop = document.getElementById('batch_action_top');
    const applyBatchTop = document.getElementById('apply-batch-top');
    
    if (applyBatchTop && batchForm) {
        applyBatchTop.addEventListener('click', function() {
            const action = batchActionTop.value;

            if (!action) {
                alert('请选择一个批量操作');
                return;
            }

            if (selectedArticleIds.size === 0) {
                alert('请至少选择一篇文章');
                return;
            }

            if (action === 'delete') {
                if (!confirm(`确定要删除所选的 ${selectedArticleIds.size} 篇文章吗？此操作不可恢复！`)) {
                    return;
                }
            }

            // 清除现有的隐藏输入
            const existingInputs = batchForm.querySelectorAll('input[name="article_ids[]"]');
            existingInputs.forEach(input => input.remove());

            // 添加所有选中的文章ID作为隐藏输入
            selectedArticleIds.forEach(articleId => {
                const hiddenInput = document.createElement('input');
                hiddenInput.type = 'hidden';
                hiddenInput.name = 'article_ids[]';
                hiddenInput.value = articleId;
                batchForm.appendChild(hiddenInput);
            });

            batchActionHidden.value = action;
            batchForm.submit();
        });
    }
});
</script>

<?php
// 包含底部
include 'includes/footer.php';
?>