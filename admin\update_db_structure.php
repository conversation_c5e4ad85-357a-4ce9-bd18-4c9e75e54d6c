<?php
require_once 'config.php';
require_once 'includes/db.php';

// 显示操作结果的函数
function showResult($message, $isError = false) {
    echo '<div style="padding: 10px; margin: 10px 0; border-radius: 5px; ' . 
         ($isError ? 'background-color: #f8d7da; color: #721c24;' : 'background-color: #d4edda; color: #155724;') . 
         '">' . $message . '</div>';
}

// 开始更新数据库结构
echo '<h1>数据库结构更新</h1>';

try {
    // 1. 在game_records表中添加game_type字段
    $sql = "ALTER TABLE game_records 
            ADD COLUMN game_type ENUM('暗杀', '死斗', '盟主') AFTER player_id,
            ADD COLUMN virtual_ip VARCHAR(50) AFTER game_type,
            ADD COLUMN player_rank VARCHAR(50) AFTER virtual_ip,
            ADD INDEX (game_type)";
    
    if ($db->query($sql)) {
        showResult("成功添加字段: game_type, virtual_ip, player_rank 到 game_records 表");
    } else {
        showResult("添加字段失败", true);
    }

    // 2. 从games表和players表中同步数据到game_records表
    $sql = "UPDATE game_records gr
            JOIN games g ON gr.game_id = g.id
            JOIN players p ON gr.player_id = p.id
            SET gr.game_type = g.game_type,
                gr.virtual_ip = p.virtual_ip,
                gr.player_rank = p.player_rank";
    
    if ($db->query($sql)) {
        showResult("成功将数据从games表和players表同步到game_records表");
    } else {
        showResult("数据同步失败", true);
    }

    // 3. 创建暗杀游戏视图
    $sql = "CREATE OR REPLACE VIEW assassination_games AS
            SELECT g.id as game_id, g.game_type, p.nickname, gr.virtual_ip, gr.player_rank,
                   gr.kills, gr.deaths, gr.team, gr.wins, gr.losses
            FROM games g
            JOIN game_records gr ON g.id = gr.game_id
            JOIN players p ON gr.player_id = p.id
            WHERE g.game_type = '暗杀'";
    
    if ($db->query($sql)) {
        showResult("成功创建暗杀游戏视图 (assassination_games)");
    } else {
        showResult("创建暗杀游戏视图失败", true);
    }

    // 4. 创建死斗游戏视图
    $sql = "CREATE OR REPLACE VIEW deathmatch_games AS
            SELECT g.id as game_id, g.game_type, p.nickname, gr.virtual_ip, gr.player_rank,
                   gr.kills, gr.deaths, gr.team, gr.wins, gr.losses
            FROM games g
            JOIN game_records gr ON g.id = gr.game_id
            JOIN players p ON gr.player_id = p.id
            WHERE g.game_type = '死斗'";
    
    if ($db->query($sql)) {
        showResult("成功创建死斗游戏视图 (deathmatch_games)");
    } else {
        showResult("创建死斗游戏视图失败", true);
    }

    // 5. 创建盟主游戏视图
    $sql = "CREATE OR REPLACE VIEW boss_games AS
            SELECT g.id as game_id, g.game_type, p.nickname, gr.virtual_ip, gr.player_rank,
                   gr.kills, gr.deaths, gr.team, gr.wins, gr.losses
            FROM games g
            JOIN game_records gr ON g.id = gr.game_id
            JOIN players p ON gr.player_id = p.id
            WHERE g.game_type = '盟主'";
    
    if ($db->query($sql)) {
        showResult("成功创建盟主游戏视图 (boss_games)");
    } else {
        showResult("创建盟主游戏视图失败", true);
    }

    echo '<h2>数据库结构更新完成</h2>';
    echo '<p>现在可以通过三个视图访问不同游戏类型的数据：</p>';
    echo '<ul>';
    echo '<li><strong>assassination_games</strong> - 暗杀游戏数据</li>';
    echo '<li><strong>deathmatch_games</strong> - 死斗游戏数据</li>';
    echo '<li><strong>boss_games</strong> - 盟主游戏数据</li>';
    echo '</ul>';
    
    echo '<p>示例查询：</p>';
    echo '<pre>SELECT * FROM assassination_games WHERE nickname LIKE "%玩家%";</pre>';
    
} catch (Exception $e) {
    showResult("发生错误: " . $e->getMessage(), true);
}
?> 