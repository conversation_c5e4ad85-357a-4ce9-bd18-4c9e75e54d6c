Page({
  data: {
    animateClass: '',
    loadingClass: '',
    loadingProgress: 0
  },

  onLoad: function() {
    // 添加logo闪烁效果
    this.glitchEffect();
    // 启动加载动画
    this.startLoading();
  },

  glitchEffect: function() {
    // 随机添加毛刺效果
    this.glitchTimer = setInterval(() => {
      if (Math.random() > 0.8) {
        this.setData({
          animateClass: 'glitch'
        });
        setTimeout(() => {
          this.setData({
            animateClass: ''
          });
        }, 200);
      }
    }, 500);
  },

  startLoading: function() {
    this.setData({
      loadingClass: 'loading'
    });
    
    // 2秒后跳转到主页
    setTimeout(() => {
      wx.switchTab({
        url: '/pages/ranking/ranking'
      });
    }, 2000);
  },

  onUnload: function() {
    // 清理定时器
    if (this.glitchTimer) {
      clearInterval(this.glitchTimer);
    }
  }
}); 