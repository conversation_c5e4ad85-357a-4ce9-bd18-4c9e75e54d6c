<?php
require_once 'config.php';
require_once 'includes/db.php';
require_once 'includes/utils.php';

// 包含头部
include 'includes/header.php';
?>

<div class="main-content">
    <div class="container">
<?php

// 获取当前API密钥
$sql = "SELECT * FROM api_settings WHERE is_active = 1 ORDER BY id DESC LIMIT 1";
$api_setting = $db->getRow($sql);

// 如果没有找到API设置，使用配置文件中的默认值
if (!$api_setting) {
    $api_setting = [
        'api_key' => API_KEY,
        'created_at' => date('Y-m-d H:i:s'),
        'last_used' => null,
        'is_active' => 1
    ];
}

// 获取网站根URL
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
$host = $_SERVER['HTTP_HOST'];
$base_url = $protocol . $host;

// 获取网站基础目录
$script_name = $_SERVER['SCRIPT_NAME'];
$base_dir = '';
if (strpos($script_name, '/admin/') !== false) {
    $base_dir = substr($script_name, 0, strpos($script_name, '/admin/'));
}

// 检查特殊情况：基础目录为空但实际有子目录
if (empty($base_dir) && isset($_SERVER['DOCUMENT_ROOT'])) {
    $current_dir = dirname(__DIR__);
    $document_root = $_SERVER['DOCUMENT_ROOT'];
    
    // 如果当前目录不等于文档根目录，说明可能有子目录
    if ($current_dir != $document_root) {
        // 尝试从当前目录提取子目录
        $relative_path = str_replace($document_root, '', $current_dir);
        $relative_path = str_replace('\\', '/', $relative_path); // 修正Windows路径
        
        if (!empty($relative_path)) {
            $base_dir = $relative_path;
        }
    }
}

// 完整基础URL包含基础目录
$base_full_url = $base_url . $base_dir;

// 调试信息
$debug_info = [];
$debug_enabled = false; // 设置为false关闭调试模式
$debug_info[] = "主机: $host";
$debug_info[] = "脚本路径: $script_name";
$debug_info[] = "基础目录: $base_dir";
$debug_info[] = "完整基础URL: $base_full_url";

// API端点列表
$api_endpoints = [
    'players' => [
        'title' => '获取所有玩家列表',
        'params' => []
    ],
    'player' => [
        'title' => '获取特定玩家详情',
        'params' => [
            'nickname' => [
                'type' => 'text',
                'label' => '玩家昵称',
                'required' => true
            ]
        ]
    ],
    'games' => [
        'title' => '获取所有游戏场次列表',
        'params' => []
    ],
    'game' => [
        'title' => '获取特定游戏场次详情',
        'params' => [
            'id' => [
                'type' => 'number',
                'label' => '游戏ID',
                'required' => true
            ]
        ]
    ],
    'ranks' => [
        'title' => '获取军衔统计数据',
        'params' => []
    ],
    'announcements' => [
        'title' => '获取公告列表',
        'params' => []
    ],
    'announcement' => [
        'title' => '获取特定公告详情',
        'params' => [
            'id' => [
                'type' => 'number',
                'label' => '公告ID',
                'required' => true
            ]
        ]
    ],
    'articles' => [
        'title' => '获取文章列表',
        'params' => []
    ],
    'article' => [
        'title' => '获取特定文章详情',
        'params' => [
            'id' => [
                'type' => 'number',
                'label' => '文章ID',
                'required' => true
            ]
        ]
    ],
    'info' => [
        'title' => '获取API信息和可用端点列表',
        'params' => []
    ]
];

// 初始化变量
$selected_endpoint = '';
$endpoint_params = [];
$api_response = null;
$api_url = '';
$raw_response = '';

// 检查API文件是否存在
$api_file_status = [];
$possible_api_locations = [
    __DIR__ . '/api.php',  // 当前目录
    dirname(__DIR__) . '/api.php',  // 上层目录
    __DIR__ . '/../api.php',  // 相对路径上层目录
];

foreach ($possible_api_locations as $location) {
    if (file_exists($location)) {
        $api_file_status[] = [
            'path' => $location,
            'exists' => true,
            'readable' => is_readable($location)
        ];
    } else {
        $api_file_status[] = [
            'path' => $location,
            'exists' => false,
            'readable' => false
        ];
    }
}

// 添加便利函数
function canCreateFile($path) {
    $dir = dirname($path);
    return is_dir($dir) && is_writable($dir);
}

// 检测并修复路径问题
function detectPathIssues($url) {
    // 检查常见子目录名称
    $common_subdirs = ['BDLX', 'www', 'public_html', 'htdocs', 'web'];
    $url_parts = parse_url($url);
    
    if (!isset($url_parts['path']) || $url_parts['path'] == '/') {
        // 测试常见子目录
        $possible_paths = [];
        foreach ($common_subdirs as $dir) {
            $possible_paths[] = '/' . $dir;
        }
        return $possible_paths;
    }
    
    return [];
}

// 获取网站根目录物理路径
$root_directory = realpath($_SERVER['DOCUMENT_ROOT']);
$admin_dir = __DIR__;
$parent_dir = dirname($admin_dir);

// 检查是否可以在网站根目录创建api.php
$can_create_api_file = canCreateFile($parent_dir . '/api.php');

// 处理表单提交
if (isset($_POST['endpoint']) && !empty($_POST['endpoint'])) {
    $selected_endpoint = $_POST['endpoint'];
    
    // 构建API URL - 尝试多种可能的路径
    $possible_api_paths = [
        '/api.php', // 根目录
        '/admin/api.php', // admin目录
        '../api.php', // 上级目录
        '/api/index.php', // API目录
        '/api/', // API目录默认索引
        '/', // 网站根路由处理
    ];
    
    // 如果检测到BDLX目录，特别添加这个路径
    if (strpos($base_dir, '/BDLX') !== false || strpos($base_dir, '/bdlx') !== false) {
        $possible_api_paths = array_merge([
            '/BDLX/api.php',
            '/BDLX/admin/api.php',
            '/bdlx/api.php',
            '/bdlx/admin/api.php'
        ], $possible_api_paths);
        $debug_info[] = "检测到BDLX目录，添加特定路径尝试";
    }
    
    // 检测可能的路径问题
    $possible_base_dirs = detectPathIssues($base_full_url);
    if (!empty($possible_base_dirs)) {
        $debug_info[] = "检测到可能的子目录问题，尝试常见子目录路径";
        foreach ($possible_base_dirs as $possible_dir) {
            $possible_api_paths[] = $possible_dir . '/api.php';
            $debug_info[] = "添加可能的路径: $possible_dir/api.php";
        }
    }
    
    $api_url = '';
    $debug_info[] = "基础URL: $base_url";
    $debug_info[] = "完整基础URL(含目录): $base_full_url";
    
    // 添加API密钥
    $api_key_param = '&api_key=' . $api_setting['api_key'];
    
    // 添加其他参数
    $other_params = '';
    if (isset($api_endpoints[$selected_endpoint]['params']) && !empty($api_endpoints[$selected_endpoint]['params'])) {
        foreach ($api_endpoints[$selected_endpoint]['params'] as $param_name => $param_info) {
            if (isset($_POST[$param_name]) && !empty($_POST[$param_name])) {
                $param_value = Utils::sanitizeInput($_POST[$param_name]);
                $other_params .= '&' . $param_name . '=' . urlencode($param_value);
                $endpoint_params[$param_name] = $param_value;
            } elseif ($param_info['required']) {
                // 如果是必需参数但没有提供，则不发送请求
                $api_url = '';
                $debug_info[] = "缺少必要参数: $param_name";
                break;
            }
        }
    }
    
    // 如果所有必需参数都具备，尝试所有可能的路径
    if ($other_params !== null) {
        foreach ($possible_api_paths as $api_path) {
            // 正确处理相对路径的URL构建
            $test_url = '';
            if (strpos($api_path, '../') === 0) {
                // 对于相对路径，需要特殊处理
                $parent_url = dirname($base_full_url);
                $relative_path = substr($api_path, 3); // 移除'../'
                $test_url = $parent_url . '/' . $relative_path;
            } else {
                // 对于绝对路径，使用完整基础URL
                $test_url = $base_full_url . $api_path;
            }
            $test_url .= '?path=' . $selected_endpoint . $api_key_param . $other_params;
            
            $debug_info[] = "尝试API路径: $test_url";
            
            // 使用curl测试此URL是否可访问
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $test_url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($ch, CURLOPT_NOBODY, true); // 只检查头部
            curl_setopt($ch, CURLOPT_TIMEOUT, 5); // 设置超时时间，避免长时间等待
            
            curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            $debug_info[] = "路径 $api_path 返回状态码: $http_code";
            
            // 如果返回的不是404，则可能是正确的路径
            if ($http_code != 404 && $http_code != 0) {
                $api_url = $test_url;
                $debug_info[] = "找到有效API路径: $api_path (状态码: $http_code)";
                break;
            }
        }
        
        // 如果所有路径都是404，使用第一个路径但显示警告
        if (empty($api_url)) {
            $api_url = $base_full_url . $possible_api_paths[0] . '?path=' . $selected_endpoint . $api_key_param . $other_params;
            $debug_info[] = "警告: 未找到有效的API路径，使用默认路径";
        }
    }
    
    // 发送API请求
    if (!empty($api_url)) {
        $debug_info[] = "准备请求URL: $api_url";
        
        // 设置是否已尝试API请求
        $api_requested = false;
        
        // 函数：使用CURL发送API请求
        function sendCurlRequest($url, &$debug_info) {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10); // 10秒超时
            
            $response = curl_exec($ch);
            $curl_info = curl_getinfo($ch);
            $curl_error = curl_error($ch);
            
            $debug_info[] = "CURL状态码: " . $curl_info['http_code'];
            if (!empty($curl_error)) {
                $debug_info[] = "CURL错误: $curl_error";
            }
            
            curl_close($ch);
            
            return [
                'response' => $response,
                'http_code' => $curl_info['http_code'],
                'error' => $curl_error
            ];
        }
        
        // 首先尝试使用CURL请求
        $debug_info[] = "尝试使用CURL";
        $curl_result = sendCurlRequest($api_url, $debug_info);
        $raw_response = $curl_result['response'];
        $api_requested = true;
        
        // 如果CURL请求失败，尝试使用file_get_contents
        if ($raw_response === false || empty($raw_response)) {
            $debug_info[] = "CURL请求失败，尝试使用file_get_contents";
            
            // 使用file_get_contents尝试
            $context_options = [
                'ssl' => [
                    'verify_peer' => false,
                    'verify_peer_name' => false,
                ],
                'http' => [
                    'timeout' => 10,
                    'ignore_errors' => true
                ]
            ];
            $context = stream_context_create($context_options);
            
            try {
                $raw_response = @file_get_contents($api_url, false, $context);
                $debug_info[] = "file_get_contents请求完成";
                
                if ($raw_response === false) {
                    $error = error_get_last();
                    $debug_info[] = "file_get_contents错误: " . ($error ? $error['message'] : '未知错误');
                }
            } catch (Exception $e) {
                $debug_info[] = "file_get_contents异常: " . $e->getMessage();
            }
        }
        
        // 处理响应
        if ($raw_response) {
            $debug_info[] = "收到原始响应: " . substr($raw_response, 0, 100) . (strlen($raw_response) > 100 ? '...' : '');
            
            // 检查响应是否为HTML (通常表示404或其他错误页面)
            if (strpos($raw_response, '<!DOCTYPE') !== false || strpos($raw_response, '<html') !== false) {
                $debug_info[] = "收到HTML响应而非JSON，可能是服务器错误或404页面";
                $api_response = [
                    'status' => 'error',
                    'message' => '收到HTML错误页面而非API响应，请检查API文件路径是否正确',
                    'data' => null
                ];
            } else {
                // 尝试解析JSON
                $api_response = json_decode($raw_response, true);
                
                if ($api_response === null && json_last_error() !== JSON_ERROR_NONE) {
                    $debug_info[] = "JSON解析错误: " . json_last_error_msg();
                    $debug_info[] = "原始响应内容: " . htmlspecialchars($raw_response);
                    $api_response = [
                        'status' => 'error',
                        'message' => 'JSON解析失败: ' . json_last_error_msg() . '。请检查API是否返回了有效的JSON格式',
                        'data' => null
                    ];
                }
            }
        } else {
            $debug_info[] = "没有收到响应";
            
            // 提供更有帮助的错误信息
            $error_message = '未收到响应';
            
            if ($curl_result['http_code'] === 0) {
                $error_message .= '，无法连接到服务器。请检查URL格式是否正确。';
            } elseif ($curl_result['http_code'] === 404) {
                $error_message .= '，API文件不存在(404错误)。请确认api.php文件已正确创建。';
            } else {
                $error_message .= '，请检查API是否正常运行。';
            }
            
            if (!empty($curl_result['error'])) {
                $error_message .= ' CURL错误: ' . $curl_result['error'];
            }
            
            $api_response = [
                'status' => 'error',
                'message' => $error_message,
                'data' => null
            ];
        }
    }
} elseif (isset($_POST['create_api_file']) && $_POST['create_api_file'] == 1) {
    // 处理自动创建API文件请求
    $api_template = <<<'EOT'
<?php
// API处理脚本
header('Content-Type: application/json');

// 引入必要的文件
require_once __DIR__ . '/admin/config.php';
require_once __DIR__ . '/admin/includes/db.php';
require_once __DIR__ . '/admin/includes/utils.php';

// 初始化响应数组
$response = [
    'status' => 'error',
    'message' => '未知错误',
    'data' => null
];

// 检查API密钥
$api_key = isset($_GET['api_key']) ? $_GET['api_key'] : '';
$path = isset($_GET['path']) ? $_GET['path'] : '';

// 验证API密钥
$sql = "SELECT * FROM api_settings WHERE api_key = ? AND is_active = 1";
$valid_key = $db->getRow($sql, [$api_key]);

if (!$valid_key && $api_key != API_KEY) {
    $response['message'] = 'API密钥无效';
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit;
}

// 更新最后使用时间
if ($valid_key) {
    $db->query("UPDATE api_settings SET last_used = NOW() WHERE id = ?", [$valid_key['id']]);
}

// 根据路径处理请求
switch ($path) {
    case 'players':
        // 获取所有玩家
        $players = $db->getAll("SELECT * FROM players ORDER BY id DESC");
        $response = [
            'status' => 'success',
            'message' => '获取玩家列表成功',
            'data' => $players
        ];
        break;
        
    case 'player':
        // 获取特定玩家
        $nickname = isset($_GET['nickname']) ? $_GET['nickname'] : '';
        if (empty($nickname)) {
            $response['message'] = '玩家昵称不能为空';
        } else {
            $player = $db->getRow("SELECT * FROM players WHERE nickname = ?", [$nickname]);
            if ($player) {
                $response = [
                    'status' => 'success',
                    'message' => '获取玩家信息成功',
                    'data' => $player
                ];
            } else {
                $response['message'] = '找不到该玩家';
            }
        }
        break;
    
    // 可以添加更多端点处理
    
    case 'info':
        // 返回API信息
        $endpoints = [
            'players' => '获取所有玩家列表',
            'player' => '获取特定玩家详情 (参数: nickname)',
            // 添加其他端点
        ];
        
        $response = [
            'status' => 'success',
            'message' => 'API信息',
            'data' => [
                'version' => '1.0',
                'endpoints' => $endpoints
            ]
        ];
        break;
        
    default:
        $response['message'] = '无效的API路径';
        break;
}

// 输出JSON响应
echo json_encode($response, JSON_UNESCAPED_UNICODE);
EOT;

    // 获取正确的API路径
    $api_file_path = $parent_dir . '/api.php';
    
    // 修正模板中的路径引用
    $script_dir = dirname($_SERVER['SCRIPT_NAME']);
    $relative_admin_path = '';
    
    // 如果脚本在子目录中（如BDLX/admin/），需要调整相对路径
    if (strpos($script_dir, '/admin') !== false) {
        $relative_admin_path = 'admin/';
    }
    
    // 替换模板中的路径
    $api_template = str_replace(
        "require_once __DIR__ . '/admin/",
        "require_once __DIR__ . '/$relative_admin_path",
        $api_template
    );
    
    // 尝试创建API文件
    $create_result = file_put_contents($api_file_path, $api_template);
    
    if ($create_result !== false) {
        $create_message = [
            'status' => 'success',
            'message' => 'API文件已成功创建在: ' . $api_file_path
        ];
    } else {
        $create_message = [
            'status' => 'error',
            'message' => '无法创建API文件。请检查目录权限。'
        ];
    }
}
?>

<h1 class="page-title">API测试工具</h1>

<?php if (isset($create_message)): ?>
<div class="alert alert-<?php echo $create_message['status'] === 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show" role="alert">
    <?php echo $create_message['message']; ?>
    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
        <span aria-hidden="true">&times;</span>
    </button>
</div>
<?php endif; ?>

<div class="row">
    <div class="col-md-4">
        <div class="card">
            <div class="card-title">选择API端点</div>
            <form method="post" action="" id="api-test-form">
                <div class="form-group">
                    <label for="endpoint" class="form-label">API端点</label>
                    <select id="endpoint" name="endpoint" class="form-control" required>
                        <option value="">-- 选择API端点 --</option>
                        <?php foreach ($api_endpoints as $endpoint => $info): ?>
                            <option value="<?php echo $endpoint; ?>" <?php echo ($selected_endpoint === $endpoint) ? 'selected' : ''; ?>>
                                <?php echo $info['title']; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div id="params-container">
                    <?php if ($selected_endpoint && isset($api_endpoints[$selected_endpoint]['params'])): ?>
                        <?php foreach ($api_endpoints[$selected_endpoint]['params'] as $param_name => $param_info): ?>
                            <div class="form-group">
                                <label for="<?php echo $param_name; ?>" class="form-label">
                                    <?php echo $param_info['label']; ?>
                                    <?php if ($param_info['required']): ?><span class="text-danger">*</span><?php endif; ?>
                                </label>
                                <input 
                                    type="<?php echo $param_info['type']; ?>" 
                                    id="<?php echo $param_name; ?>" 
                                    name="<?php echo $param_name; ?>" 
                                    class="form-control"
                                    value="<?php echo isset($endpoint_params[$param_name]) ? $endpoint_params[$param_name] : ''; ?>"
                                    <?php echo $param_info['required'] ? 'required' : ''; ?>
                                >
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
                
                <div class="form-group">
                    <button type="submit" class="btn btn-primary">发送请求</button>
                </div>
            </form>
        </div>
        
        <div class="card mt-3">
            <div class="card-title">API密钥</div>
            <div class="api-key-info">
                <div class="input-group">
                    <input type="text" id="api_key" class="form-control" value="<?php echo $api_setting['api_key']; ?>" readonly>
                    <button type="button" class="btn btn-secondary" onclick="copyApiKey()">复制</button>
                </div>
                <p class="mt-2"><small>API密钥将自动添加到所有请求中</small></p>
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <div class="card">
            <div class="card-title">API响应</div>
            
            <?php if ($api_url): ?>
                <div class="api-url">
                    <strong>请求URL:</strong>
                    <code><?php echo htmlspecialchars($api_url); ?></code>
                </div>
            <?php endif; ?>
            
            <?php if ($api_response): ?>
                <div class="api-response">
                    <div class="response-status <?php echo $api_response['status'] === 'success' ? 'text-success' : 'text-danger'; ?>">
                        <strong>状态:</strong> <?php echo $api_response['status']; ?>
                    </div>
                    
                    <div class="response-message">
                        <strong>消息:</strong> <?php echo $api_response['message']; ?>
                    </div>
                    
                    <?php if (isset($api_response['data']) && $api_response['data']): ?>
                        <div class="response-data">
                            <strong>数据:</strong>
                            <pre id="json-response"><?php echo json_encode($api_response['data'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE); ?></pre>
                            <button type="button" class="btn btn-sm btn-secondary mt-2" onclick="copyResponseJson()">复制JSON</button>
                        </div>
                    <?php endif; ?>
                </div>
            <?php elseif (!empty($raw_response)): ?>
                <div class="api-response">
                    <div class="response-raw">
                        <strong>原始响应:</strong>
                        <pre><?php echo htmlspecialchars($raw_response); ?></pre>
                    </div>
                </div>
            <?php else: ?>
                <div class="api-response-placeholder">
                    <p class="text-muted">选择API端点并发送请求以查看响应</p>
                </div>
            <?php endif; ?>
            
            <?php if ($debug_enabled && !empty($debug_info)): ?>
                <div class="debug-info mt-3">
                    <div class="card-title">调试信息</div>
                    <pre class="debug-log"><?php echo htmlspecialchars(implode("\n", $debug_info)); ?></pre>
                </div>
            <?php endif; ?>
        </div>
        
        <div class="card mt-3">
            <div class="card-title">使用说明</div>
            <div class="card-body">
                <ol>
                    <li>从左侧下拉菜单中选择要测试的API端点</li>
                    <li>如果该端点需要参数，填写必要的参数值</li>
                    <li>点击"发送请求"按钮发送API请求</li>
                    <li>查看右侧面板中的API响应结果</li>
                </ol>
                <p><strong>提示:</strong> 此工具将自动添加API密钥到所有请求中，无需手动添加</p>
                <p><strong>常见问题解决:</strong></p>
                <ul>
                    <li>如果请求返回404错误，请确保API文件(api.php)位于正确的位置</li>
                    <li>确保API文件返回有效的JSON格式数据</li>
                    <li>检查API密钥是否有效</li>
                </ul>
                
                <div class="mt-3">
                    <h5>API文件检查</h5>
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>路径</th>
                                <th>存在</th>
                                <th>可读</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($api_file_status as $status): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($status['path']); ?></td>
                                <td><?php echo $status['exists'] ? '<span class="text-success">是</span>' : '<span class="text-danger">否</span>'; ?></td>
                                <td><?php echo $status['readable'] ? '<span class="text-success">是</span>' : '<span class="text-danger">否</span>'; ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                    
                    <?php if (!array_column($api_file_status, 'exists')): ?>
                    <div class="alert alert-warning">
                        没有在常见位置找到API文件。请确认api.php文件已创建并放置在正确位置。
                    </div>
                    
                    <div class="mt-3">
                        <h5>创建API文件</h5>
                        <p>您可以使用下面的模板快速创建一个基本的API文件。将以下代码保存为api.php文件并放置在网站根目录：</p>
                        <pre class="bg-light p-3" style="max-height: 200px; overflow-y: auto;">
&lt;?php
// API处理脚本
header('Content-Type: application/json');

// 引入必要的文件
require_once __DIR__ . '/admin/config.php';
require_once __DIR__ . '/admin/includes/db.php';
require_once __DIR__ . '/admin/includes/utils.php';

// 初始化响应数组
$response = [
    'status' => 'error',
    'message' => '未知错误',
    'data' => null
];

// 检查API密钥
$api_key = isset($_GET['api_key']) ? $_GET['api_key'] : '';
$path = isset($_GET['path']) ? $_GET['path'] : '';

// 验证API密钥
$sql = "SELECT * FROM api_settings WHERE api_key = ? AND is_active = 1";
$valid_key = $db->getRow($sql, [$api_key]);

if (!$valid_key && $api_key != API_KEY) {
    $response['message'] = 'API密钥无效';
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit;
}

// 更新最后使用时间
if ($valid_key) {
    $db->query("UPDATE api_settings SET last_used = NOW() WHERE id = ?", [$valid_key['id']]);
}

// 根据路径处理请求
switch ($path) {
    case 'players':
        // 获取所有玩家
        $players = $db->getAll("SELECT * FROM players ORDER BY id DESC");
        $response = [
            'status' => 'success',
            'message' => '获取玩家列表成功',
            'data' => $players
        ];
        break;
        
    case 'player':
        // 获取特定玩家
        $nickname = isset($_GET['nickname']) ? $_GET['nickname'] : '';
        if (empty($nickname)) {
            $response['message'] = '玩家昵称不能为空';
        } else {
            $player = $db->getRow("SELECT * FROM players WHERE nickname = ?", [$nickname]);
            if ($player) {
                $response = [
                    'status' => 'success',
                    'message' => '获取玩家信息成功',
                    'data' => $player
                ];
            } else {
                $response['message'] = '找不到该玩家';
            }
        }
        break;
    
    // 可以添加更多端点处理
    
    case 'info':
        // 返回API信息
        $endpoints = [
            'players' => '获取所有玩家列表',
            'player' => '获取特定玩家详情 (参数: nickname)',
            // 添加其他端点
        ];
        
        $response = [
            'status' => 'success',
            'message' => 'API信息',
            'data' => [
                'version' => '1.0',
                'endpoints' => $endpoints
            ]
        ];
        break;
        
    default:
        $response['message'] = '无效的API路径';
        break;
}

// 输出JSON响应
echo json_encode($response, JSON_UNESCAPED_UNICODE);
?&gt;</pre>
                        <button class="btn btn-sm btn-primary mt-2" onclick="copyApiTemplate()">复制模板代码</button>
                        
                        <?php if ($can_create_api_file): ?>
                        <form method="post" action="" class="mt-3">
                            <input type="hidden" name="create_api_file" value="1">
                            <button type="submit" class="btn btn-sm btn-success">自动创建API文件</button>
                            <small class="form-text text-muted">将在 <?php echo htmlspecialchars($parent_dir); ?>/api.php 创建API文件</small>
                        </form>
                        <?php else: ?>
                        <div class="alert alert-warning mt-3">
                            无法自动创建API文件，请手动复制上面的代码并创建api.php文件。
                        </div>
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>
                    
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 根据选择的端点更新参数表单
$('#endpoint').change(function() {
    var endpoint = $(this).val();
    if (endpoint) {
        $('#api-test-form').submit();
    }
});

// 复制API密钥
function copyApiKey() {
    var apiKeyInput = document.getElementById('api_key');
    apiKeyInput.select();
    document.execCommand('copy');
    
    alert('API密钥已复制到剪贴板');
}

// 复制JSON响应
function copyResponseJson() {
    var jsonResponse = document.getElementById('json-response');
    
    // 创建一个临时文本区域
    var textArea = document.createElement('textarea');
    textArea.value = jsonResponse.textContent;
    document.body.appendChild(textArea);
    
    // 选择并复制文本
    textArea.select();
    document.execCommand('copy');
    
    // 移除临时文本区域
    document.body.removeChild(textArea);
    
    alert('JSON响应已复制到剪贴板');
}

function copyApiTemplate() {
    const template = document.querySelector('.mt-3 pre').textContent;
    const textArea = document.createElement('textarea');
    textArea.value = template;
    document.body.appendChild(textArea);
    textArea.select();
    document.execCommand('copy');
    document.body.removeChild(textArea);
    alert('API模板代码已复制到剪贴板');
}
</script>

<style>
.card {
    margin-bottom: 20px;
}
.api-url {
    padding: 10px;
    background-color: #f8f9fa;
    margin-bottom: 15px;
    word-break: break-all;
}
.api-response {
    padding: 15px;
}
.api-response-placeholder {
    padding: 30px;
    text-align: center;
}
.response-status, .response-message {
    margin-bottom: 10px;
}
.response-data, .response-raw {
    margin-top: 15px;
}
#json-response, .debug-log {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 4px;
    max-height: 400px;
    overflow-y: auto;
    white-space: pre-wrap;
    font-size: 12px;
}
.debug-info {
    background-color: #f8f9fa;
    padding: 10px;
    border-radius: 4px;
    margin-top: 15px;
}
.debug-log {
    color: #6c757d;
    line-height: 1.4;
}
.api-key-info {
    padding: 15px;
}
</style>

<?php
// 包含底部
    </div><!-- 结束 .container -->
</div><!-- 结束 .main-content -->

<?php
include 'includes/footer.php';
?> 