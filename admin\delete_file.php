<?php
require_once 'config.php';
require_once 'includes/utils.php';

// 检查登录状态
Utils::checkLogin();

// 检查参数
if (!isset($_GET['file']) || !isset($_GET['type'])) {
    $_SESSION['message'] = '缺少必要参数';
    $_SESSION['message_type'] = 'danger';
    header('Location: index.php');
    exit;
}

$file = Utils::sanitizeInput($_GET['file']);
$type = Utils::sanitizeInput($_GET['type']);

// 根据类型确定文件路径和重定向页面
$file_path = '';
$redirect_page = 'index.php';

switch ($type) {
    case 'excel':
        $file_path = EXCEL_DIR . $file;
        $redirect_page = 'export_data.php';
        break;
    case 'image':
        $file_path = IMAGES_DIR . $file;
        break;
    case 'article':
        $file_path = ARTICLES_DIR . $file;
        $redirect_page = 'articles.php';
        break;
    case 'announcement':
        $file_path = ANNOUNCEMENTS_DIR . $file;
        $redirect_page = 'announcements.php';
        break;
    default:
        $_SESSION['message'] = '未知文件类型';
        $_SESSION['message_type'] = 'danger';
        header('Location: index.php');
        exit;
}

// 检查文件是否存在
if (!file_exists($file_path)) {
    $_SESSION['message'] = '文件不存在';
    $_SESSION['message_type'] = 'danger';
    header('Location: ' . $redirect_page);
    exit;
}

// 删除文件
if (Utils::deleteFile($file_path)) {
    $_SESSION['message'] = '文件删除成功';
    $_SESSION['message_type'] = 'success';
    
    // 记录活动
    Utils::logActivity('删除文件', "删除文件：{$file}");
} else {
    $_SESSION['message'] = '文件删除失败';
    $_SESSION['message_type'] = 'danger';
}

header('Location: ' . $redirect_page);
exit; 