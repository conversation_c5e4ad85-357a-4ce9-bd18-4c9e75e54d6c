<?php
if (session_status() == PHP_SESSION_NONE && !headers_sent()) {
    session_start();
}

// 检查用户是否已登录
require_once 'includes/utils.php';
Utils::checkLogin();


// 获取当前页面
$current_page = basename($_SERVER['SCRIPT_NAME']);
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BDLX后台管理系统</title>
    <link rel="icon" href="../favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="../favicon.ico" type="image/x-icon">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/layout-fix.css">
    <!-- 确保layout-fix.css最后加载以覆盖其他样式 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <style>
        :root {
            --primary: #4361ee;
            --primary-light: #5a73f0;
            --primary-dark: #3a4fcc;
            --secondary: #6c757d;
            --success: #2ecc71;
            --danger: #e74c3c;
            --warning: #f39c12;
            --info: #3498db;
            --light: #f8f9fa;
            --dark: #212529;
            --gray-100: #f8f9fa;
            --gray-200: #e9ecef;
            --gray-300: #dee2e6;
            --gray-400: #ced4da;
            --gray-500: #adb5bd;
            --gray-600: #6c757d;
            --gray-700: #495057;
            --gray-800: #343a40;
            --gray-900: #212529;
            --sidebar-width: 240px;
            --sidebar-width-collapsed: 60px;
            --header-height: 60px;
            --font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
            --border-radius: 6px;
            --transition-speed: 0.3s;
            --box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        html, body {
            height: 100%;
            margin: 0;
            padding: 0;
            font-family: var(--font-family);
            background-color: var(--gray-100);
            color: var(--gray-800);
            overflow: hidden;
        }
        
        body {
            overflow: hidden;
            margin: 0;
            padding: 0;
            height: 100vh;
        }
        
        .wrapper {
            display: flex;
            height: 100vh;
            overflow: hidden;
        }
        
        /* 布局结构 */
        .page-wrapper {
            display: flex;
            height: 100vh;
            width: 100%;
            position: relative;
            overflow: hidden;
        }
        
        /* 侧边栏样式 */
        .side-nav {
            width: var(--sidebar-width);
            background: linear-gradient(135deg, var(--dark) 0%, var(--gray-800) 100%);
            color: white;
            height: 100vh;
            position: fixed;
            top: 0;
            left: 0;
            overflow-y: auto;
            z-index: 1030;
            box-shadow: var(--box-shadow);
            transition: width var(--transition-speed), transform var(--transition-speed);
        }
        
        .side-nav::-webkit-scrollbar {
            width: 6px;
        }
        
        .side-nav::-webkit-scrollbar-thumb {
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 3px;
        }
        
        .side-nav::-webkit-scrollbar-track {
            background-color: transparent;
        }
        
        .side-nav .logo {
            padding: 20px 15px;
            font-size: 18px;
            font-weight: bold;
            background-color: rgba(0, 0, 0, 0.15);
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: var(--header-height);
            overflow: hidden;
            transition: all var(--transition-speed);
        }
        
        .logo-icon {
            font-size: 22px;
            margin-right: 10px;
            color: var(--primary);
        }
        
        .logo-text {
            transition: opacity var(--transition-speed);
            white-space: nowrap;
        }
        
        /* 导航菜单 */
        .nav-menu {
            padding: 10px 0;
            margin-bottom: 20px;
        }
        
        .nav-section {
            margin-bottom: 10px;
            padding: 0 15px;
        }
        
        .nav-section-title {
            font-size: 12px;
            text-transform: uppercase;
            color: var(--gray-500);
            margin: 15px 0 8px;
            padding-left: 10px;
            letter-spacing: 0.5px;
            transition: opacity var(--transition-speed);
        }
        
        .nav-item {
            margin: 4px 0;
            border-radius: var(--border-radius);
            overflow: hidden;
        }
        
        .nav-link {
            display: flex;
            align-items: center;
            padding: 10px 15px;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all var(--transition-speed);
            border-radius: var(--border-radius);
            margin: 3px 0;
            position: relative;
        }
        
        .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }
        
        .nav-link.active {
            background-color: var(--primary);
            color: white;
            box-shadow: 0 3px 8px rgba(67, 97, 238, 0.3);
        }
        
        .nav-icon {
            width: 20px;
            text-align: center;
            margin-right: 10px;
            font-size: 16px;
            transition: margin var(--transition-speed);
        }
        
        .nav-text {
            font-size: 14px;
            white-space: nowrap;
            transition: opacity var(--transition-speed);
        }
        
        /* 侧边栏底部附加信息 */
        .sidebar-footer {
            padding: 15px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            font-size: 12px;
            color: var(--gray-500);
            text-align: center;
            transition: opacity var(--transition-speed);
        }
        
        /* 内容区域 */
        .content-wrapper {
            flex: 1;
            margin-left: var(--sidebar-width);
            display: flex !important;
            flex-direction: column !important;
            height: 100vh;
            transition: margin-left var(--transition-speed);
            width: calc(100% - var(--sidebar-width));
            position: relative;
            overflow: hidden;
            background: var(--gray-100);
            z-index: 1;
        }
        
        .top-header {
            background-color: white;
            height: var(--header-height);
            padding: 0 25px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: var(--box-shadow);
            position: relative;
            z-index: 10;
            width: 100%;
        }
        
        .header-left {
            display: flex;
            align-items: center;
        }
        
        .header-right {
            display: flex;
            align-items: center;
        }
        
        .toggle-sidebar {
            background: none;
            border: none;
            color: var(--gray-700);
            font-size: 20px;
            cursor: pointer;
            padding: 5px;
            margin-right: 15px;
            border-radius: 4px;
            transition: all 0.2s;
        }
        
        .toggle-sidebar:hover {
            background-color: var(--gray-200);
        }
        
        .page-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--gray-800);
        }
        
        .user-nav {
            display: flex;
            align-items: center;
        }
        
        .user-nav a {
            color: var(--gray-700);
            text-decoration: none;
            margin-left: 20px;
            font-size: 14px;
            display: flex;
            align-items: center;
            transition: color 0.2s;
            position: relative;
        }
        
        .user-nav a:hover {
            color: var(--primary);
        }
        
        .user-nav a i {
            margin-right: 5px;
            font-size: 16px;
        }

        /* 移动端切换按钮 */
        .mobile-toggle-btn {
            display: none;
            align-items: center;
            justify-content: center;
            width: 36px;
            height: 36px;
            padding: 0;
            background: transparent;
            border: none;
            color: var(--gray-700);
            font-size: 20px;
            cursor: pointer;
            margin-right: 15px;
            border-radius: 4px;
            transition: all 0.2s;
        }

        .mobile-toggle-btn:hover {
            background-color: var(--gray-200);
        }

        .main-content {
            flex: 1;
            overflow-y: auto;
            overflow-x: hidden;
            padding: 20px;
            position: relative;
            z-index: 5;
        }
        
        /* 侧边栏遮罩层 - 用于移动设备 */
        .sidebar-backdrop {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1025;
            display: none;
            opacity: 0;
            transition: opacity var(--transition-speed);
        }
        
        body.sidebar-visible .sidebar-backdrop {
            display: block;
            opacity: 1;
        }
        
        /* 折叠侧边栏相关样式 */
        body.collapsed .side-nav {
            width: var(--sidebar-width-collapsed);
        }
        
        body.collapsed .logo-text,
        body.collapsed .nav-text,
        body.collapsed .nav-section-title,
        body.collapsed .sidebar-footer {
            opacity: 0;
            visibility: hidden;
        }
        
        body.collapsed .nav-icon {
            margin-right: 0;
        }
        
        body.collapsed .content-wrapper {
            margin-left: var(--sidebar-width-collapsed);
            width: calc(100% - var(--sidebar-width-collapsed));
        }
        
        @media (max-width: 992px) {
            .stats-cards {
                grid-template-columns: repeat(2, 1fr);
            }
            
            /* 中等屏幕下调整边距 */
            .main-content {
                padding: 20px;
            }
        }
        
        @media (max-width: 768px) {
            /* 调整移动设备下侧边栏和内容区域的行为 */
            .side-nav {
                transform: translateX(-100%);
                width: var(--sidebar-width);
                z-index: 1050;
                box-shadow: none;
            }
            
            body.sidebar-visible .side-nav {
                transform: translateX(0);
                box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
            }
            
            .content-wrapper {
                margin-left: 0;
                width: 100%;
            }
            
            body.collapsed .side-nav {
                width: var(--sidebar-width);
                transform: translateX(-100%);
            }
            
            body.collapsed .content-wrapper {
                margin-left: 0;
                width: 100%;
            }
            
            body.sidebar-visible .content-wrapper {
                /* 侧边栏显示时内容区域不需要移动 */
                margin-left: 0;
                width: 100%;
            }
            
            body.collapsed .logo-text,
            body.collapsed .nav-text,
            body.collapsed .nav-section-title,
            body.collapsed .sidebar-footer {
                opacity: 1; /* 小屏幕展开侧边栏时显示文本 */
                visibility: visible;
            }
            
            body.collapsed .nav-icon {
                margin-right: 10px;
            }
            
            .stats-cards {
                grid-template-columns: 1fr;
            }
            
            /* 在移动设备上调整顶部导航 */
            .top-header {
                padding: 0 15px;
                height: 50px;
            }
            
            .header-left .page-title {
                font-size: 16px;
                max-width: 180px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
            
            .toggle-sidebar {
                margin-right: 10px;
            }

            .mobile-toggle-btn {
                display: flex;
            }

            .user-nav a {
                margin-left: 15px;
            }
            
            .user-nav a span {
                display: none;
            }
            
            .user-nav a i {
                margin-right: 0;
                font-size: 18px;
            }
            
            .main-content {
                height: calc(100vh - 50px);
            }
        }
        
        @media (max-width: 576px) {
            .top-header {
                padding: 0 10px;
            }
            
            .main-content {
                padding: 15px 10px;
            }
            
            .card {
                padding: 15px;
            }
            
            .stats-cards {
                gap: 10px;
                margin-bottom: 15px;
            }
            
            .header-left .page-title {
                max-width: 120px;
            }
            
            /* 移动端表格调整 */
            .table-responsive {
                margin-left: -15px;
                margin-right: -15px;
                width: calc(100% + 30px);
                border-radius: 0;
            }
            
            /* 移动端卡片调整 */
            .card-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
            
            .card-actions {
                align-self: flex-end;
            }
        }
        
        @media print {
            .side-nav, .top-header {
                display: none;
            }
            
            .content-wrapper {
                margin-left: 0;
                width: 100%;
            }
            
            .main-content {
                padding: 0;
            }
            
            .card {
                box-shadow: none;
                border: 1px solid var(--gray-300);
            }
        }
        
        /* 卡片样式增强 */
        .card {
            background-color: white;
            border-radius: var(--border-radius);
            margin-bottom: 20px;
            box-shadow: var(--box-shadow);
            overflow: hidden;
        }
        
        .card-header {
            padding: 15px 20px;
            border-bottom: 1px solid var(--gray-200);
            background-color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--gray-800);
            margin: 0;
        }
        
        .card-body {
            padding: 20px;
        }
        
        /* 数据表格样式增强 */
        .data-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .data-table th,
        .data-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid var(--gray-200);
        }
        
        .data-table th {
            font-weight: 600;
            color: var(--gray-700);
            background-color: var(--gray-100);
            white-space: nowrap;
        }
        
        .data-table tbody tr:hover {
            background-color: var(--gray-100);
        }
        
        /* 数据统计卡片样式 */
        .stats-card {
            display: flex;
            padding: 20px;
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            transition: transform 0.3s, box-shadow 0.3s;
            position: relative;
            overflow: hidden;
            height: 100%;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }
        
        .stats-card-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 50px;
            height: 50px;
            border-radius: 12px;
            margin-right: 15px;
        }
        
        .stats-card-icon i {
            font-size: 24px;
        }
        
        .stats-card-content {
            flex: 1;
        }
        
        .stats-card-value {
            font-size: 24px;
            font-weight: 700;
            color: var(--gray-900);
            margin-bottom: 5px;
            line-height: 1.2;
        }
        
        .stats-card-title {
            font-size: 14px;
            color: var(--gray-600);
            margin: 0;
        }
        
        /* 添加动画效果 */
        .fade-in {
            animation: fadeIn 0.5s ease forwards;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        /* 添加自定义滚动条 */
        ::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }
        
        ::-webkit-scrollbar-track {
            background-color: transparent;
        }
        
        ::-webkit-scrollbar-thumb {
            background-color: rgba(0, 0, 0, 0.2);
            border-radius: 3px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background-color: rgba(0, 0, 0, 0.3);
        }
    </style>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="js/fix-layout.js"></script>
    <script src="js/tabs.js"></script>
</head>
<body>
    <div class="page-wrapper">
        <div class="side-nav">
            <div class="logo">
                <span class="logo-icon"><i class="fas fa-cube"></i></span>
                <span class="logo-text">BDLX管理系统</span>
                
            </div>
            
            <div class="nav-menu">
                <div class="nav-section">
                    <div class="nav-section-title">数据管理</div>
                    <div class="nav-item">
                        <a href="index.php" class="nav-link <?php echo $current_page == 'index.php' ? 'active' : ''; ?>">
                            <span class="nav-icon"><i class="fas fa-home"></i></span>
                            <span class="nav-text">首页</span>
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="create.php" class="nav-link <?php echo $current_page == 'create.php' ? 'active' : ''; ?>">
                            <span class="nav-icon"><i class="fas fa-plus-circle"></i></span>
                            <span class="nav-text">游戏数据</span>
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="upload.php" class="nav-link <?php echo $current_page == 'upload.php' ? 'active' : ''; ?>">
                            <span class="nav-icon"><i class="fas fa-upload"></i></span>
                            <span class="nav-text">上传文件</span>
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="inventory.php" class="nav-link <?php echo $current_page == 'inventory.php' ? 'active' : ''; ?>">
                            <span class="nav-icon"><i class="fas fa-warehouse"></i></span>
                            <span class="nav-text">数据汇总</span>
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="game_type_view.php" class="nav-link <?php echo $current_page == 'game_type_view.php' ? 'active' : ''; ?>">
                            <span class="nav-icon"><i class="fas fa-gamepad"></i></span>
                            <span class="nav-text">游戏类型数据</span>
                        </a>
                    </div>
                </div>
                
                <div class="nav-section">
                    <div class="nav-section-title">内容管理</div>
                    <div class="nav-item">
                        <a href="articles.php" class="nav-link <?php echo $current_page == 'articles.php' ? 'active' : ''; ?>">
                            <span class="nav-icon"><i class="fas fa-file-alt"></i></span>
                            <span class="nav-text">文章管理</span>
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="article_categories.php" class="nav-link <?php echo $current_page == 'article_categories.php' ? 'active' : ''; ?>">
                            <span class="nav-icon"><i class="fas fa-folder"></i></span>
                            <span class="nav-text">文章分类</span>
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="announcements.php" class="nav-link <?php echo $current_page == 'announcements.php' ? 'active' : ''; ?>">
                            <span class="nav-icon"><i class="fas fa-bullhorn"></i></span>
                            <span class="nav-text">公告管理</span>
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="rules.php" class="nav-link <?php echo $current_page == 'rules.php' ? 'active' : ''; ?>">
                            <span class="nav-icon"><i class="fas fa-scroll"></i></span>
                            <span class="nav-text">规则通知</span>
                        </a>
                    </div>
                </div>
                
                <div class="nav-section">
                    <div class="nav-section-title">系统设置</div>
                    <div class="nav-item">
                        <a href="api_settings.php" class="nav-link <?php echo $current_page == 'api_settings.php' ? 'active' : ''; ?>">
                            <span class="nav-icon"><i class="fas fa-cogs"></i></span>
                            <span class="nav-text">API设置</span>
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="api_test.php" class="nav-link <?php echo $current_page == 'api_test.php' ? 'active' : ''; ?>">
                            <span class="nav-icon"><i class="fas fa-vial"></i></span>
                            <span class="nav-text">API测试</span>
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="player_test.php" class="nav-link <?php echo $current_page == 'player_test.php' ? 'active' : ''; ?>">
                            <span class="nav-icon"><i class="fas fa-gamepad"></i></span>
                            <span class="nav-text">玩家测试</span>
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="import_export.php" class="nav-link <?php echo $current_page == 'import_export.php' ? 'active' : ''; ?>">
                            <span class="nav-icon"><i class="fas fa-exchange-alt"></i></span>
                            <span class="nav-text">导入/导出</span>
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="change_password.php" class="nav-link <?php echo $current_page == 'change_password.php' ? 'active' : ''; ?>">
                            <span class="nav-icon"><i class="fas fa-key"></i></span>
                            <span class="nav-text">修改密码</span>
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="sidebar-footer">
                <a href="logout.php" style="color: var(--gray-400); text-decoration: none; font-size: 14px; display: flex; align-items: center; justify-content: center; gap: 8px;">
                    <i class="fas fa-sign-out-alt"></i> 退出登录
                </a>
                <div style="margin-top: 10px; font-size: 12px; opacity: 0.5;">版本 v1.0.0</div>
            </div>
        </div>
        
        <!-- 侧边栏遮罩层 -->
        <div class="sidebar-backdrop"></div>
        
        <div class="content-wrapper">
            <div class="top-header">
                
           
            <div class="header-left">
                    <button id="mobile-toggle" class="mobile-toggle-btn">
                        <i class="fas fa-bars"></i>
                    </button>
                    <div class="page-title-wrapper">
                        <h2 class="current-page-title"><?php echo getPageTitle($current_page); ?></h2>
                    </div>
                </div>
                


                <div class="header-right">
                    <div class="user-menu">
                        <div class="user-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="user-info">
                            管理员
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="main-content fade-in">
                <div class="container">

</body>
</html>

<?php
/**
 * 根据当前页面返回标题
 */
function getPageTitle($page) {
    switch ($page) {
        case 'index.php':
            return '数据列表';
        case 'upload.php':
            return '上传数据';
        case 'inventory.php':
            return '数据汇总';
        case 'export_data.php':
            return '导出数据';
        case 'articles.php':
            return '文章管理';
        case 'article_edit.php':
            return '编辑文章';
        case 'article_categories.php':
            return '文章分类';
        case 'announcements.php':
            return '公告管理';
        case 'api_settings.php':
            return 'API设置';
        case 'player_test.php':
            return '玩家测试';
        case 'api_test.php':
            return 'API测试';
        case 'game_type_view.php':
            return '游戏类型数据';
        case 'create.php':
            return isset($_GET['id']) ? '查看数据' : '创建数据';
        case 'rules.php':
            return '规则通知';
        case 'rule_edit.php':
            return isset($_GET['id']) ? '编辑规则通知' : '添加规则通知';
        default:
            return 'BDLX后台管理系统';
    }
}
?>