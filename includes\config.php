<?php
// 检查会话状态
$session_already_started = (session_status() == PHP_SESSION_ACTIVE);

// 只有在会话尚未开始的情况下才设置会话配置
if (!$session_already_started) {
    // 会话配置（必须在会话开始前设置）
    ini_set('session.cookie_httponly', 1);
    ini_set('session.use_only_cookies', 1);
    ini_set('session.cookie_secure', 0); // 在生产环境中设置为1
    
    // 启动会话
    session_start();
} else {
    // 会话已经启动，记录警告但不尝试设置会话 ini
    error_log("警告：会话已经启动，无法修改会话设置。");
}

// 数据库连接配置
$db_host = 'localhost';
$db_name = 'bdlx';
$db_user = 'root';
$db_pass = '';

// 尝试建立PDO连接
try {
    $pdo = new PDO(
        "mysql:host=$db_host;dbname=$db_name;charset=utf8mb4",
        $db_user,
        $db_pass,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]
    );
} catch (PDOException $e) {
    // 记录错误但不显示敏感信息
    error_log("数据库连接失败: " . $e->getMessage());
    die("数据库连接失败，请联系管理员。");
}

// 网站基本配置
define('SITE_URL', 'http://localhost/BDLX');
define('SITE_NAME', 'BDLX系统');
define('UPLOAD_DIR', __DIR__ . '/../uploads/');

// 时区设置
date_default_timezone_set('Asia/Shanghai');

// 错误处理
error_reporting(E_ALL);
ini_set('display_errors', 1); // 开发环境设置，生产环境应设为0 