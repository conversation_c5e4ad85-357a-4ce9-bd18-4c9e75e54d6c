<?php
namespace admin\includes;

require_once __DIR__ . '/../../vendor/autoload.php';
require_once __DIR__ . '/db.php';

use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;

class ExcelHandler {
    private $filePath;
    private $spreadsheet;
    
    public function getSpreadsheet() {
        return $this->spreadsheet;
    }
    
    public function __construct($filePath = null) {
        if ($filePath && file_exists($filePath)) {
            $this->filePath = $filePath;
            $this->loadFile();
        }
    }
    
    public function loadFile() {
        try {
            $this->spreadsheet = IOFactory::load($this->filePath);
            return true;
        } catch (Exception $e) {
            error_log("Excel文件加载失败: " . $e->getMessage());
            return false;
        }
    }
    
    public function setFilePath($filePath) {
        $this->filePath = $filePath;
        return $this->loadFile();
    }
    
    public function readData($sheet = 0) {
        if (!$this->spreadsheet) {
            return false;
        }
        
        try {
            $worksheet = $this->spreadsheet->getSheet($sheet);
            $highestRow = $worksheet->getHighestRow();
            $highestColumn = $worksheet->getHighestColumn();
            $highestColumnIndex = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::columnIndexFromString($highestColumn);
            
            $data = [];
            // 假设第一行是标题行
            $headers = [];
            for ($col = 1; $col <= $highestColumnIndex; $col++) {
                $headerValue = $worksheet->getCellByColumnAndRow($col, 1)->getValue();
                // 处理RichText类型的表头值
                if ($headerValue instanceof \PhpOffice\PhpSpreadsheet\RichText\RichText) {
                    $headerValue = $headerValue->getPlainText();
                }
                $headers[] = $headerValue;
            }
            
            // 读取数据行
            for ($row = 2; $row <= $highestRow; $row++) {
                $rowData = [];
                for ($col = 1; $col <= $highestColumnIndex; $col++) {
                    $cellValue = $worksheet->getCellByColumnAndRow($col, $row)->getValue();
                    // 处理RichText类型的单元格值
                    if ($cellValue instanceof \PhpOffice\PhpSpreadsheet\RichText\RichText) {
                        $cellValue = $cellValue->getPlainText();
                    }
                    if (isset($headers[$col - 1])) {
                        $rowData[$headers[$col - 1]] = $cellValue;
                    }
                }
                if (!empty(array_filter($rowData))) {
                    $data[] = $rowData;
                }
            }
            
            return $data;
        } catch (Exception $e) {
            error_log("Excel数据读取失败: " . $e->getMessage());
            return false;
        }
    }
    
    public function writeData($data, $headers = null, $sheet = 0) {
        if (!$this->spreadsheet) {
            return false;
        }
        
        try {
            $worksheet = $this->spreadsheet->getActiveSheet();
            
            // 设置工作表标题
            $worksheet->setTitle('数据统计');
            
            // 写入表头
            if ($headers) {
                $col = 1;
                foreach ($headers as $header) {
                    $cell = $worksheet->getCellByColumnAndRow($col, 1);
                    $cell->setValue($header);
                    
                    // 设置表头样式
                    $cell->getStyle()
                        ->getFont()
                        ->setBold(true)
                        ->setSize(12);
                    
                    $cell->getStyle()
                        ->getFill()
                        ->setFillType(Fill::FILL_SOLID)
                        ->getStartColor()
                        ->setRGB('CCCCCC');
                    
                    $cell->getStyle()
                        ->getAlignment()
                        ->setHorizontal(Alignment::HORIZONTAL_CENTER)
                        ->setVertical(Alignment::VERTICAL_CENTER);
                    
                    // 自动调整列宽
                    $worksheet->getColumnDimensionByColumn($col)->setAutoSize(true);
                    
                    $col++;
                }
            }
            
            // 写入数据
            $row = 2; // 从第二行开始（假设第一行是标题）
            foreach ($data as $rowData) {
                $col = 1;
                foreach ($rowData as $value) {
                    $cell = $worksheet->getCellByColumnAndRow($col, $row);
                    $cell->setValue($value);
                    
                    // 设置单元格样式
                    $cell->getStyle()
                        ->getAlignment()
                        ->setHorizontal(Alignment::HORIZONTAL_CENTER)
                        ->setVertical(Alignment::VERTICAL_CENTER);
                    
                    // 设置边框
                    $cell->getStyle()
                        ->getBorders()
                        ->getAllBorders()
                        ->setBorderStyle(Border::BORDER_THIN);
                    
                    $col++;
                }
                $row++;
            }
            
            // 冻结首行
            $worksheet->freezePane('A2');
            
            return true;
        } catch (Exception $e) {
            error_log("Excel数据写入失败: " . $e->getMessage());
            return false;
        }
    }
    
    public function updateCell($row, $col, $value, $sheet = 0) {
        if (!$this->spreadsheet) {
            return false;
        }
        
        try {
            $worksheet = $this->spreadsheet->getSheet($sheet);
            $worksheet->setCellValueByColumnAndRow($col, $row, $value);
            return true;
        } catch (Exception $e) {
            error_log("Excel单元格更新失败: " . $e->getMessage());
            return false;
        }
    }
    
    public function saveFile($filePath = null) {
        if (!$this->spreadsheet) {
            return false;
        }
        
        $savePath = $filePath ?: $this->filePath;
        
        try {
            // 确保目录存在
            $dir = dirname($savePath);
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
            }
            
            // 如果文件已存在，先删除
            if (file_exists($savePath)) {
                chmod($savePath, 0644); // 确保文件可写
                unlink($savePath);
            }
            
            // 创建Excel 2007写入器
            $writer = IOFactory::createWriter($this->spreadsheet, 'Xlsx');
            
            // 设置写入器选项
            $writer->setIncludeCharts(false);
            $writer->setPreCalculateFormulas(false);
            $writer->setOffice2003Compatibility(false);
            
            // 保存文件
            $writer->save($savePath);
            
            // 设置新文件的权限
            chmod($savePath, 0644);
            
            // 清理内存
            $this->spreadsheet->disconnectWorksheets();
            unset($this->spreadsheet);
            
            return true;
        } catch (Exception $e) {
            error_log("Excel文件保存失败: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            return false;
        }
    }
    
    public function createNew() {
        $this->spreadsheet = new Spreadsheet();
        
        // 设置文档属性
        $this->spreadsheet->getProperties()
            ->setCreator('BDLX系统')
            ->setLastModifiedBy('BDLX系统')
            ->setTitle('数据统计')
            ->setSubject('玩家数据统计')
            ->setDescription('由BDLX系统自动生成的数据统计表')
            ->setCategory('Game Statistics');

        // 设置默认字体
        $this->spreadsheet->getDefaultStyle()->getFont()->setName('Microsoft YaHei');
        $this->spreadsheet->getDefaultStyle()->getFont()->setSize(10);
        
        // 设置默认对齐方式
        $this->spreadsheet->getDefaultStyle()->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_LEFT);
        $this->spreadsheet->getDefaultStyle()->getAlignment()->setVertical(\PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER);
        
        // 设置活动工作表
        $this->spreadsheet->setActiveSheetIndex(0);
        
        return true;
    }
    
    public function exportToCsv($filePath) {
        if (!$this->spreadsheet) {
            return false;
        }
        
        try {
            $writer = IOFactory::createWriter($this->spreadsheet, 'Csv');
            $writer->setUseBOM(true); // 使用BOM标记解决中文乱码
            $writer->save($filePath);
            return true;
        } catch (Exception $e) {
            error_log("Excel导出为CSV失败: " . $e->getMessage());
            return false;
        }
    }
    
    // 解析游戏战绩数据，适用于特定格式的Excel文件
    public function parseGameData() {
        $data = $this->readData();
        if (!$data) {
            return false;
        }
        
        // 适应特定格式：玩家昵称/大厅虚拟IP/大厅军衔/狙杀/死亡/分组/胜场/败场
        $parsedData = [];
        foreach ($data as $row) {
            if (isset($row['玩家昵称']) && $row['玩家昵称']) {
                $parsedData[] = [
                    'nickname' => $row['玩家昵称'],
                    'virtual_ip' => $row['大厅虚拟IP'] ?? '',
                    'player_rank' => $row['大厅军衔'] ?? '',
                    'kills' => isset($row['狙杀']) ? (int)$row['狙杀'] : 0,
                    'deaths' => isset($row['死亡']) ? (int)$row['死亡'] : 0,
                    'team' => $row['分组'] ?? '',
                    'wins' => isset($row['胜场']) ? (int)$row['胜场'] : 0,
                    'losses' => isset($row['败场']) ? (int)$row['败场'] : 0
                ];
            }
        }
        
        return $parsedData;
    }
}