/**
 * 布局修复脚本
 * 用于解决内容区域不显示的问题
 */
document.addEventListener('DOMContentLoaded', function() {
    // 确保主内容区域正确显示
    const mainContent = document.querySelector('.main-content');
    const contentWrapper = document.querySelector('.content-wrapper');
    const sideNav = document.querySelector('.side-nav');
    const header = document.querySelector('.top-header');
    
    // 应用布局修复函数
    applyLayoutFix();
    
    // 监听窗口大小变化
    window.addEventListener('resize', applyLayoutFix);
    
    // 监听DOM变化，检测内容区是否被隐藏
    if (window.MutationObserver) {
        const observer = new MutationObserver(function(mutations) {
            const mainContent = document.querySelector('.main-content');
            if (mainContent) {
                const style = window.getComputedStyle(mainContent);
                if (style.display === 'none' || style.visibility === 'hidden' || style.opacity === '0') {
                    applyLayoutFix();
                }
            }
        });

        observer.observe(document.body, {
            attributes: true,
            childList: true,
            subtree: true
        });
    }
    
    // 确保DOM完全加载后再次应用样式（解决某些浏览器可能的加载时序问题）
    setTimeout(applyLayoutFix, 100);
    setTimeout(applyLayoutFix, 500);
    setTimeout(applyLayoutFix, 1000);
});

// 应用布局修复函数
function applyLayoutFix() {
    const mainContent = document.querySelector('.main-content');
    const contentWrapper = document.querySelector('.content-wrapper');
    const sideNav = document.querySelector('.side-nav');
    const header = document.querySelector('.top-header');
    
    // 强制显示主内容区域
    if (mainContent) {
        // 确保内容区域可见，通过设置明确的样式
        mainContent.style.display = 'block';
        mainContent.style.visibility = 'visible';
        mainContent.style.opacity = '1';
        
        // 确保内容区域有正确的高度
        if (header) {
            const headerHeight = header.offsetHeight || 60;
            mainContent.style.height = `calc(100vh - ${headerHeight}px)`;
            mainContent.style.minHeight = '300px'; // 确保最小高度
        }
        
        // 确保内容区域可滚动且正确显示
        mainContent.style.overflowY = 'auto';
        mainContent.style.overflowX = 'hidden';
        mainContent.style.position = 'relative';
        mainContent.style.zIndex = '5';
    }
    
    // 修复侧边栏与内容区域的交互
    if (sideNav && contentWrapper) {
        // 确保侧边栏正确定位
        sideNav.style.position = 'fixed';
        sideNav.style.zIndex = '1030';
        
        // 确保内容区域有正确的宽度和边距
        let sidebarWidth;
        if (window.innerWidth <= 768) {
            sidebarWidth = 0;
        } else if (document.body.classList.contains('collapsed')) {
            sidebarWidth = 60; // 折叠宽度
        } else {
            sidebarWidth = 240; // 展开宽度
        }
        
        // 设置内容区域样式
        contentWrapper.style.marginLeft = `${sidebarWidth}px`;
        contentWrapper.style.width = `calc(100% - ${sidebarWidth}px)`;
        contentWrapper.style.display = 'flex';
        contentWrapper.style.flexDirection = 'column';
        contentWrapper.style.position = 'relative';
        contentWrapper.style.zIndex = '1';
        contentWrapper.style.background = 'var(--gray-100)';
    }
}

// 确保window load事件也触发布局修复
window.addEventListener('load', function() {
    // 应用布局修复
    applyLayoutFix();
    
    // 处理可能的样式冲突
    const style = document.createElement('style');
    style.textContent = `
        .main-content {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
        }
        .content-wrapper {
            display: flex !important;
            flex-direction: column !important;
        }
    `;
    document.head.appendChild(style);
    
    // 检查页面是否正确加载
    setTimeout(function checkAndFixContent() {
        const mainContent = document.querySelector('.main-content');
        if (mainContent) {
            const children = mainContent.children;
            let hasVisibleContent = false;
            
            for (let i = 0; i < children.length; i++) {
                const style = window.getComputedStyle(children[i]);
                if (style.display !== 'none' && style.visibility !== 'hidden') {
                    hasVisibleContent = true;
                    break;
                }
            }
            
            // 如果没有可见内容，尝试重新加载主内容
            if (!hasVisibleContent && window.location.pathname.includes('index.php')) {
                console.log('尝试修复内容区域');
                // 重新应用样式
                applyLayoutFix();
            }
        }
    }, 2000);
}); 