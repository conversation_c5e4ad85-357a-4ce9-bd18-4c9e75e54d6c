<?php
require_once 'config.php';
require_once 'includes/db.php';
require_once 'includes/utils.php';
require_once 'includes/stats_calculator.php';

// 包含头部
include 'includes/header.php';
?>


    <div class="container">
<?php

// 获取当前API密钥
$sql = "SELECT * FROM api_settings WHERE is_active = 1 ORDER BY id DESC LIMIT 1";
$api_setting = $db->getRow($sql);

// 如果没有找到API设置，使用配置文件中的默认值
if (!$api_setting) {
    $api_setting = [
        'api_key' => API_KEY,
        'created_at' => date('Y-m-d H:i:s'),
        'last_used' => null,
        'is_active' => 1
    ];
}

// 获取网站根URL
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
$host = $_SERVER['HTTP_HOST'];
$base_url = $protocol . $host;

// 创建统计计算器实例
$stats = new StatsCalculator($db);

// 初始化变量
$playerStats = null;
$playerGames = [];
$nickname = '';
$errorMessage = '';

// 处理表单提交
if (isset($_GET['nickname']) && !empty($_GET['nickname'])) {
    $nickname = Utils::sanitizeInput($_GET['nickname']);
    
    // 获取玩家统计信息
    $playerStats = $stats->getPlayerStats($nickname);
    
    if ($playerStats) {
        // 获取玩家ID
        $sql = "SELECT id FROM players WHERE nickname = '" . $db->escape($nickname) . "'";
        $player = $db->getRow($sql);
        
        if ($player) {
            $playerId = $player['id'];
            
            // 获取玩家参与的游戏
            $sql = "SELECT g.id, g.unique_id, g.game_type, g.upload_time,
                           gr.kills, gr.deaths, gr.team, gr.wins, gr.losses,
                           p.virtual_ip
                    FROM games g
                    JOIN game_records gr ON g.id = gr.game_id
                    JOIN players p ON gr.player_id = p.id
                    WHERE gr.player_id = {$playerId}
                    ORDER BY g.upload_time DESC";
            
            $playerGames = $db->getRows($sql);
        }
    } else {
        $errorMessage = "未找到玩家 '{$nickname}'";
    }
}
?>

<h1 class="page-title">玩家详情测试</h1>

<div class="card">
    <div class="card-title">玩家搜索</div>
    <form method="get" action="" class="search-form">
        <div class="form-group">
            <label for="nickname" class="form-label">玩家昵称</label>
            <div class="input-group">
                <input type="text" id="nickname" name="nickname" class="form-control" value="<?php echo htmlspecialchars($nickname); ?>" required>
                <button type="submit" class="btn btn-primary">搜索</button>
            </div>
        </div>
    </form>
</div>

<?php if ($errorMessage): ?>
<div class="alert alert-danger">
    <?php echo $errorMessage; ?>
</div>
<?php endif; ?>

<?php if ($playerStats): ?>
<div class="card">
    <div class="card-title">玩家基本信息</div>
    <div class="player-info">
        <div class="row">
            <div class="col-md-6">
                <h3><?php echo htmlspecialchars($playerStats['nickname']); ?></h3>
                <p class="rank-badge"><?php echo htmlspecialchars($playerStats['player_rank']); ?></p>
            </div>
            <div class="col-md-6 text-right">
                <div class="api-info">
                    <p><small>使用 API 获取:</small></p>
                    <code>GET <?php echo $base_url; ?>/admin/api.php?path=player&nickname=<?php echo urlencode($playerStats['nickname']); ?>&api_key=<?php echo $api_setting['api_key']; ?></code>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-title">玩家统计</div>
    <div class="row">
        <div class="col-md-6">
            <div class="stat-box">
                <div class="stat-item">
                    <span class="stat-label">总狙杀数</span>
                    <span class="stat-value"><?php echo $playerStats['total_kills']; ?></span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">总死亡数</span>
                    <span class="stat-value"><?php echo $playerStats['total_deaths']; ?></span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">KD值</span>
                    <span class="stat-value"><?php echo $playerStats['kd']; ?></span>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="stat-box">
                <div class="stat-item">
                    <span class="stat-label">总胜场</span>
                    <span class="stat-value"><?php echo $playerStats['total_wins']; ?></span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">总败场</span>
                    <span class="stat-value"><?php echo $playerStats['total_losses']; ?></span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">胜率</span>
                    <span class="stat-value"><?php echo $playerStats['win_rate']; ?></span>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-title">游戏场次列表</div>
    <?php if (empty($playerGames)): ?>
    <div class="alert alert-info">该玩家暂无游戏记录</div>
    <?php else: ?>
    <div class="table-responsive">
        <table class="table table-hover">
            <thead>
                <tr>
                    <th>游戏ID</th>
                    <th>游戏类型</th>
                    <th>时间</th>
                    <th>大厅虚拟IP</th>
                    <th>队伍</th>
                    <th>击杀</th>
                    <th>死亡</th>
                    <th>胜场</th>
                    <th>败场</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($playerGames as $game): ?>
                <tr>
                    <td><?php echo $game['unique_id']; ?></td>
                    <td><?php echo $game['game_type']; ?></td>
                    <td><?php echo date('Y-m-d H:i', strtotime($game['upload_time'])); ?></td>
                    <td><?php echo $game['virtual_ip']; ?></td>
                    <td><?php echo $game['team']; ?></td>
                    <td><?php echo $game['kills']; ?></td>
                    <td><?php echo $game['deaths']; ?></td>
                    <td><?php echo $game['wins']; ?></td>
                    <td><?php echo $game['losses']; ?></td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
    <?php endif; ?>
</div>

<style>
.search-form {
    margin-bottom: 20px;
}
.player-info {
    padding: 15px;
}
.rank-badge {
    display: inline-block;
    padding: 5px 10px;
    background-color: #6c757d;
    color: white;
    border-radius: 4px;
    margin-top: 5px;
}
.api-info {
    margin-top: 15px;
    background-color: #f8f9fa;
    padding: 10px;
    border-radius: 4px;
}
.stat-box {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -5px;
}
.stat-item {
    flex: 1 0 calc(33.33% - 10px);
    margin: 5px;
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 4px;
    text-align: center;
}
.stat-label {
    display: block;
    font-size: 14px;
    color: #6c757d;
    margin-bottom: 5px;
}
.stat-value {
    display: block;
    font-size: 20px;
    font-weight: bold;
}
</style>
<?php endif; ?>

    </div><!-- 结束 .container -->
</div><!-- 结束 .main-content -->

<?php
// 包含底部
include 'includes/footer.php';
?> 