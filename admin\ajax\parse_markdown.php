<?php
require_once '../config.php';
require_once '../includes/db.php';
require_once '../includes/utils.php';
require_once '../../includes/formatting.php';

// 验证管理员登录状态
session_start();
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('HTTP/1.1 403 Forbidden');
    echo '未授权访问';
    exit;
}

// 获取POST数据
$content = isset($_POST['content']) ? $_POST['content'] : '';

// 先处理短代码
$content = parse_shortcodes($content);

// 再解析Markdown内容
$html = Utils::parseMarkdown($content);

// 输出解析后的HTML
echo $html;
?> 