<?php
require_once 'config.php';
require_once 'includes/db.php';
require_once 'includes/utils.php';
require_once 'includes/excel_handler.php';

// 包含头部
include 'includes/header.php';
?>

<div class="main-content">
    <div class="container">
<?php

$error = '';
$success = '';

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $game_type = isset($_POST['game_type']) ? Utils::sanitizeInput($_POST['game_type']) : '';
    
    if (empty($game_type)) {
        $error = '请选择游戏类型';
    } elseif (!isset($_FILES['excel_file']) || $_FILES['excel_file']['error'] != 0) {
        $error = '请选择Excel文件';
    } elseif (!isset($_FILES['image_file']) || $_FILES['image_file']['error'] != 0) {
        $error = '请上传游戏图片';
    } else {
        // 上传Excel文件
        $excel_result = Utils::uploadFile($_FILES['excel_file'], EXCEL_DIR, ['xlsx', 'xls']);
        
        if (!$excel_result['success']) {
            $error = '上传Excel文件失败: ' . $excel_result['error'];
        } else {
            // 上传图片文件
            $image_result = Utils::uploadFile($_FILES['image_file'], IMAGES_DIR, ['jpg', 'jpeg', 'png', 'gif']);
            
            if (!$image_result['success']) {
                // 删除已上传的Excel文件
                Utils::deleteFile($excel_result['file_path']);
                $error = '上传图片文件失败: ' . $image_result['error'];
            } else {
                // 生成唯一ID
                $unique_id = Utils::generateUniqueId();
                
                // 保存到数据库
                $game_data = [
                    'unique_id' => $unique_id,
                    'excel_file' => $excel_result['file_name'],
                    'image_file' => $image_result['file_name'],
                    'game_type' => $game_type,
                    'upload_time' => date('Y-m-d H:i:s')
                ];
                
                $game_id = $db->insert('games', $game_data);
                
                if (!$game_id) {
                    // 删除已上传的文件
                    Utils::deleteFile($excel_result['file_path']);
                    Utils::deleteFile($image_result['file_path']);
                    $error = '保存游戏数据失败';
                } else {
                    // 解析Excel文件，保存玩家数据
                    $excelHandler = new ExcelHandler($excel_result['file_path']);
                    $gameData = $excelHandler->parseGameData();
                    
                    if (!$gameData) {
                        $error = '解析Excel文件失败';
                    } else {
                        // 创建统计计算器
                        require_once 'includes/stats_calculator.php';
                        $stats = new StatsCalculator($db);
                        
                        // 处理游戏数据
                        $processedData = $stats->processGameData($gameData, $game_id);
                        
                        // 保存玩家数据和游戏记录
                        foreach ($processedData as $record) {
                            // 检查玩家是否已存在
                            $sql = "SELECT id FROM players WHERE nickname = '{$db->escape($record['player_name'])}'";
                            $player = $db->getRow($sql);
                            
                            if (!$player) {
                                // 创建新玩家
                                $player_data = [
                                    'nickname' => $record['player_name'],
                                    'virtual_ip' => $record['virtual_ip'],
                                    'player_rank' => $record['player_rank'],
                                    'first_seen' => date('Y-m-d H:i:s'),
                                    'last_seen' => date('Y-m-d H:i:s')
                                ];
                                
                                $player_id = $db->insert('players', $player_data);
                            } else {
                                // 更新现有玩家
                                $player_id = $player['id'];
                                $sql = "UPDATE players SET 
                                        virtual_ip = '{$db->escape($record['virtual_ip'])}',
                                        player_rank = '{$db->escape($record['player_rank'])}',
                                        last_seen = NOW()
                                        WHERE id = {$player_id}";
                                $db->query($sql);
                            }
                            
                            // 保存游戏记录
                            if ($player_id) {
                                $game_record = [
                                    'game_id' => $game_id,
                                    'player_id' => $player_id,
                                    'kills' => $record['kills'],
                                    'deaths' => $record['deaths'],
                                    'team' => $record['team'],
                                    'wins' => $record['wins'],
                                    'losses' => $record['losses']
                                ];
                                
                                $record_id = $db->insert('game_records', $game_record);
                                
                                if (!$record_id) {
                                    error_log("保存游戏记录失败: " . print_r($game_record, true));
                                }
                            }
                        }
                        
                        $success = '上传成功！正在跳转到编辑页面...';
                        // 记录活动
                        Utils::logActivity('上传数据', "上传游戏数据：{$game_type}，ID：{$unique_id}");
                        
                        // 设置跳转
                        echo '<script>
                            setTimeout(function() {
                                window.location.href = "create.php?id=' . $game_id . '";
                            }, 2000);
                        </script>';
                    }
                }
            }
        }
    }
}

// 关闭主布局容器
echo '</main>';
?>

<!-- 自定义布局结构 -->
<div class="container-fluid px-4 py-2">
    <div class="d-flex align-items-center justify-content-between mb-2">
        <h1 class="page-title fs-3 mb-0">上传数据</h1>
   <!--      <div class="alert alert-warning py-2 px-3 mt-3 mb-0">
                        <small><i class="fas fa-exclamation-triangle me-1"></i> 请确保Excel文件的第一行包含上述列名，且数据从第二行开始。</small>
                    </div>-->
    </div>

    <?php if (!empty($error)): ?>
        <div class="alert alert-danger py-2 px-3 mb-3 alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-1"></i> <?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="关闭"></button>
        </div>
    <?php endif; ?>

    <?php if (!empty($success)): ?>
        <div class="alert alert-success py-2 px-3 mb-3 alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-1"></i> <?php echo $success; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="关闭"></button>
        </div>
    <?php endif; ?>

    <div class="d-flex flex-row flex-nowrap gap-3">
        <!-- 左侧表单 -->
        <div class="flex-grow-2" style="width: 45%;">
            <div class="card shadow-sm h-100">
                <div class="card-header py-2 bg-primary text-white">
                    <i class="fas fa-upload me-1"></i> <strong>上传游戏数据</strong>
                </div>
                <div class="card-body p-3">
                    <form method="POST" action="upload.php" enctype="multipart/form-data" class="needs-validation" novalidate>
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="game_type" class="form-label small mb-1">游戏类型 <span class="text-danger">*</span></label>
                                <select id="game_type" name="game_type" class="form-select form-select-sm" required>
                                    <option value="">请选择游戏类型</option>
                                    <option value="暗杀">暗杀</option>
                                    <option value="死斗">死斗</option>
                                    <option value="盟主">盟主</option>
                                </select>
                                <div class="form-text small mt-1">请选择正确的游戏类型分类</div>
                            </div>
                            
                            <div class="col-md-6">
                                <label for="excel_file" class="form-label small mb-1">Excel文件 <span class="text-danger">*</span></label>
                                <input type="file" id="excel_file" name="excel_file" class="form-control form-control-sm" accept=".xlsx,.xls" required>
                                <div class="form-text small mt-1">请上传Excel格式的战绩数据文件</div>
                            </div>
                        </div>
                        
                        <div class="mb-3 mt-2">
                            <label for="image_file" class="form-label small mb-1">游戏截图 <span class="text-danger">*</span></label>
                            <input type="file" id="image_file" name="image_file" class="form-control form-control-sm" accept=".jpg,.jpeg,.png,.gif" data-preview="image_preview" required>
                            <div class="form-text small mt-1">支持JPG、PNG、GIF格式</div>
                        </div>
                        
                        <div class="mb-3">
                            <div id="image_preview" class="image-preview border rounded p-2 mb-2 text-center d-none">
                                <img id="preview_img" class="img-fluid" style="max-height: 180px;" />
                            </div>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-upload me-1"></i> 上传数据
                            </button>

                            <a href="index.php" class="btn btn-sm btn-outline-secondary" style=" margin-left: 20px; margin-top: 15px;">
            <i class="fas fa-arrow-left me-1"></i> 返回
        </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- 右侧说明 -->
        <div class="flex-grow-1" style="width: 55%;">
            <div class="card shadow-sm h-100">
                <div class="card-header py-2 bg-info text-white">
                    <i class="fas fa-info-circle me-1"></i> <strong>Excel文件格式说明:</strong>（Excel文件应包含以下列）
                </div>
                <div class="card-body p-3">
                    <div class="table-responsive">
                        <table class="table table-sm table-bordered table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th class="small">列名</th>
                                    <th class="small">说明</th>
                                </tr>
                            </thead>
                            <tbody class="small">
                                <tr>
                                    <td><strong>玩家昵称</strong></td>
                                    <td>玩家的游戏昵称</td>
                                </tr>
                                <tr>
                                    <td><strong>大厅虚拟IP</strong></td>
                                    <td>玩家的IP地址</td>
                                </tr>
                                <tr>
                                    <td><strong>大厅军衔</strong></td>
                                    <td>玩家的军衔等级</td>
                                </tr>
                                <tr>
                                    <td><strong>狙杀</strong></td>
                                    <td>玩家的击杀数量</td>
                                </tr>
                                <tr>
                                    <td><strong>死亡</strong></td>
                                    <td>玩家的死亡次数</td>
                                </tr>
                                <tr>
                                    <td><strong>分组</strong></td>
                                    <td>玩家所在的队伍</td>
                                </tr>
                                <tr>
                                    <td><strong>胜场</strong></td>
                                    <td>玩家所在队伍的胜场数</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 图片预览功能
    const imageInput = document.getElementById('image_file');
    const previewContainer = document.getElementById('image_preview');
    const previewImage = document.getElementById('preview_img');
    
    if(imageInput) {
        imageInput.addEventListener('change', function() {
            if(this.files && this.files[0]) {
                const reader = new FileReader();
                
                reader.onload = function(e) {
                    previewImage.src = e.target.result;
                    previewContainer.classList.remove('d-none');
                }
                
                reader.readAsDataURL(this.files[0]);
            } else {
                previewContainer.classList.add('d-none');
            }
        });
    }
    
    // 表单验证
    const forms = document.querySelectorAll('.needs-validation');
    Array.from(forms).forEach(form => {
        form.addEventListener('submit', event => {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        }, false);
    });
});
</script>

<style>
/* 强制保持左右两列布局 */
.d-flex.flex-row {
    display: flex !important;
    flex-direction: row !important;
    flex-wrap: nowrap !important;
}

/* 响应式调整 */
@media (max-width: 991.98px) {
    .d-flex.flex-row {
        gap: 1rem !important;
    }
}

/* 小屏幕优化 */
@media (max-width: 767.98px) {
    .container-fluid {
        padding-left: 10px !important;
        padding-right: 10px !important;
    }
    
    .card-header {
        padding: 0.4rem 0.75rem;
    }
    
    .table-responsive table {
        font-size: 0.8rem;
    }
    
    .page-title {
        font-size: 1.4rem !important;
    }
}

/* 美化表格 */
.table-hover tbody tr:hover {
    background-color: rgba(0,0,0,.03);
}

/* 美化卡片 */
.card {
    transition: all 0.2s ease;
    border: none;
    border-radius: 0.375rem;
}

.card-header {
    border-top-left-radius: 0.375rem !important;
    border-top-right-radius: 0.375rem !important;
}
</style>

    </div><!-- 结束 .container -->
</div><!-- 结束 .main-content -->

<?php
// 包含底部
include 'includes/footer.php';
?> 