<?php
require_once 'config.php';
require_once 'includes/db.php';
require_once 'includes/utils.php';
require_once 'includes/stats_calculator.php';

// 检查登录状态
Utils::checkLogin();

// 创建统计计算器
$stats = new StatsCalculator($db);

// 获取所有可用的游戏模式
$game_modes = $stats->getAvailableGameModes();

// 检查是否是直接导出请求
if (isset($_GET['format'])) {
    $format = Utils::sanitizeInput($_GET['format']);
    // 只支持Excel格式
    if ($format != 'excel') {
        $format = 'excel';
    }
    
    $type = isset($_GET['type']) ? Utils::sanitizeInput($_GET['type']) : 'players';
    
    // 根据类型导出不同数据
    if ($type == 'ranks') {
        // 导出军衔分布数据
        $filename = $stats->exportRankStats($format);
    } else {
        // 导出玩家数据
        $filename = $stats->exportPlayerStats($format);
    }
    
    if ($filename) {
        // 记录活动
        Utils::logActivity('导出数据', "导出{$type}数据为Excel格式，文件名：{$filename}");
        
        // 重定向到下载页面
        header("Location: download.php?file={$filename}&type=excel");
        exit;
    } else {
        $_SESSION['message'] = '导出数据失败';
        $_SESSION['message_type'] = 'danger';
        
        // 重定向回导出页面
        header('Location: export_data.php');
        exit;
    }
}

// 包含头部
include 'includes/header.php';
?>


    <div class="container">
<?php
?>
<h1 class="page-title">导出数据</h1>

<table style="width:100%; border-collapse:separate; border-spacing:0 15px;">
    <tr>
        <td style="width:40%; vertical-align:top; padding-right:15px;">
            <div class="card" style="height:100%;">
                <div class="card-title">数据导出选项</div>
                <div style="padding:30px;">
                    <div style="margin-bottom:15px;">
                        <div style="display:flex; flex-wrap:wrap; align-items:center;">
                            <label style="margin-right:10px; margin-bottom:20px;">选择数据类型：</label>
                            <div style="display:inline-block; margin-right:10px;">
                                <input type="radio" name="export_type" id="type-players" value="players" checked>
                                <label for="type-players">玩家数据一览</label>
                            </div>
                            <div style="display:inline-block;">
                                <input type="radio" name="export_type" id="type-ranks" value="ranks">
                                <label for="type-ranks">军衔分布</label>
                            </div>
                        </div>
                    </div>
                    
                    <div>
                        <div style="display:flex; flex-wrap:wrap; gap:8px;">
                            <button class="btn btn-primary export-btn" data-format="excel" title="导出为Excel格式(.xlsx)" style="min-width:100px;">
                                <i class="fas fa-file-excel"></i>&nbsp;&nbsp;Excel
                            </button>
                        </div>
                        <small style="display:block; color:#6c757d; margin-top:35px;">选择格式后将立即开始导出</small>
                    </div>
                </div>
            </div>
        </td>
        
        <td style="width:60%; vertical-align:top;">
            <div class="card" style="height:100%;">
                <div class="card-title">导出数据说明</div>
                
                <div style="padding:0 15px;" id="players-desc">
                    <p>玩家数据一览包含以下内容：（注：数据基于系统中录入的所有游戏记录计算。导出的数据将按游戏模式分表显示。）</p>
                    <ul style="margin-bottom:0;">
                        <li><strong>玩家昵称</strong>：玩家的游戏昵称</li>
                        <li><strong>总狙杀数</strong>：玩家的总击杀数量</li>
                        <li><strong>总死亡数</strong>：玩家的总死亡次数</li>
                        <li><strong>KD值</strong>：玩家的击杀死亡比率</li>
                        <li><strong>总胜场</strong>：玩家的总胜场数</li>
                        <li><strong>总败场</strong>：玩家的总败场数</li>
                        <li><strong>胜场率</strong>：玩家的胜率百分比</li>
                        <li><strong>军衔</strong>：玩家的军衔等级</li>
                    </ul>
                </div>
                
                <div style="padding:0 15px; display:none;" id="ranks-desc">
                    <p>军衔分布数据包含以下内容：（注：数据基于系统中录入的所有游戏记录计算。导出的数据将按游戏模式分表显示。）</p>
                    <ul style="margin-bottom:0;">
                        <li><strong>军衔</strong>：军衔名称</li>
                        <li><strong>玩家数量</strong>：该军衔的玩家数量</li>
                        <li><strong>总狙杀数</strong>：该军衔玩家的总击杀数量</li>
                        <li><strong>总死亡数</strong>：该军衔玩家的总死亡次数</li>
                        <li><strong>平均KD值</strong>：该军衔玩家的平均击杀死亡比</li>
                        <li><strong>总胜场</strong>：该军衔玩家的总胜场数</li>
                        <li><strong>总败场</strong>：该军衔玩家的总败场数</li>
                        <li><strong>胜率</strong>：该军衔玩家的平均胜率</li>
                    </ul>
                </div>
            </div>
        </td>
    </tr>
</table>

<div class="card" style="margin-top:20px;">
    <div class="card-title">历史导出记录</div>
    <?php
    // 获取导出目录中的文件列表
    $export_files = [];
    if (is_dir(EXCEL_DIR)) {
        $files = scandir(EXCEL_DIR);
        foreach ($files as $file) {
            if ($file != '.' && $file != '..' && !is_dir(EXCEL_DIR . $file)) {
                // 获取文件信息
                $file_info = [
                    'name' => $file,
                    'size' => filesize(EXCEL_DIR . $file),
                    'time' => filemtime(EXCEL_DIR . $file)
                ];
                
                // 检查是否是导出文件（按照命名规则）
                if (strpos($file, 'player_stats_') === 0 || strpos($file, 'rank_stats_') === 0) {
                    $export_files[] = $file_info;
                }
            }
        }
        
        // 按时间排序（最新的在前）
        usort($export_files, function($a, $b) {
            return $b['time'] - $a['time'];
        });
    }
    ?>
    
    <div style="padding:0;">
        <?php if (empty($export_files)): ?>
            <div style="margin:15px; padding:10px; background-color:#d1ecf1; color:#0c5460; border:1px solid #bee5eb; border-radius:4px;">暂无导出记录</div>
        <?php else: ?>
            <div style="overflow-x:auto;">
                <table style="width:100%; border-collapse:collapse; margin-bottom:0;">
                    <thead style="background-color:#f8f9fa;">
                        <tr>
                            <th style="padding:10px; text-align:left; border-bottom:1px solid #dee2e6;">文件名</th>
                            <th style="padding:10px; text-align:left; border-bottom:1px solid #dee2e6;">大小</th>
                            <th style="padding:10px; text-align:left; border-bottom:1px solid #dee2e6;">导出时间</th>
                            <th style="padding:10px; text-align:center; border-bottom:1px solid #dee2e6;">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($export_files as $file): ?>
                            <tr style="border-bottom:1px solid #dee2e6; background-color:#fff;" onmouseover="this.style.backgroundColor='#f8f9fa';" onmouseout="this.style.backgroundColor='#fff';">
                                <td style="padding:10px;"><?php echo $file['name']; ?></td>
                                <td style="padding:10px;"><?php echo Utils::formatFileSize($file['size']); ?></td>
                                <td style="padding:10px;"><?php echo date('Y-m-d H:i:s', $file['time']); ?></td>
                                <td style="padding:10px; text-align:center;">
                                    <a href="download.php?file=<?php echo $file['name']; ?>&type=excel" class="btn btn-primary btn-sm" style="margin-right:5px;">
                                        <i class="fas fa-download"></i> 下载
                                    </a>
                                    <a href="javascript:void(0);" onclick="confirmDelete('确定要删除此文件吗？', function() { window.location.href='delete_file.php?file=<?php echo $file['name']; ?>&type=excel'; })" class="btn btn-danger btn-sm">
                                        <i class="fas fa-trash-alt"></i> 删除
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 处理导出类型选择
    const exportTypeRadios = document.querySelectorAll('input[name="export_type"]');
    const playersDesc = document.getElementById('players-desc');
    const ranksDesc = document.getElementById('ranks-desc');
    
    exportTypeRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.value === 'players') {
                playersDesc.style.display = 'block';
                ranksDesc.style.display = 'none';
            } else {
                playersDesc.style.display = 'none';
                ranksDesc.style.display = 'block';
            }
        });
    });
    
    // 处理导出按钮点击
    const exportBtns = document.querySelectorAll('.export-btn');
    
    exportBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            // 保存按钮原始内容用于恢复
            const originalContent = this.innerHTML;
            const format = this.getAttribute('data-format');
            const exportType = document.querySelector('input[name="export_type"]:checked').value;
            
            // 显示加载提示
            this.innerHTML = `<i class="fas fa-spinner fa-spin"></i>&nbsp;&nbsp;处理中...`;
            this.disabled = true;
            
            // 禁用所有导出按钮，防止重复点击
            exportBtns.forEach(otherBtn => {
                if (otherBtn !== this) {
                    otherBtn.disabled = true;
                }
            });
            
            // 创建并提交表单，而不是直接跳转
            const form = document.createElement('form');
            form.method = 'GET';
            form.action = 'export_data.php';
            
            // 添加格式参数
            const formatInput = document.createElement('input');
            formatInput.type = 'hidden';
            formatInput.name = 'format';
            formatInput.value = format;
            form.appendChild(formatInput);
            
            // 添加类型参数
            const typeInput = document.createElement('input');
            typeInput.type = 'hidden';
            typeInput.name = 'type';
            typeInput.value = exportType;
            form.appendChild(typeInput);
            
            // 添加随机参数，防止浏览器缓存
            const randomInput = document.createElement('input');
            randomInput.type = 'hidden';
            randomInput.name = 'rand';
            randomInput.value = Math.random().toString(36).substring(2, 15);
            form.appendChild(randomInput);
            
            // 将表单添加到文档中并提交
            document.body.appendChild(form);
            
            // 设置超时以防提交失败
            setTimeout(function() {
                // 如果5秒后按钮仍处于禁用状态，恢复所有按钮
                if (btn.disabled) {
                    btn.innerHTML = originalContent;
                    btn.disabled = false;
                    
                    exportBtns.forEach(otherBtn => {
                        otherBtn.disabled = false;
                    });
                }
            }, 5000);
            
            form.submit();
        });
    });
});
</script>

    </div><!-- 结束 .container -->
</div><!-- 结束 .main-content -->

<?php
// 包含底部
include 'includes/footer.php';
?> 