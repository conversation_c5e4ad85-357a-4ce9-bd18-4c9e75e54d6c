<?php
require_once 'config.php';
require_once 'includes/db.php';
require_once 'includes/utils.php';
require_once 'includes/stats_calculator.php';

// 包含头部
include 'includes/header.php';
?>

<div class="container">
<?php
// 获取当前API密钥
$sql = "SELECT * FROM api_settings WHERE is_active = 1 ORDER BY id DESC LIMIT 1";
$api_setting = $db->getRow($sql);
if (!$api_setting) {
    $api_setting = [
        'api_key' => API_KEY,
        'created_at' => date('Y-m-d H:i:s'),
        'last_used' => null,
        'is_active' => 1
    ];
}
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
$host = $_SERVER['HTTP_HOST'];
$base_url = $protocol . $host;
$stats = new StatsCalculator($db);
$playerStats = null;
$playerGames = [];
$nickname = '';
$errorMessage = '';
if (isset($_GET['nickname']) && !empty($_GET['nickname'])) {
    $nickname = Utils::sanitizeInput($_GET['nickname']);
    $playerStats = $stats->getPlayerStats($nickname);
    if ($playerStats) {
        $sql = "SELECT id FROM players WHERE nickname = '" . $db->escape($nickname) . "'";
        $player = $db->getRow($sql);
        if ($player) {
            $playerId = $player['id'];
            $sql = "SELECT g.id, g.unique_id, g.image_file, g.game_type, g.upload_time, gr.kills, gr.deaths, gr.team, gr.wins, gr.losses, p.virtual_ip FROM games g JOIN game_records gr ON g.id = gr.game_id JOIN players p ON gr.player_id = p.id WHERE gr.player_id = {$playerId} ORDER BY g.upload_time DESC";
            $playerGames = $db->getRows($sql);
        }
    } else {
        $errorMessage = "未找到玩家 '{$nickname}'";
    }
} else {
    $errorMessage = "缺少玩家昵称参数";
}
?>

<h1 class="page-title">
    <?php echo isset($playerStats['nickname']) ? htmlspecialchars($playerStats['nickname']) : ''; ?> 的战绩统计详情
</h1>

<?php if ($errorMessage): ?>
<div class="alert alert-danger">
    <?php echo $errorMessage; ?>
</div>
<?php endif; ?>

<?php if ($playerStats): ?>


<div class="card">
    <div class="card-title">玩家统计</div>
    <div class="row">
        <div class="col-md-6">
            <div class="stat-box">
                <div class="stat-item">
                    <span class="stat-label">总狙杀数</span>
                    <span class="stat-value"><?php echo $playerStats['total_kills']; ?></span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">总死亡数</span>
                    <span class="stat-value"><?php echo $playerStats['total_deaths']; ?></span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">KD值</span>
                    <span class="stat-value"><?php echo $playerStats['kd']; ?></span>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="stat-box">
                <div class="stat-item">
                    <span class="stat-label">总胜场</span>
                    <span class="stat-value"><?php echo $playerStats['total_wins']; ?></span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">总败场</span>
                    <span class="stat-value"><?php echo $playerStats['total_losses']; ?></span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">胜率</span>
                    <span class="stat-value"><?php echo $playerStats['win_rate']; ?></span>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-title">游戏场次列表</div>
    <?php if (empty($playerGames)): ?>
    <div class="alert alert-info">该玩家暂无游戏记录</div>
    <?php else: ?>
    <div class="table-responsive">
        <table class="table table-hover">
            <thead class="table-header">
                <tr>
                    <th>游戏ID</th>
                    <th>游戏类型</th>
                    <th>时间</th>
                    
                    <th>队伍</th>
                    <th class="text-center">击杀</th>
                    <th class="text-center">死亡</th>
                    <th class="text-center">胜场</th>
                    <th class="text-center">败场</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($playerGames as $game): ?>
                <tr>
                    <td>
                        <a href="javascript:void(0);" 
                           class="show-game-image" 
                           data-image="<?php echo  'https://img1.lxbl.online/images/' . $game['image_file']; ?>">
                            <?php echo $game['unique_id']; ?>
                        </a>
                    </td>
                    <td><?php echo $game['game_type']; ?></td>
                    <td><?php echo date('Y-m-d H:i', strtotime($game['upload_time'])); ?></td>
                    
                    <td><span class="team-tag"><?php echo $game['team']; ?></span></td>
                    <td class="text-center"><?php echo $game['kills']; ?></td>
                    <td class="text-center"><?php echo $game['deaths']; ?></td>
                    <td class="text-center"><?php echo $game['wins']; ?></td>
                    <td class="text-center"><?php echo $game['losses']; ?></td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
    <?php endif; ?>
</div>

<style>
.player-info {
    padding: 15px;
}
.rank-badge {
    display: inline-block;
    padding: 5px 10px;
    background-color: #6c757d;
    color: white;
    border-radius: 4px;
    margin-top: 5px;
}
.api-info {
    margin-top: 15px;
    background-color: #f8f9fa;
    padding: 10px;
    border-radius: 4px;
}
.stat-box {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -2px;
}
.stat-item {
    flex: 1 0 calc(33.33% - 4px);
    margin: 2px;
    background-color: #f8f9fa;
    padding: 8px 5px;
    border-radius: 4px;
    text-align: center;
}
.stat-label {
    display: block;
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 2px;
}
.stat-value {
    display: block;
    font-size: 16px;
    font-weight: bold;
}

#gameImageModal {
    display: none;
    position: fixed;
    z-index: 9999;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0,0,0,0.7);
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.3s;
    padding: 20px 200px 20px 80px;
    border-radius: 12px;
    min-width: 60%;
}
#gameImageModalImg {
    max-width: 90vw;
    max-height: 80vh;
    display: block;
    margin: auto;
    box-shadow: 0 0 32px #000;
    border-radius: 8px;
    animation: popIn 0.3s;
}
#closeGameImageModal {
    position: absolute;
    top: 10px;
    right: 20px;
    color: #fff;
    font-size: 30px;
    cursor: pointer;
    font-weight: bold;
    text-shadow: 0 2px 8px #000;
    transition: color 0.2s;
}
#closeGameImageModal:hover {
    color: #4f8cff;
}
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}
@keyframes popIn {
    from { transform: scale(0.8); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
}
@media (max-width: 768px) {
    .card { padding: 16px 6px 10px 6px; }
    .stat-box { flex-direction: column; gap: 8px; }
    .stat-item { font-size: 1rem; }
    .page-title { font-size: 1.3rem; }
    .card-title { font-size: 1.1rem; }
    .table td, .table th {
        padding: 8px 6px;
        font-size: 0.9em;
    }
}
.table-responsive {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.table {
    margin-bottom: 0;
    background: #fff;
}

.table-header {
    background: #f8f9fa;
}

.table thead th {
    border-bottom: 2px solid #dee2e6;
    padding: 12px 8px;
    font-weight: 600;
}

.table td {
    padding: 12px 8px;
    vertical-align: middle;
}

.table-hover tbody tr:hover {
    background-color: #f8f9fa;
}

.show-game-image {
    color: #007bff;
    text-decoration: none;
}

.show-game-image:hover {
    color: #0056b3;
    text-decoration: none;
}

.team-tag {
    display: inline-block;
    padding: 2px 8px;
    background: #e9ecef;
    border-radius: 4px;
    font-size: 0.9em;
}

code {
    background: #f8f9fa;
    padding: 2px 4px;
    border-radius: 3px;
    color: #666;
}
</style>


<!-- 图片弹窗 -->
<div id="gameImageModal">
    <span id="closeGameImageModal">&times;</span>
    <img id="gameImageModalImg" src="" alt="游戏图片">
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 为所有图片链接添加点击事件
    document.querySelectorAll('.show-game-image').forEach(function(el) {
        el.addEventListener('click', function() {
            var imgUrl = this.getAttribute('data-image');
            document.getElementById('gameImageModalImg').src = imgUrl;
            document.getElementById('gameImageModal').style.display = 'flex';
        });
    });
    
    // 关闭按钮事件
    document.getElementById('closeGameImageModal').addEventListener('click', function() {
        document.getElementById('gameImageModal').style.display = 'none';
    });
    
    // 点击弹窗背景关闭
    document.getElementById('gameImageModal').addEventListener('click', function(e) {
        if (e.target === this) {
            this.style.display = 'none';
        }
    });
});
</script>


<?php endif; ?>

</div><!-- 结束 .container -->
</div><!-- 结束 .main-content -->

<?php
// 包含底部
include 'includes/footer.php';
?> 