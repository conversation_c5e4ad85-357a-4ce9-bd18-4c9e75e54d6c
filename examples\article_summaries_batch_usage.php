<?php
/**
 * Example usage of the article_summaries_batch API endpoint
 * 
 * This file demonstrates how to use the new batch endpoint to retrieve
 * multiple article summaries efficiently.
 */

class ArticleBatchClient {
    private $baseUrl;
    private $apiKey;
    
    public function __construct($baseUrl, $apiKey = null) {
        $this->baseUrl = rtrim($baseUrl, '/');
        $this->apiKey = $apiKey;
    }
    
    /**
     * Get multiple article summaries in a single request
     * 
     * @param array $articleIds Array of article IDs to retrieve
     * @return array API response
     */
    public function getArticleSummariesBatch($articleIds) {
        // Validate input
        if (empty($articleIds) || !is_array($articleIds)) {
            throw new InvalidArgumentException('Article IDs must be a non-empty array');
        }
        
        // Convert to comma-separated string
        $idsParam = implode(',', array_map('intval', $articleIds));
        
        // Build URL
        $url = $this->baseUrl . '/admin/api.php?path=article_summaries_batch&ids=' . urlencode($idsParam);
        
        // Add API key if provided
        if ($this->apiKey) {
            $url .= '&api_key=' . urlencode($this->apiKey);
        }
        
        // Make request
        $context = stream_context_create([
            'http' => [
                'method' => 'GET',
                'header' => $this->apiKey ? "X-API-Key: {$this->apiKey}\r\n" : '',
                'timeout' => 30
            ]
        ]);
        
        $response = file_get_contents($url, false, $context);
        
        if ($response === false) {
            throw new RuntimeException('Failed to make API request');
        }
        
        $data = json_decode($response, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new RuntimeException('Invalid JSON response: ' . json_last_error_msg());
        }
        
        return $data;
    }
    
    /**
     * Get article summaries with error handling and retry logic
     * 
     * @param array $articleIds Array of article IDs
     * @param int $maxRetries Maximum number of retries
     * @return array Processed results
     */
    public function getArticleSummariesWithRetry($articleIds, $maxRetries = 3) {
        $attempt = 0;
        
        while ($attempt < $maxRetries) {
            try {
                $response = $this->getArticleSummariesBatch($articleIds);
                
                if ($response['status'] === 'success') {
                    return [
                        'success' => true,
                        'articles' => $response['data']['found_articles'],
                        'not_found' => $response['data']['not_found_ids'],
                        'total_found' => $response['data']['total_found'],
                        'total_requested' => $response['data']['total_requested']
                    ];
                } else {
                    // Handle API errors
                    return [
                        'success' => false,
                        'error' => $response['message'],
                        'articles' => [],
                        'not_found' => $articleIds
                    ];
                }
            } catch (Exception $e) {
                $attempt++;
                if ($attempt >= $maxRetries) {
                    return [
                        'success' => false,
                        'error' => 'Max retries exceeded: ' . $e->getMessage(),
                        'articles' => [],
                        'not_found' => $articleIds
                    ];
                }
                
                // Wait before retry (exponential backoff)
                sleep(pow(2, $attempt - 1));
            }
        }
    }
}

// Example usage
try {
    // Initialize client
    $client = new ArticleBatchClient('http://localhost/BDLX');
    
    // Example 1: Get multiple articles
    echo "Example 1: Getting multiple articles\n";
    echo str_repeat('-', 50) . "\n";
    
    $articleIds = [1, 2, 3];
    $result = $client->getArticleSummariesWithRetry($articleIds);
    
    if ($result['success']) {
        echo "Successfully retrieved {$result['total_found']} out of {$result['total_requested']} articles\n";
        
        foreach ($result['articles'] as $article) {
            echo "- [{$article['id']}] {$article['title']}\n";
            if ($article['summary']) {
                echo "  Summary: " . substr($article['summary'], 0, 100) . "...\n";
            }
            echo "  Category: {$article['category_name']}\n\n";
        }
        
        if (!empty($result['not_found'])) {
            echo "Articles not found: " . implode(', ', $result['not_found']) . "\n";
        }
    } else {
        echo "Error: {$result['error']}\n";
    }
    
    echo "\n" . str_repeat('=', 50) . "\n\n";
    
    // Example 2: Handle large batches (chunking)
    echo "Example 2: Handling large batches with chunking\n";
    echo str_repeat('-', 50) . "\n";
    
    $largeArticleList = range(1, 100); // 100 articles
    $batchSize = 20; // Process in chunks of 20
    $allArticles = [];
    $allNotFound = [];
    
    $chunks = array_chunk($largeArticleList, $batchSize);
    
    foreach ($chunks as $chunkIndex => $chunk) {
        echo "Processing chunk " . ($chunkIndex + 1) . "/" . count($chunks) . "...\n";
        
        $result = $client->getArticleSummariesWithRetry($chunk);
        
        if ($result['success']) {
            $allArticles = array_merge($allArticles, $result['articles']);
            $allNotFound = array_merge($allNotFound, $result['not_found']);
            echo "  Found {$result['total_found']} articles in this chunk\n";
        } else {
            echo "  Error in chunk: {$result['error']}\n";
            $allNotFound = array_merge($allNotFound, $chunk);
        }
        
        // Small delay between chunks to be nice to the server
        usleep(100000); // 100ms
    }
    
    echo "\nFinal results:\n";
    echo "Total articles found: " . count($allArticles) . "\n";
    echo "Total articles not found: " . count($allNotFound) . "\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
