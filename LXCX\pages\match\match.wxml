<view class="container">

  <!-- 搜索历史 -->
  <view class="history-box" wx:if="{{searchHistory.length > 0}}">
    <view class="history-title">
      <text>搜索历史</text>
      <text class="clear-history" bindtap="clearHistory">清空</text>
    </view>
    <view class="history-tags">
      <text class="history-tag" wx:for="{{searchHistory}}" wx:key="index" bindtap="onTapHistory" data-keyword="{{item}}">{{item}}</text>
    </view>
  </view>
  
  <!-- 玩家列表 -->
  <view class="players-list">

    <!-- 加载中状态 -->
    <view class="loading" wx:if="{{loading && players.length === 0}}">
      <text>正在搜寻英雄...</text>
    </view>
    <!-- 错误状态 -->
    <view class="error-box" wx:elif="{{errorMsg}}">
      <icon type="warn" size="50"></icon>
      <text class="error-msg">{{errorMsg}}</text>
     
      <button class="retry-btn" bindtap="retryLoading">下拉刷新</button>
    </view>
    <!-- 无数据状态 -->
    <view class="empty" wx:elif="{{players.length === 0}}">
      <text>暂无英雄的数据</text>
    </view>

<!-- 搜索区域 -->
  <view class="search-box">
    
    🔍<input class="search-input" placeholder="搜索玩家昵称(下拉刷新)[八百玩家的选择]" value="{{searchKeyword}}" bindinput="onSearchInput" bindconfirm="doSearch" confirm-type="search"></input>
  </view>

<!-- 排序选项 -->
<view class="sort-options">

  <view class="sort-buttons">
    <view class="sort-item {{sortField === 'kd' ? 'active' : ''}}" bindtap="onSort" data-field="kd">
     
      ⚔️ KD  {{sortField === 'kd' ? (sortOrder === 'asc' ? '↑' : '↓') : ''}}
    </view>
    <view class="sort-item {{sortField === 'win_rate' ? 'active' : ''}}" bindtap="onSort" data-field="win_rate">
      
      🚩胜率 {{sortField === 'win_rate' ? (sortOrder === 'asc' ? '↑' : '↓') : ''}}
    </view>
    <view class="sort-item {{sortField === 'total_kills' ? 'active' : ''}}" bindtap="onSort" data-field="total_kills">
      
      🎯狙杀 {{sortField === 'total_kills' ? (sortOrder === 'asc' ? '↑' : '↓') : ''}}
    </view>
    <view class="sort-item {{sortField === 'total_deaths' ? 'active' : ''}}" bindtap="onSort" data-field="total_deaths">
      
      💀死亡 {{sortField === 'total_deaths' ? (sortOrder === 'asc' ? '↑' : '↓') : ''}}
    </view>
  </view>
  
</view>

    <!-- 玩家卡片 -->
    <view class="player-card" wx:for="{{players}}" wx:key="id" bindtap="onTapPlayer" data-nickname="{{item.nickname}}">
      <image class="player-avatar" lazy-load="{{true}}" src="{{item.avatar || '/images/default-avatar.png'}}"></image>
      <view class="player-info">
        <view class="player-name">{{item.nickname}} 
         <text class="vip-tag" wx:if="{{item.player_rank}}">{{item.player_rank}}</text>
         <text wx:else>未知</text>
        </view>
        <view class="player-stats">
          <text>KD: {{item.kd || '0.00'}}</text>
          <text>胜率: {{item.win_rate || '0.00%'}}</text>
        </view>
        <view class="player-summary">狙杀: {{item.total_kills || 0}} | 死亡: {{item.total_deaths || 0}} </view>
      </view>
      
      <view class="arrow">
        <image src="/images/arrow-right.png"></image>
      </view>
     
    </view>
    
    <!-- 加载更多区域 -->
    <view class="load-more-area" wx:if="{{players.length > 0}}">
      <view wx:if="{{loading}}" class="loading-indicator">
        <view class="loading-spinner"></view>
        <text>加载中...</text>
      </view>
      <view wx:elif="{{hasMore}}" class="load-more-button" bindtap="onClickLoadMore">
        点击加载更多
      </view>
      <view wx:else class="no-more-data">
        已经到底啦~
      </view>
    </view>
  </view>
  
</view> 
