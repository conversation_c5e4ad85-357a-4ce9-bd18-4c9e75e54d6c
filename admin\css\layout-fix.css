/* 
 * 布局修复和增强 CSS
 * 这个文件包含对主样式的补充和修复，确保所有页面布局一致和美观
 */

/* 修复内容容器溢出问题 */
.main-content {
    overflow-y: auto !important;
    overflow-x: hidden !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* 确保内容区域正确显示 */
.content-wrapper {
    display: flex !important;
    flex-direction: column !important;
    flex: 1 !important;
}

/* 修复页面边距问题 */
.container {
    padding: 0 25px !important;
}

/* 确保卡片内部元素正确对齐 */
.card-body {
    padding: 25px !important;
}

.card-header {
    padding: 18px 25px !important;
}

/* 表格样式增强 */
.table th {
    font-weight: 600 !important;
    color: var(--gray-700) !important;
}

.table td {
    vertical-align: middle !important;
}

/* 表单元素对齐和间距 */
.form-group {
    margin-bottom: 20px !important;
}

.filter-form .form-group {
    margin-bottom: 0 !important;
}

/* 按钮样式增强 */
.btn {
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1) !important;
    transition: all 0.2s ease !important;
}

.btn:hover {
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15) !important;
    transform: translateY(-1px) !important;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary), var(--primary-dark)) !important;
    border: none !important;
}

.btn-success {
    background: linear-gradient(135deg, var(--success), #27ae60) !important;
    border: none !important;
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger), #c0392b) !important;
    border: none !important;
}

.btn-secondary {
    background: linear-gradient(135deg, var(--secondary), #495057) !important;
    border: none !important;
}

/* 导航链接样式增强 */
.nav-link {
    transition: all 0.25s ease !important;
}

.nav-link:hover {
    transform: translateX(5px) !important;
}

.nav-link.active {
    position: relative !important;
}

.nav-link.active::before {
    content: '' !important;
    position: absolute !important;
    left: -15px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    width: 4px !important;
    height: 20px !important;
    background-color: white !important;
    border-radius: 2px !important;
}

/* 提升可视层次感 */
.card {
    border: none !important;
    transition: transform 0.3s ease, box-shadow 0.3s ease !important;
}

.card:hover {
    transform: translateY(-3px) !important;
}

/* 表格行交替色设置 */
.table tbody tr:nth-child(even) {
    background-color: rgba(0, 0, 0, 0.02) !important;
}

/* 增强型动画效果 */
.fade-in {
    animation: fadeIn 0.5s ease !important;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 数据为空的提示样式优化 */
.alert-info {
    text-align: center !important;
    padding: 25px !important;
    font-size: 15px !important;
}

/* 针对移动设备的响应式布局优化 */
@media (max-width: 768px) {
    .stats-cards {
        grid-template-columns: repeat(2, 1fr) !important;
    }
    
    .container {
        padding: 0 15px !important;
    }
    
    .card-body, .card-header {
        padding: 15px !important;
    }
    
    .table th, .table td {
        padding: 10px !important;
    }
    
    .btn {
        padding: 8px 12px !important;
    }
}

@media (max-width: 576px) {
    .stats-cards {
        grid-template-columns: 1fr !important;
    }
    
    .header-right .user-menu {
        display: none !important;
    }
    
    .header-left {
        width: 100% !important;
        justify-content: space-between !important;
    }
    
    .top-header {
        padding: 0 15px !important;
    }
}

/* 确保表单在移动设备上正确显示 */
@media (max-width: 768px) {
    .filter-form {
        flex-direction: column !important;
        align-items: stretch !important;
    }
    
    .filter-form .form-group {
        margin-bottom: 10px !important;
    }
}

/* 确保滚动条样式一致 */
::-webkit-scrollbar {
    width: 6px !important;
    height: 6px !important;
}

::-webkit-scrollbar-track {
    background-color: transparent !important;
}

::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2) !important;
    border-radius: 3px !important;
}

::-webkit-scrollbar-thumb:hover {
    background-color: rgba(0, 0, 0, 0.3) !important;
}

.main-content {
    flex: 1;
    height: calc(100vh - var(--header-height));
    overflow-y: auto !important;
    overflow-x: hidden !important;
    position: relative;
    z-index: 5;
    padding: 20px 0; /* 修改这里，移除左右内边距 */
    background: var(--gray-100);
}

.main-content > .container {
    min-height: 100%; /* 添加这个确保容器至少占满整个高度 */
    display: flex;
    flex-direction: column;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px 15px;
}

/* 标签导航样式 */
.tab-navigation {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    background: white;
    padding: 15px 20px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.tab-item {
    padding: 8px 16px;
    border-radius: 4px;
    color: var(--gray-700);
    text-decoration: none;
    transition: all 0.3s;
}

.tab-item:hover {
    background: var(--gray-100);
}

.tab-item.active {
    background: var(--primary);
    color: white;
}

/* 内容卡片样式 */
.tab-content {
    display: none;
    margin-bottom: 20px;
}

.tab-content.active {
    display: block;
}

/* 仪表盘容器样式 */
.dashboard-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* 数据统计卡片网格 */
.stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.stats-card {
    background: white;
    padding: 20px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    

.stats-card-value {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 5px;
}

.stats-card-title {
    color: var(--gray-600);
    font-size: 14px;
}

/* 布局修复样式 */
html, body {
    height: 100vh;
    margin: 0;
    padding: 0;
    overflow: hidden;
}

.page-wrapper {
    height: 100vh;
    overflow: hidden;
}

.wrapper {
    display: flex;
    height: 100vh;
    overflow: hidden;
}

/* 确保表格内容可以水平滚动 */
.table-responsive {
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

/* 确保卡片内容正确显示 */
.card {
    margin-bottom: 20px;
    height: auto;
    overflow: visible;
}

.card-body {
    overflow: visible;
}

/* 确保模态框内容可以滚动 */
.modal-body {
    max-height: calc(100vh - 200px);
    overflow-y: auto;
}

/* 针对不同设备的响应式调整 */
@media (max-width: 768px) {
    .content-wrapper {
        margin-left: 0;
        width: 100%;
    }
    
    .main-content {
        height: calc(100vh - 50px);
        padding: 15px;
    }

    .tab-navigation {
        flex-wrap: wrap;
        padding: 10px;
    }
    
    .tab-item {
        width: calc(50% - 5px);
        text-align: center;
    }
    
    .stats-cards {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
    }

    .container {
        padding: 0 10px;
    }
}

/* 修复可能的z-index问题 */
.top-header {
    position: relative;
    z-index: 10;
}

.side-nav {
    z-index: 1030;
}

.main-content {
    z-index: 5;
}

/* 确保表单和输入框正确显示 */
.form-group {
    margin-bottom: 1rem;
    position: relative;
}

/* 确保图片预览区域正确显示 */
.image-preview {
    max-width: 100%;
    overflow: hidden;
}

/* 修复编辑器区域的显示问题 */
.editor-container {
    height: auto;
    overflow: visible;
}

/* 修复标签和过滤器区域的显示问题 */
.filter-card {
    margin-bottom: 20px;
    overflow: visible;
}

.tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 15px;
}