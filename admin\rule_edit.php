<?php
// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    require_once 'includes/db.php';
    
    $title = trim($_POST['title']);
    $content = trim($_POST['content']);
    $status = isset($_POST['status']) ? 1 : 0;
    $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
    
    if (empty($title)) {
        $error = "标题不能为空";
    } else {
        if ($id > 0) {
            $query = "UPDATE rules SET title = ?, content = ?, status = ?, updated_at = NOW() WHERE id = ?";
            $stmt = $mysqli->prepare($query);
            $stmt->bind_param('ssii', $title, $content, $status, $id);
            $stmt->execute();
            $stmt->close();
        } else {
            $query = "INSERT INTO rules (title, content, status, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())";
            $stmt = $mysqli->prepare($query);
            $stmt->bind_param('ssi', $title, $content, $status);
            $stmt->execute();
            $stmt->close();
        }
        
        header("Location: rules.php");
        exit;
    }
}

// 获取规则数据
$id = isset($_GET['id']) ? intval($_GET['id']) : 0;
$rule = ['title' => '', 'content' => '', 'status' => 0];

if ($id > 0) {
    require_once 'includes/db.php';
    $query = "SELECT * FROM rules WHERE id = ?";
    $stmt = $mysqli->prepare($query);
    $stmt->bind_param('i', $id);
    $stmt->execute();
    $result = $stmt->get_result();
    if ($row = $result->fetch_assoc()) {
        $rule = $row;
    } else {
        header("Location: rules.php");
        exit;
    }
    $stmt->close();
}

// 加载页面模板
require_once 'includes/header.php';
?>

<div class="card">
    <div class="card-header">
        <h3 class="card-title"><?php echo $id ? '编辑规则通知' : '添加规则通知'; ?></h3>
    </div>
    <div class="card-body">
        <?php if (isset($error)): ?>
        <div class="alert alert-danger">
            <?php echo htmlspecialchars($error); ?>
        </div>
        <?php endif; ?>
        
        <form method="post" class="rule-form">
            <?php if ($id): ?>
            <input type="hidden" name="id" value="<?php echo $id; ?>">
            <?php endif; ?>
            
            <div class="form-group">
                <label for="title">标题</label>
                <input type="text" id="title" name="title" class="form-control" value="<?php echo htmlspecialchars($rule['title']); ?>" required>
            </div>
            
            <div class="form-group">
                <label for="content">内容</label>
                <textarea id="content" name="content" class="form-control" rows="10" required><?php echo htmlspecialchars($rule['content']); ?></textarea>
            </div>
            
            <div class="form-group">
                <label class="checkbox-container">
                    <input type="checkbox" name="status" value="1" <?php echo $rule['status'] ? 'checked' : ''; ?>>
                    <span class="checkbox-text">立即发布</span>
                </label>
            </div>
            
            <div class="form-actions">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> 保存
                </button>
                <a href="rules.php" class="btn btn-secondary">
                    <i class="fas fa-times"></i> 取消
                </a>
            </div>
        </form>
    </div>
</div>

<style>
.form-group {
    margin-bottom: 1.5rem;
}

.form-control {
    display: block;
    width: 100%;
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    line-height: 1.5;
    color: var(--gray-900);
    background-color: #fff;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    transition: border-color 0.15s ease-in-out;
}

.form-control:focus {
    border-color: var(--primary);
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(67, 97, 238, 0.25);
}

textarea.form-control {
    resize: vertical;
    min-height: 100px;
}

label {
    display: inline-block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--gray-700);
}

.checkbox-container {
    display: flex;
    align-items: center;
    cursor: pointer;
    user-select: none;
}

.checkbox-container input[type="checkbox"] {
    margin-right: 0.5rem;
}

.checkbox-text {
    font-weight: normal;
}

.form-actions {
    margin-top: 2rem;
    display: flex;
    gap: 1rem;
}

.btn-secondary {
    background-color: var(--secondary);
}

.btn-secondary:hover {
    background-color: #5a6268;
}

.alert {
    padding: 0.75rem 1.25rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: var(--border-radius);
}

.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

.rule-form {
    max-width: 800px;
}
</style>

<?php require_once 'includes/footer.php'; ?> 