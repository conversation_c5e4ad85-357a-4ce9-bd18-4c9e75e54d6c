<?php
require_once '../config.php';
require_once '../includes/db.php';
require_once '../includes/utils.php';

// 检查是否是AJAX请求或API请求
Utils::checkLoginAjax();

// 设置响应头
header('Content-Type: application/json');

// 获取参数
$game_type = isset($_GET['type']) ? Utils::sanitizeInput($_GET['type']) : '暗杀';
$player_id = isset($_GET['player_id']) ? (int)$_GET['player_id'] : null;
$search = isset($_GET['search']) ? Utils::sanitizeInput($_GET['search']) : '';
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 20;
$sort_by = isset($_GET['sort']) ? Utils::sanitizeInput($_GET['sort']) : 'kills';
$order = isset($_GET['order']) ? Utils::sanitizeInput($_GET['order']) : 'desc';

// 验证游戏类型
$valid_types = ['暗杀', '死斗', '盟主'];
if (!in_array($game_type, $valid_types)) {
    Utils::apiResponse('error', '无效的游戏类型', null);
    exit;
}

// 计算偏移量
$offset = ($page - 1) * $limit;

// 构建查询条件
$where = "WHERE gm.game_type = '{$game_type}'";
if (!empty($search)) {
    $where .= " AND (p.nickname LIKE '%{$search}%' OR p.player_rank LIKE '%{$search}%')";
}
if ($player_id) {
    $where .= " AND p.id = {$player_id}";
}

// 构建排序条件
$order_sql = '';
switch ($sort_by) {
    case 'nickname':
        $order_sql = "ORDER BY p.nickname {$order}";
        break;
    case 'kills':
        $order_sql = "ORDER BY total_kills {$order}";
        break;
    case 'deaths':
        $order_sql = "ORDER BY total_deaths {$order}";
        break;
    case 'kd':
        $order_sql = "ORDER BY (CASE WHEN total_deaths = 0 THEN total_kills ELSE total_kills / total_deaths END) {$order}";
        break;
    case 'wins':
        $order_sql = "ORDER BY total_wins {$order}";
        break;
    case 'losses':
        $order_sql = "ORDER BY total_losses {$order}";
        break;
    case 'rank':
        $order_sql = "ORDER BY p.player_rank {$order}";
        break;
    default:
        $order_sql = "ORDER BY total_kills {$order}";
        break;
}

// 获取总记录数
$count_sql = "SELECT COUNT(DISTINCT p.id) as total 
              FROM players p
              LEFT JOIN game_records gr ON p.id = gr.player_id
              LEFT JOIN games gm ON gr.game_id = gm.id
              {$where}";
$result = $db->getRow($count_sql);
$total_records = $result['total'];
$total_pages = ceil($total_records / $limit);

// 构建SQL查询
$sql = "SELECT 
            p.id as player_id,
            p.nickname,
            p.player_rank,
            p.is_banned,
            SUM(gr.kills) as total_kills,
            SUM(gr.deaths) as total_deaths,
            SUM(gr.wins) as total_wins,
            SUM(gr.losses) as total_losses,
            (CASE WHEN SUM(gr.deaths) = 0 THEN SUM(gr.kills) ELSE SUM(gr.kills) / SUM(gr.deaths) END) as kd,
            (CASE WHEN (SUM(gr.wins) + SUM(gr.losses)) = 0 THEN 0 ELSE (SUM(gr.wins) / (SUM(gr.wins) + SUM(gr.losses))) * 100 END) as win_rate
        FROM 
            players p
        LEFT JOIN 
            game_records gr ON p.id = gr.player_id
        LEFT JOIN
            games gm ON gr.game_id = gm.id
        {$where}
        GROUP BY 
            p.id, p.nickname, p.player_rank, p.is_banned
        {$order_sql}
        LIMIT {$offset}, {$limit}";

$players_data = $db->getRows($sql);

// 计算KPR和DPR
foreach ($players_data as &$player) {
    $total_games = $player['total_wins'] + $player['total_losses'];
    $player['kpr'] = ($total_games > 0) ? $player['total_kills'] / $total_games : 0;
    $player['dpr'] = ($total_games > 0) ? $player['total_deaths'] / $total_games : 0;
    
    // 格式化数值
    $player['kd'] = number_format($player['kd'], 2);
    $player['win_rate'] = number_format($player['win_rate'], 2);
    $player['kpr'] = number_format($player['kpr'], 2);
    $player['dpr'] = number_format($player['dpr'], 2);
}

// 构建响应
$response = [
    'status' => 'success',
    'message' => '获取' . $game_type . '模式数据成功',
    'data' => [
        'game_type' => $game_type,
        'total_records' => $total_records,
        'total_pages' => $total_pages,
        'current_page' => $page,
        'players' => $players_data
    ]
];

echo json_encode($response, JSON_UNESCAPED_UNICODE);