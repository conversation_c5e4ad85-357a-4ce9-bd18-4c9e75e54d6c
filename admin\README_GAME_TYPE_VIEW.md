# 游戏类型数据查询功能说明

## 功能概述

根据方案二，我们优化了现有的数据库结构，实现了按游戏类型（暗杀/死斗/盟主）查询游戏数据的功能。主要通过以下方式实现：

1. 在game_records表中添加了三个新字段：game_type, virtual_ip, player_rank
2. 创建了三个数据库视图，分别对应三种游戏类型的数据
3. 开发了专门的游戏类型数据查询页面

## 数据库更改

### 1. 表结构更改

在game_records表中添加了以下字段：

- `game_type` ENUM('暗杀', '死斗', '盟主') - 游戏类型
- `virtual_ip` VARCHAR(50) - 大厅虚拟IP
- `player_rank` VARCHAR(50) - 大厅军衔

同时为game_type字段创建了索引，以提高查询性能。

### 2. 数据库视图

创建了三个视图，分别对应三种游戏类型：

- `assassination_games` - 暗杀游戏数据
- `deathmatch_games` - 死斗游戏数据
- `boss_games` - 盟主游戏数据

每个视图包含以下字段：
- game_id - 游戏记录ID
- game_type - 游戏类型
- nickname - 玩家昵称
- virtual_ip - 大厅虚拟IP
- player_rank - 大厅军衔
- kills - 狙杀
- deaths - 死亡
- team - 分组（流星/蝴蝶）
- wins - 胜场
- losses - 败场

## 新增文件

1. `update_db_structure.php` - 数据库结构更新脚本
2. `game_type_view.php` - 游戏类型数据查询页面
3. `README_GAME_TYPE_VIEW.md` - 功能说明文档

## 修改文件

1. `create.php` - 更新了插入和更新游戏记录的代码，确保新字段能够正确处理
2. `includes/header.php` - 添加了游戏类型数据查询页面的导航链接

## 使用方法

### 1. 更新数据库结构

首先需要运行数据库结构更新脚本：

1. 访问 `http://您的网站/admin/update_db_structure.php`
2. 脚本会自动添加新字段、创建索引和视图，并同步现有数据

### 2. 查看游戏类型数据

1. 登录管理后台
2. 在左侧导航菜单中点击"游戏类型数据"
3. 在页面顶部选择游戏类型（暗杀/死斗/盟主）
4. 可以使用搜索框搜索特定玩家或军衔

### 3. 数据库查询示例

如果需要直接查询数据库，可以使用以下SQL示例：

```sql
-- 查询所有暗杀游戏数据
SELECT * FROM assassination_games;

-- 查询所有死斗游戏数据
SELECT * FROM deathmatch_games;

-- 查询所有盟主游戏数据
SELECT * FROM boss_games;

-- 按玩家昵称搜索
SELECT * FROM assassination_games WHERE nickname LIKE '%玩家名称%';

-- 按军衔搜索
SELECT * FROM deathmatch_games WHERE player_rank LIKE '%军衔名称%';

-- 按KD值排序
SELECT *, 
  CASE WHEN deaths = 0 THEN kills ELSE kills/deaths END as kd 
FROM boss_games 
ORDER BY kd DESC;
```

## 注意事项

1. 此方案保留了原有的数据库结构，只是通过添加冗余字段和创建视图来优化查询性能
2. game_records表中的game_type、virtual_ip和player_rank字段是从games表和players表同步过来的冗余数据
3. 在添加或更新游戏记录时，系统会自动维护这些冗余字段的值
4. 如果数据量非常大，可以考虑定期维护索引或使用数据库分区技术进一步优化性能 