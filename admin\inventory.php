<?php
require_once 'config.php';
require_once 'includes/db.php';
require_once 'includes/utils.php';
require_once 'includes/stats_calculator.php';

// 包含头部
include 'includes/header.php';
?>
<?php

// 创建统计计算器
$stats = new StatsCalculator($db);

// 获取筛选参数
$sort_by = isset($_GET['sort']) ? Utils::sanitizeInput($_GET['sort']) : 'kills';
$order = isset($_GET['order']) ? Utils::sanitizeInput($_GET['order']) : 'desc';
$search = isset($_GET['search']) ? Utils::sanitizeInput($_GET['search']) : '';
$game_type = isset($_GET['game_type']) ? Utils::sanitizeInput($_GET['game_type']) : '';

// 获取玩家分页数据
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$page = max(1, $page);
$per_page = 16;
$offset = ($page - 1) * $per_page;

// 获取军衔分页数据
$rank_page = isset($_GET['rank_page']) ? (int)$_GET['rank_page'] : 1;
$rank_page = max(1, $rank_page);
$rank_per_page = 16;
$rank_offset = ($rank_page - 1) * $rank_per_page;

// 获取当前活跃的标签
$active_tab = isset($_GET['active_tab']) ? Utils::sanitizeInput($_GET['active_tab']) : 'player-data';

// 构建查询条件
$where_conditions = [];
if (!empty($search)) {
    $where_conditions[] = "(p.nickname LIKE '%{$search}%' OR p.player_rank LIKE '%{$search}%')";
}
if (!empty($game_type)) {
    $where_conditions[] = "gm.game_type = '{$game_type}'";
}
$where = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// 构建排序条件
$order_sql = '';
switch ($sort_by) {
    case 'nickname':
        $order_sql = "ORDER BY p.nickname {$order}";
        break;
    case 'kills':
        $order_sql = "ORDER BY total_kills {$order}";
        break;
    case 'deaths':
        $order_sql = "ORDER BY total_deaths {$order}";
        break;
    case 'kd':
        $order_sql = "ORDER BY (CASE WHEN total_deaths = 0 THEN total_kills ELSE total_kills / total_deaths END) {$order}";
        break;
    case 'wins':
        $order_sql = "ORDER BY total_wins {$order}";
        break;
    case 'losses':
        $order_sql = "ORDER BY total_losses {$order}";
        break;
    case 'rank':
        $order_sql = "ORDER BY p.player_rank {$order}";
        break;
    default:
        $order_sql = "ORDER BY total_kills {$order}";
        break;
}

// 获取总记录数
$count_sql = "SELECT COUNT(DISTINCT p.id) as total
              FROM players p
              LEFT JOIN game_records g ON p.id = g.player_id
              LEFT JOIN games gm ON g.game_id = gm.id
              {$where}";
$result = $db->getRow($count_sql);
$total_records = $result['total'];
$total_pages = ceil($total_records / $per_page);

// 获取玩家数据
$sql = "SELECT
            p.id as player_id,
            p.nickname,
            p.player_rank,
            p.is_banned,
            SUM(g.kills) as total_kills,
            SUM(g.deaths) as total_deaths,
            SUM(g.wins) as total_wins,
            SUM(g.losses) as total_losses,
            (CASE WHEN SUM(g.deaths) = 0 THEN SUM(g.kills) ELSE SUM(g.kills) / SUM(g.deaths) END) as kd,
            (CASE WHEN (SUM(g.wins) + SUM(g.losses)) = 0 THEN 0 ELSE (SUM(g.wins) / (SUM(g.wins) + SUM(g.losses))) * 100 END) as win_rate,
            MAX(g.team) as team
        FROM
            players p
        LEFT JOIN
            game_records g ON p.id = g.player_id
        LEFT JOIN
            games gm ON g.game_id = gm.id
        {$where}
        GROUP BY
            p.id, p.nickname, p.player_rank, p.is_banned
        {$order_sql}
        LIMIT {$offset}, {$per_page}";

$players_data = $db->getRows($sql);

// 获取所有玩家的队伍信息，用于计算每个队伍的平均KD值（根据游戏类型筛选）
$team_where = "WHERE gr.team IS NOT NULL AND gr.team != ''";
if (!empty($game_type)) {
    $team_where .= " AND gm.game_type = '{$game_type}'";
}
$team_sql = "SELECT
                gr.team,
                SUM(gr.kills) as team_kills,
                SUM(gr.deaths) as team_deaths
            FROM
                game_records gr
            LEFT JOIN
                games gm ON gr.game_id = gm.id
            {$team_where}
            GROUP BY
                gr.team";
$team_stats = $db->getRows($team_sql);

// 计算每个队伍的平均KD值
$team_kd = [];
foreach ($team_stats as $stat) {
    $team_kd[$stat['team']] = ($stat['team_deaths'] > 0) ? $stat['team_kills'] / $stat['team_deaths'] : $stat['team_kills'];
}

// 为每个玩家计算KPR和DPR
foreach ($players_data as &$player) {
    $total_games = $player['total_wins'] + $player['total_losses'];
    $player['kpr'] = ($total_games > 0) ? $player['total_kills'] / $total_games : 0;
    $player['dpr'] = ($total_games > 0) ? $player['total_deaths'] / $total_games : 0;
}
unset($player);
$players = $players_data;

// 获取汇总统计（根据游戏类型筛选）
$summary_where = '';
if (!empty($game_type)) {
    $summary_where = "WHERE gm.game_type = '{$game_type}'";
}
$sql = "SELECT
            COUNT(DISTINCT p.id) as player_count,
            COUNT(DISTINCT gr.game_id) as game_count,
            SUM(gr.kills) as total_kills,
            SUM(gr.deaths) as total_deaths,
            SUM(gr.wins) as total_wins,
            SUM(gr.losses) as total_losses
        FROM
            players p
        LEFT JOIN
            game_records gr ON p.id = gr.player_id
        LEFT JOIN
            games gm ON gr.game_id = gm.id
        {$summary_where}";

$summary = $db->getRow($sql);

// 获取军衔分布
$rank_stats_all = $stats->getStatsByRank();

// 获取军衔分布数量
$total_rank_records = count($rank_stats_all);
$total_rank_pages = ceil($total_rank_records / $rank_per_page);

// 分页显示军衔数据
$rank_stats = array_slice($rank_stats_all, $rank_offset, $rank_per_page);
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        /* 移除所有滚动条相关样式 */
        body::-webkit-scrollbar,
        div::-webkit-scrollbar,
        .dashboard-container::-webkit-scrollbar,
        .stats-cards::-webkit-scrollbar,
        *::-webkit-scrollbar {
            width: 0 !important;
            height: 0 !important;
            display: none !important;
        }
        
        body, div, .dashboard-container, .stats-cards, * {
            -ms-overflow-style: none !important;  /* IE 和 Edge */
            scrollbar-width: none !important;  /* Firefox */
        }
        
        /* 隐藏右侧滚动控件区域 */
        @media screen and (min-width: 768px) {
            body::after {
                content: '';
                position: fixed;
                top: 0;
                right: 0;
                width: 20px;
                height: 100%;
                background-color: transparent;
                z-index: 9999;
                pointer-events: none;
            }
        }
        
        /* 禁用特定位置的元素 */
        div[style*="position: fixed"][style*="right: 0"] {
            display: none !important;
            visibility: hidden !important;
            opacity: 0 !important;
            pointer-events: none !important;
        }
        
        .dashboard-container {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            padding: 8px;
            padding-right: 25px; /* 增加右侧内边距，防止滚动条出现 */
            max-width: 1200px;
            margin: 0 auto;
            position: relative; /* 添加相对定位 */
            overflow: visible !important; /* 确保不会出现滚动条 */
        }
        
        html, body {
            overflow-x: hidden;
            overflow-y: auto;
        }
        
        .page-title {
            text-align: left;
            margin-bottom: 8px;
            margin-top: 5px;
            color: #2c3e50;
            font-size: 1.3rem;
            font-weight: bold;
        }
        
        .stats-cards {
            display: flex;
            flex-wrap: nowrap;
            justify-content: space-between;
            gap: 5px;
            margin-bottom: 5px;
            overflow: visible !important;
            max-height: 60px;
        }
        
        .stats-card {
            background: #ffffff;
            color: #2c3e50;
            border-radius: 5px;
            padding: 1px 2px;
            box-shadow: 0 1px 5px rgba(0, 0, 0, 0.2);
            transition: transform 0.3s ease;
            width: 15%;
            max-width: 150px;
            height: 55px;
            text-align: center;
            border-left: 2px solid #3498db;
            flex-shrink: 0;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        
        .stats-card:hover {
            transform: translateY(+2px);
        }
        
        .stats-card-title {
            font-size: 0.75rem;
            margin-bottom: 2px;
            color: #7f8c8d;
        }
        
        .stats-card-value {
            font-size: 1.2rem;
            font-weight: bold;
            color: #3498db;
        }
        
        .card {
            background-color: white;
            border-radius: 6px;
            padding: 8px;
            box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
            margin-bottom: 8px;
        }
        
        .card-title {
            font-size: 1.1rem;
            color: #2c3e50;
            margin-bottom: 8px;
            padding-bottom: 5px;
            border-bottom: 1px solid #f1f1f1;
            font-weight: bold;
        }
        
        .table-responsive {
            overflow-x: auto;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 0;
            background: #fff;
        }
        
        .table th, .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #edf2f7;
            font-size: 0.9rem;
            vertical-align: middle;
        }
        
        .table thead th {
            background-color: #f8f9fa;
            color: #2c3e50;
            font-weight: 600;
            border-bottom: 2px solid #e9ecef;
            white-space: nowrap;
        }
        
        .table tbody tr:hover {
            background-color: #f8fafc;
            transition: background-color 0.2s ease;
        }
        
        .table tbody tr td {
            transition: all 0.2s ease;
        }
        
        /* 数字列的样式 */
        .table td:nth-child(3),
        .table td:nth-child(4),
        .table td:nth-child(5),
        .table td:nth-child(6),
        .table td:nth-child(7),
        .table td:nth-child(8) {
            text-align: center;
        }
        
        /* 胜率列样式 */
        .table td:nth-child(8) {
            font-weight: 500;
            color: #2c3e50;
        }
        
        /* 军衔列样式 */
        .table td:nth-child(9) {
            color: #4a5568;
            font-weight: 500;
        }
        
        /* 操作按钮样式 */
        .btn-sm {
            padding: 4px 8px;
            font-size: 0.85rem;
            border-radius: 4px;
            transition: all 0.2s ease;
        }
        
        .btn-danger {
            background-color: #e74c3c;
            border: none;
        }
        
        .btn-danger:hover {
            background-color: #c0392b;
        }
        
        .btn-success {
            background-color: #2ecc71;
            border: none;
        }
        
        .btn-success:hover {
            background-color: #27ae60;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            margin-top: 10px;
            gap: 4px;
        }
        
        .pagination-link {
            display: inline-block;
            padding: 5px 10px;
            background-color: #f1f1f1;
            color: #333;
            text-decoration: none;
            border-radius: 3px;
            transition: background-color 0.3s;
            font-size: 0.8rem;
        }
        
        .pagination-link:hover {
            background-color: #ddd;
        }
        
        .pagination-active {
            background-color: #3498db;
            color: white;
        }
        
        .form-control {
            padding: 6px 10px;
            border: 1px solid #ddd;
            border-radius: 3px;
            width: 100%;
            font-size: 0.85rem;
        }
        
        .btn {
            padding: 6px 12px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            transition: background-color 0.3s;
            font-size: 0.85rem;
        }
        
        .btn-primary {
            background-color: #3498db;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #2980b9;
        }
        
        .btn-secondary {
            background-color: #95a5a6;
            color: white;
        }
        
        .btn-secondary:hover {
            background-color: #7f8c8d;
        }
        
        .text-center {
            text-align: center;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .stats-cards {
                grid-template-columns: 1fr;
            }

            .pagination {
                flex-wrap: wrap;
            }

            /* 只针对玩家数据一览表格的移动端优化 */
            #player-data .table-responsive {
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
            }

            #player-data .table {
                min-width: 900px; /* 确保表格有足够的宽度显示所有列 */
            }

            #player-data .table th,
            #player-data .table td {
                padding: 8px 4px;
                font-size: 0.8rem;
                white-space: nowrap;
            }

            /* 确保玩家数据一览表格的操作列始终可见 */
            #player-data .table th:last-child,
            #player-data .table td:last-child {
                position: sticky;
                right: 0;
                background-color: #fff;
                border-left: 1px solid #edf2f7;
                min-width: 90px;
                text-align: center;
                z-index: 1;
            }

            #player-data .table thead th:last-child {
                background-color: #f8f9fa;
                z-index: 2;
            }

            /* 玩家数据一览表格的操作按钮在移动端的样式 */
            #player-data .btn-sm {
                padding: 3px 6px;
                font-size: 0.75rem;
                min-width: 70px;
                white-space: nowrap;
            }
        }

        /* 更小屏幕的优化 - 只针对玩家数据一览表格 */
        @media (max-width: 480px) {
            #player-data .table {
                min-width: 700px; /* 在更小屏幕上减少最小宽度 */
            }

            #player-data .table th,
            #player-data .table td {
                padding: 6px 3px;
                font-size: 0.75rem;
            }

            /* 隐藏一些不太重要的列以节省空间 - 只针对玩家数据一览表格 */
            #player-data .table th:nth-child(4), /* 总死亡数 */
            #player-data .table td:nth-child(4),
            #player-data .table th:nth-child(7), /* 总败场 */
            #player-data .table td:nth-child(7) {
                display: none;
            }

            /* 操作列保持可见 */
            #player-data .table th:last-child,
            #player-data .table td:last-child {
                min-width: 80px;
            }

            #player-data .btn-sm {
                padding: 2px 4px;
                font-size: 0.7rem;
                min-width: 60px;
            }
        }
        
        /* 添加标签式分页导航样式 */
        .tab-navigation {
            display: flex;
            margin-bottom: 8px;
        }
        
        .tab-item {
            padding: 6px 10px;
            border: 1px solid #e74c3c;
            color: #e74c3c;
            font-weight: bold;
            text-decoration: none;
            margin-right: 6px;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 0.85rem;
        }
        
        .tab-item:hover, .tab-item.active {
            background-color: #e74c3c;
            color: white;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        /* 添加通知框样式 */
        .notification {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            padding: 20px;
            border-radius: 8px;
            background-color: white;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 1000;
            display: none;
            text-align: center;
            min-width: 300px;
        }
        
        .notification.success {
            border-left: 4px solid #28a745;
        }
        
        .notification.error {
            border-left: 4px solid #dc3545;
        }
        
        .notification-backdrop {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 999;
            display: none;
        }
        
        .notification-title {
            font-size: 1.1em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .notification-message {
            margin-bottom: 15px;
        }
        
        .notification-button {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            background-color: #3498db;
            color: white;
            cursor: pointer;
        }
        
        .notification-button:hover {
            background-color: #2980b9;
        }
        
        /* 添加玩家昵称链接样式 */
        .table tbody tr td a {
            color: #007bff;
            text-decoration: none;
        }
        
        .table tbody tr td a:hover {
            color: #0056b3;
            text-decoration: none;
        }
        
        .table thead th a {
            color: #2c3e50;
            text-decoration: none;
        }
        
        .table thead th a:hover {
            color: #3498db;
            text-decoration: none;
        }

        .search-container {
            padding: 20px;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .search-form {
            width: 100%;
        }

        .search-wrapper {
            display: flex;
            align-items: center;
            gap: 15px;
            max-width: 800px;
            margin: 0 auto;
        }

        .search-input {
            flex: 1;
            padding: 12px 20px;
            font-size: 16px;
            border: 2px solid #e1e1e1;
            border-radius: 6px;
            transition: all 0.3s ease;
            outline: none;
        }

        .search-input:focus {
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        .search-buttons {
            display: flex;
            gap: 10px;
        }

        .search-btn {
            padding: 12px 24px;
            font-size: 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .search-submit {
            background-color: #3498db;
            color: white;
        }

        .search-submit:hover {
            background-color: #2980b9;
        }

        .search-reset {
            background-color: #95a5a6;
            color: white;
        }

        .search-reset:hover {
            background-color: #7f8c8d;
        }

        @media (max-width: 768px) {
            .search-wrapper {
                flex-direction: column;
            }
            
            .search-buttons {
                width: 100%;
            }
            
            .search-btn {
                flex: 1;
            }
        }
    </style>
</head>
<body>

<!-- 添加通知组件 -->
<div class="notification-backdrop" id="notificationBackdrop"></div>
<div class="notification" id="notification">
    <div class="notification-title" id="notificationTitle"></div>
    <div class="notification-message" id="notificationMessage"></div>
    <button class="notification-button" onclick="hideNotification()">确定</button>
</div>

<div class="dashboard-container">
    <h1 class="page-title">数据汇总</h1>

    <div class="stats-cards">
        <div class="stats-card">
            <div class="stats-card-title">总玩家数</div>
            <div class="stats-card-value"><?php echo $summary['player_count']; ?></div>
        </div>
        <div class="stats-card">
            <div class="stats-card-title">总游戏场次</div>
            <div class="stats-card-value"><?php echo $summary['game_count']; ?></div>
        </div>
        <div class="stats-card">
            <div class="stats-card-title">总狙杀数</div>
            <div class="stats-card-value"><?php echo $summary['total_kills']; ?></div>
        </div>
        <div class="stats-card">
            <div class="stats-card-title">总死亡数</div>
            <div class="stats-card-value"><?php echo $summary['total_deaths']; ?></div>
        </div>
        <div class="stats-card">
            <div class="stats-card-title">总胜场数</div>
            <div class="stats-card-value"><?php echo $summary['total_wins']; ?></div>
        </div>
        <div class="stats-card">
            <div class="stats-card-title">总败场数</div>
            <div class="stats-card-value"><?php echo $summary['total_losses']; ?></div>
        </div>
    </div>

    <div class="card">
        <div class="search-container">
            <form method="GET" action="inventory.php" class="search-form">
                <div class="search-wrapper">
                    <input type="text" name="search" class="search-input" placeholder="搜索玩家昵称或军衔" value="<?php echo $search; ?>">
                    <input type="hidden" name="active_tab" value="<?php echo $active_tab; ?>">
                    <div class="search-buttons">
                        <button type="submit" class="search-btn search-submit">搜索</button>
                        <?php if (!empty($search)): ?>
                            <a href="inventory.php?active_tab=<?php echo $active_tab; ?>" class="search-btn search-reset">重置</a>
                        <?php endif; ?>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- 添加标签导航 -->
    <div class="tab-navigation">
        <a href="#player-data" class="tab-item <?php echo $active_tab == 'player-data' ? 'active' : ''; ?>" data-tab="player-data">玩家数据一览</a>
        <a href="#player-details" class="tab-item <?php echo $active_tab == 'player-details' ? 'active' : ''; ?>" data-tab="player-details">玩家详细数据</a>
        <a href="#rank-data" class="tab-item <?php echo $active_tab == 'rank-data' ? 'active' : ''; ?>" data-tab="rank-data">军衔分布</a>
        <a href="#game-type-assassination" class="tab-item <?php echo $active_tab == 'game-type-assassination' ? 'active' : ''; ?>" data-tab="game-type-assassination">暗杀模式</a>
        <a href="#game-type-deathmatch" class="tab-item <?php echo $active_tab == 'game-type-deathmatch' ? 'active' : ''; ?>" data-tab="game-type-deathmatch">死斗模式</a>
        <a href="#game-type-boss" class="tab-item <?php echo $active_tab == 'game-type-boss' ? 'active' : ''; ?>" data-tab="game-type-boss">盟主模式</a>
    </div>

    <!-- 玩家数据内容区 -->
    <div id="player-data" class="card tab-content <?php echo $active_tab == 'player-data' ? 'active' : ''; ?>">
        <div class="card-title">玩家数据一览</div>
        <div class="table-responsive">
            <table class="table data-table">
                <thead>
                    <tr>
                        <th>序号</th>
                        <th>
                            <a href="inventory.php?sort=nickname&order=<?php echo ($sort_by == 'nickname' && $order == 'asc') ? 'desc' : 'asc'; ?><?php echo !empty($search) ? '&search='.$search : ''; ?><?php echo isset($_GET['rank_page']) ? '&rank_page='.$rank_page : ''; ?>">
                                玩家昵称
                                <?php if ($sort_by == 'nickname'): ?>
                                    <i class="fa fa-sort-<?php echo $order == 'asc' ? 'up' : 'down'; ?>"></i>
                                <?php endif; ?>
                            </a>
                        </th>
                        <th>
                            <a href="inventory.php?sort=kills&order=<?php echo ($sort_by == 'kills' && $order == 'asc') ? 'desc' : 'asc'; ?><?php echo !empty($search) ? '&search='.$search : ''; ?><?php echo isset($_GET['rank_page']) ? '&rank_page='.$rank_page : ''; ?>">
                                总狙杀数
                                <?php if ($sort_by == 'kills'): ?>
                                    <i class="fa fa-sort-<?php echo $order == 'asc' ? 'up' : 'down'; ?>"></i>
                                <?php endif; ?>
                            </a>
                        </th>
                        <th>
                            <a href="inventory.php?sort=deaths&order=<?php echo ($sort_by == 'deaths' && $order == 'asc') ? 'desc' : 'asc'; ?><?php echo !empty($search) ? '&search='.$search : ''; ?><?php echo isset($_GET['rank_page']) ? '&rank_page='.$rank_page : ''; ?>">
                                总死亡数
                                <?php if ($sort_by == 'deaths'): ?>
                                    <i class="fa fa-sort-<?php echo $order == 'asc' ? 'up' : 'down'; ?>"></i>
                                <?php endif; ?>
                            </a>
                        </th>
                        <th>
                            <a href="inventory.php?sort=kd&order=<?php echo ($sort_by == 'kd' && $order == 'asc') ? 'desc' : 'asc'; ?><?php echo !empty($search) ? '&search='.$search : ''; ?><?php echo isset($_GET['rank_page']) ? '&rank_page='.$rank_page : ''; ?>">
                                KD值
                                <?php if ($sort_by == 'kd'): ?>
                                    <i class="fa fa-sort-<?php echo $order == 'asc' ? 'up' : 'down'; ?>"></i>
                                <?php endif; ?>
                            </a>
                        </th>
                        <th>
                            <a href="inventory.php?sort=wins&order=<?php echo ($sort_by == 'wins' && $order == 'asc') ? 'desc' : 'asc'; ?><?php echo !empty($search) ? '&search='.$search : ''; ?><?php echo isset($_GET['rank_page']) ? '&rank_page='.$rank_page : ''; ?>">
                                总胜场
                                <?php if ($sort_by == 'wins'): ?>
                                    <i class="fa fa-sort-<?php echo $order == 'asc' ? 'up' : 'down'; ?>"></i>
                                <?php endif; ?>
                            </a>
                        </th>
                        <th>
                            <a href="inventory.php?sort=losses&order=<?php echo ($sort_by == 'losses' && $order == 'asc') ? 'desc' : 'asc'; ?><?php echo !empty($search) ? '&search='.$search : ''; ?><?php echo isset($_GET['rank_page']) ? '&rank_page='.$rank_page : ''; ?>">
                                总败场
                                <?php if ($sort_by == 'losses'): ?>
                                    <i class="fa fa-sort-<?php echo $order == 'asc' ? 'up' : 'down'; ?>"></i>
                                <?php endif; ?>
                            </a>
                        </th>
                        <th>胜率</th>
                        <th>
                            <a href="inventory.php?sort=rank&order=<?php echo ($sort_by == 'rank' && $order == 'asc') ? 'desc' : 'asc'; ?><?php echo !empty($search) ? '&search='.$search : ''; ?><?php echo isset($_GET['rank_page']) ? '&rank_page='.$rank_page : ''; ?>">
                                军衔
                                <?php if ($sort_by == 'rank'): ?>
                                    <i class="fa fa-sort-<?php echo $order == 'asc' ? 'up' : 'down'; ?>"></i>
                                <?php endif; ?>
                            </a>
                        </th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($players)): ?>
                        <tr>
                            <td colspan="9" class="text-center">暂无数据</td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($players as $index => $player): ?>
                            <tr>
                                <td><?php echo $offset + $index + 1; ?></td>
                                <td><a href="player-detail.php?nickname=<?php echo urlencode($player['nickname']); ?>"><?php echo $player['nickname']; ?></a></td>
                                <td><?php echo $player['total_kills']; ?></td>
                                <td><?php echo $player['total_deaths']; ?></td>
                                <td><?php echo number_format($player['kd'] ?? 0, 2); ?></td>
                                <td><?php echo $player['total_wins']; ?></td>
                                <td><?php echo $player['total_losses']; ?></td>
                                <td><?php echo number_format($player['win_rate'] ?? 0, 2); ?>%</td>
                                <td><?php echo $player['player_rank']; ?></td>
                                <td>
                                    <?php if ($player['is_banned']): ?>
                                        <button class="btn btn-success btn-sm unban-player" data-player-id="<?php echo $player['player_id']; ?>" data-nickname="<?php echo htmlspecialchars($player['nickname']); ?>">
                                            解除封禁
                                        </button>
                                    <?php else: ?>
                                        <button class="btn btn-danger btn-sm ban-player" data-player-id="<?php echo $player['player_id']; ?>" data-nickname="<?php echo htmlspecialchars($player['nickname']); ?>">
                                            封禁玩家
                                        </button>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
        
        <?php if ($total_pages > 1): ?>
            <div class="text-center mt-4">
                <div style="display: flex; justify-content: center; margin-bottom: 10px;">
                    <a href="inventory.php?page=1&sort=<?php echo $sort_by; ?>&order=<?php echo $order; ?><?php echo !empty($search) ? '&search='.$search : ''; ?><?php echo isset($_GET['rank_page']) ? '&rank_page='.$rank_page : ''; ?>&active_tab=player-data" style="text-decoration: none; color: #6c757d; margin: 0 10px;">首页</a>
                    <a href="inventory.php?page=<?php echo max(1, $page - 1); ?>&sort=<?php echo $sort_by; ?>&order=<?php echo $order; ?><?php echo !empty($search) ? '&search='.$search : ''; ?><?php echo isset($_GET['rank_page']) ? '&rank_page='.$rank_page : ''; ?>&active_tab=player-data" style="text-decoration: none; color: #6c757d; margin: 0 10px;">上一页</a>
                    <a href="inventory.php?page=<?php echo min($total_pages, $page + 1); ?>&sort=<?php echo $sort_by; ?>&order=<?php echo $order; ?><?php echo !empty($search) ? '&search='.$search : ''; ?><?php echo isset($_GET['rank_page']) ? '&rank_page='.$rank_page : ''; ?>&active_tab=player-data" style="text-decoration: none; color: #6c757d; margin: 0 10px;">下一页</a>
                    <a href="inventory.php?page=<?php echo $total_pages; ?>&sort=<?php echo $sort_by; ?>&order=<?php echo $order; ?><?php echo !empty($search) ? '&search='.$search : ''; ?><?php echo isset($_GET['rank_page']) ? '&rank_page='.$rank_page : ''; ?>&active_tab=player-data" style="text-decoration: none; color: #6c757d; margin: 0 10px;">末页</a>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- 玩家详细数据内容区 -->
    <div id="player-details" class="card tab-content <?php echo $active_tab == 'player-details' ? 'active' : ''; ?>">
        <div class="card-title">玩家详细数据</div>

        <!-- 游戏类型选择器 -->
        <div class="mb-3">
            <label for="game-type-select" class="form-label">选择游戏类型：</label>
            <select id="game-type-select" class="form-select" style="width: 200px; display: inline-block;">
                <option value="">全部游戏类型</option>
                <option value="暗杀" <?php echo $game_type == '暗杀' ? 'selected' : ''; ?>>暗杀</option>
                <option value="死斗" <?php echo $game_type == '死斗' ? 'selected' : ''; ?>>死斗</option>
                <option value="盟主" <?php echo $game_type == '盟主' ? 'selected' : ''; ?>>盟主</option>
            </select>
        </div>

        <div class="table-responsive">
            <table class="table data-table">
                <thead>
                    <tr>
                        <th>序号</th>
                        <th>
                            <a href="inventory.php?sort=nickname&order=<?php echo ($sort_by == 'nickname' && $order == 'asc') ? 'desc' : 'asc'; ?><?php echo !empty($search) ? '&search='.$search : ''; ?><?php echo !empty($game_type) ? '&game_type='.$game_type : ''; ?>&active_tab=player-details">
                                玩家昵称
                                <?php if ($sort_by == 'nickname'): ?>
                                    <i class="fa fa-sort-<?php echo $order == 'asc' ? 'up' : 'down'; ?>"></i>
                                <?php endif; ?>
                            </a>
                        </th>
                        <th>Impact 影响力</th>
                        <th>KPR 平均每局狙杀</th>
                        <th>DPR 平均每局死亡</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($players)): ?>
                        <tr>
                            <td colspan="5" class="text-center">暂无数据</td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($players as $index => $player): ?>
                            <?php 
                                // 计算Impact影响力 (KD值/场均KD值)
                                $player_kd = $player['kd'] ?? 0;
                                
                                // 计算场均KD - 如果玩家有队伍信息，使用队伍的KD，否则使用总体平均KD
                                $avg_kd = 0;
                                if (!empty($player['team']) && isset($team_kd[$player['team']])) {
                                    $avg_kd = $team_kd[$player['team']];
                                } else {
                                    $avg_kd = ($summary['total_deaths'] > 0) ? $summary['total_kills'] / $summary['total_deaths'] : 0;
                                }
                                
                                // 计算Impact影响力
                                $impact = ($avg_kd > 0) ? $player_kd / $avg_kd : $player_kd;
                            ?>
                            <tr>
                                <td><?php echo $offset + $index + 1; ?></td>
                                <td><?php echo $player['nickname']; ?></td>
                                <td><?php echo number_format($impact, 2); ?></td>
                                <td><?php echo number_format($player['kpr'] ?? 0, 2); ?></td>
                                <td><?php echo number_format($player['dpr'] ?? 0, 2); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
        
        <?php if ($total_pages > 1): ?>
            <div class="text-center mt-4">
                <div style="display: flex; justify-content: center; margin-bottom: 10px;">
                    <a href="inventory.php?page=1&sort=<?php echo $sort_by; ?>&order=<?php echo $order; ?><?php echo !empty($search) ? '&search='.$search : ''; ?><?php echo !empty($game_type) ? '&game_type='.$game_type : ''; ?>&active_tab=player-details" style="text-decoration: none; color: #6c757d; margin: 0 10px;">首页</a>
                    <a href="inventory.php?page=<?php echo max(1, $page - 1); ?>&sort=<?php echo $sort_by; ?>&order=<?php echo $order; ?><?php echo !empty($search) ? '&search='.$search : ''; ?><?php echo !empty($game_type) ? '&game_type='.$game_type : ''; ?>&active_tab=player-details" style="text-decoration: none; color: #6c757d; margin: 0 10px;">上一页</a>
                    <a href="inventory.php?page=<?php echo min($total_pages, $page + 1); ?>&sort=<?php echo $sort_by; ?>&order=<?php echo $order; ?><?php echo !empty($search) ? '&search='.$search : ''; ?><?php echo !empty($game_type) ? '&game_type='.$game_type : ''; ?>&active_tab=player-details" style="text-decoration: none; color: #6c757d; margin: 0 10px;">下一页</a>
                    <a href="inventory.php?page=<?php echo $total_pages; ?>&sort=<?php echo $sort_by; ?>&order=<?php echo $order; ?><?php echo !empty($search) ? '&search='.$search : ''; ?><?php echo !empty($game_type) ? '&game_type='.$game_type : ''; ?>&active_tab=player-details" style="text-decoration: none; color: #6c757d; margin: 0 10px;">末页</a>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- 军衔分布内容区 -->
    <div id="rank-data" class="card tab-content <?php echo $active_tab == 'rank-data' ? 'active' : ''; ?>">
        <div class="card-title">军衔分布</div>
        <div class="table-responsive">
            <table class="table">
                <thead>
                    <tr>
                        <th>军衔</th>
                        <th>玩家数量</th>
                        <th>总狙杀数</th>
                        <th>总死亡数</th>
                        <th>平均KD值</th>
                        <th>总胜场</th>
                        <th>总败场</th>
                        <th>胜率</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($rank_stats)): ?>
                        <tr>
                            <td colspan="8" class="text-center">暂无数据</td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($rank_stats as $index => $rank): ?>
                            <tr>
                                <td><?php echo !empty($rank['player_rank']) ? $rank['player_rank'] : '未知'; ?></td>
                                <td><?php echo $rank['player_count']; ?></td>
                                <td><?php echo $rank['total_kills']; ?></td>
                                <td><?php echo $rank['total_deaths']; ?></td>
                                <td><?php echo number_format($rank['kd'] ?? 0, 2); ?></td>
                                <td><?php echo $rank['total_wins']; ?></td>
                                <td><?php echo $rank['total_losses']; ?></td>
                                <td><?php echo $rank['win_rate'] ?? '0%'; ?></td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
        
        <?php if ($total_rank_pages > 1): ?>
            <div class="text-center mt-4">
                <div style="display: flex; justify-content: center; margin-bottom: 10px;">
                    <a href="inventory.php?rank_page=1&page=<?php echo $page; ?>&sort=<?php echo $sort_by; ?>&order=<?php echo $order; ?><?php echo !empty($search) ? '&search='.$search : ''; ?>&active_tab=rank-data" style="text-decoration: none; color: #6c757d; margin: 0 10px;">首页</a>
                    <a href="inventory.php?rank_page=<?php echo max(1, $rank_page - 1); ?>&page=<?php echo $page; ?>&sort=<?php echo $sort_by; ?>&order=<?php echo $order; ?><?php echo !empty($search) ? '&search='.$search : ''; ?>&active_tab=rank-data" style="text-decoration: none; color: #6c757d; margin: 0 10px;">上一页</a>
                    <a href="inventory.php?rank_page=<?php echo min($total_rank_pages, $rank_page + 1); ?>&page=<?php echo $page; ?>&sort=<?php echo $sort_by; ?>&order=<?php echo $order; ?><?php echo !empty($search) ? '&search='.$search : ''; ?>&active_tab=rank-data" style="text-decoration: none; color: #6c757d; margin: 0 10px;">下一页</a>
                    <a href="inventory.php?rank_page=<?php echo $total_rank_pages; ?>&page=<?php echo $page; ?>&sort=<?php echo $sort_by; ?>&order=<?php echo $order; ?><?php echo !empty($search) ? '&search='.$search : ''; ?>&active_tab=rank-data" style="text-decoration: none; color: #6c757d; margin: 0 10px;">末页</a>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- 暗杀模式内容区 -->
    <div id="game-type-assassination" class="card tab-content <?php echo $active_tab == 'game-type-assassination' ? 'active' : ''; ?>">
        <div class="card-title">暗杀模式数据</div>
        <div class="table-responsive">
            <table class="table data-table">
                <thead>
                    <tr>
                        <th>序号</th>
                        <th>
                            <a href="inventory.php?sort=nickname&order=<?php echo ($sort_by == 'nickname' && $order == 'asc') ? 'desc' : 'asc'; ?><?php echo !empty($search) ? '&search='.$search : ''; ?>&active_tab=game-type-assassination">
                                玩家昵称
                                <?php if ($sort_by == 'nickname'): ?>
                                    <i class="fa fa-sort-<?php echo $order == 'asc' ? 'up' : 'down'; ?>"></i>
                                <?php endif; ?>
                            </a>
                        </th>
                        <th>总狙杀数</th>
                        <th>总死亡数</th>
                        <th>KD值</th>
                        <th>总胜场</th>
                        <th>总败场</th>
                        <th>胜率</th>
                        <th>军衔</th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    // 获取暗杀模式的玩家数据
                    $assassination_where = '';
                    if (!empty($search)) {
                        $assassination_where = "AND (p.nickname LIKE '%{$search}%' OR p.player_rank LIKE '%{$search}%')";
                    }
                    
                    $assassination_sql = "SELECT 
                        p.nickname,
                        p.player_rank,
                        SUM(gr.kills) as total_kills,
                        SUM(gr.deaths) as total_deaths,
                        SUM(gr.wins) as total_wins,
                        SUM(gr.losses) as total_losses,
                        (CASE WHEN SUM(gr.deaths) = 0 THEN SUM(gr.kills) ELSE SUM(gr.kills) / SUM(gr.deaths) END) as kd,
                        (CASE WHEN (SUM(gr.wins) + SUM(gr.losses)) = 0 THEN 0 ELSE (SUM(gr.wins) / (SUM(gr.wins) + SUM(gr.losses))) * 100 END) as win_rate
                    FROM 
                        players p
                    LEFT JOIN 
                        game_records gr ON p.id = gr.player_id
                    LEFT JOIN
                        games gm ON gr.game_id = gm.id
                    WHERE 
                        gm.game_type = '暗杀' {$assassination_where}
                    GROUP BY 
                        p.id, p.nickname, p.player_rank
                    {$order_sql}
                    LIMIT {$offset}, {$per_page}";
                    
                    $assassination_players = $db->getRows($assassination_sql);
                    
                    if (empty($assassination_players)): ?>
                        <tr>
                            <td colspan="9" class="text-center">暂无数据</td>
                        </tr>
                    <?php else:
                        foreach ($assassination_players as $index => $player): ?>
                            <tr>
                                <td><?php echo $offset + $index + 1; ?></td>
                                <td><?php echo $player['nickname']; ?></td>
                                <td><?php echo $player['total_kills']; ?></td>
                                <td><?php echo $player['total_deaths']; ?></td>
                                <td><?php echo number_format($player['kd'], 2); ?></td>
                                <td><?php echo $player['total_wins']; ?></td>
                                <td><?php echo $player['total_losses']; ?></td>
                                <td><?php echo number_format($player['win_rate'], 2); ?>%</td>
                                <td><?php echo $player['player_rank']; ?></td>
                            </tr>
                        <?php endforeach;
                    endif; ?>
                </tbody>
            </table>
        </div>
        
        <?php if ($total_pages > 1): ?>
            <div class="text-center mt-4">
                <div style="display: flex; justify-content: center; margin-bottom: 10px;">
                    <a href="inventory.php?page=1&sort=<?php echo $sort_by; ?>&order=<?php echo $order; ?><?php echo !empty($search) ? '&search='.$search : ''; ?>&active_tab=game-type-assassination" style="text-decoration: none; color: #6c757d; margin: 0 10px;">首页</a>
                    <a href="inventory.php?page=<?php echo max(1, $page - 1); ?>&sort=<?php echo $sort_by; ?>&order=<?php echo $order; ?><?php echo !empty($search) ? '&search='.$search : ''; ?>&active_tab=game-type-assassination" style="text-decoration: none; color: #6c757d; margin: 0 10px;">上一页</a>
                    <a href="inventory.php?page=<?php echo min($total_pages, $page + 1); ?>&sort=<?php echo $sort_by; ?>&order=<?php echo $order; ?><?php echo !empty($search) ? '&search='.$search : ''; ?>&active_tab=game-type-assassination" style="text-decoration: none; color: #6c757d; margin: 0 10px;">下一页</a>
                    <a href="inventory.php?page=<?php echo $total_pages; ?>&sort=<?php echo $sort_by; ?>&order=<?php echo $order; ?><?php echo !empty($search) ? '&search='.$search : ''; ?>&active_tab=game-type-assassination" style="text-decoration: none; color: #6c757d; margin: 0 10px;">末页</a>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- 死斗模式内容区 -->
    <div id="game-type-deathmatch" class="card tab-content <?php echo $active_tab == 'game-type-deathmatch' ? 'active' : ''; ?>">
        <div class="card-title">死斗模式数据</div>
        <div class="table-responsive">
            <table class="table data-table">
                <thead>
                    <tr>
                        <th>序号</th>
                        <th>
                            <a href="inventory.php?sort=nickname&order=<?php echo ($sort_by == 'nickname' && $order == 'asc') ? 'desc' : 'asc'; ?><?php echo !empty($search) ? '&search='.$search : ''; ?>&active_tab=game-type-deathmatch">
                                玩家昵称
                                <?php if ($sort_by == 'nickname'): ?>
                                    <i class="fa fa-sort-<?php echo $order == 'asc' ? 'up' : 'down'; ?>"></i>
                                <?php endif; ?>
                            </a>
                        </th>
                        <th>总狙杀数</th>
                        <th>总死亡数</th>
                        <th>KD值</th>
                        <th>总胜场</th>
                        <th>总败场</th>
                        <th>胜率</th>
                        <th>军衔</th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    // 获取死斗模式的玩家数据
                    $deathmatch_where = '';
                    if (!empty($search)) {
                        $deathmatch_where = "AND (p.nickname LIKE '%{$search}%' OR p.player_rank LIKE '%{$search}%')";
                    }
                    
                    $deathmatch_sql = "SELECT 
                        p.nickname,
                        p.player_rank,
                        SUM(gr.kills) as total_kills,
                        SUM(gr.deaths) as total_deaths,
                        SUM(gr.wins) as total_wins,
                        SUM(gr.losses) as total_losses,
                        (CASE WHEN SUM(gr.deaths) = 0 THEN SUM(gr.kills) ELSE SUM(gr.kills) / SUM(gr.deaths) END) as kd,
                        (CASE WHEN (SUM(gr.wins) + SUM(gr.losses)) = 0 THEN 0 ELSE (SUM(gr.wins) / (SUM(gr.wins) + SUM(gr.losses))) * 100 END) as win_rate
                    FROM 
                        players p
                    LEFT JOIN 
                        game_records gr ON p.id = gr.player_id
                    LEFT JOIN
                        games gm ON gr.game_id = gm.id
                    WHERE 
                        gm.game_type = '死斗' {$deathmatch_where}
                    GROUP BY 
                        p.id, p.nickname, p.player_rank
                    {$order_sql}
                    LIMIT {$offset}, {$per_page}";
                    
                    $deathmatch_players = $db->getRows($deathmatch_sql);
                    
                    if (empty($deathmatch_players)): ?>
                        <tr>
                            <td colspan="9" class="text-center">暂无数据</td>
                        </tr>
                    <?php else:
                        foreach ($deathmatch_players as $index => $player): ?>
                            <tr>
                                <td><?php echo $offset + $index + 1; ?></td>
                                <td><?php echo $player['nickname']; ?></td>
                                <td><?php echo $player['total_kills']; ?></td>
                                <td><?php echo $player['total_deaths']; ?></td>
                                <td><?php echo number_format($player['kd'], 2); ?></td>
                                <td><?php echo $player['total_wins']; ?></td>
                                <td><?php echo $player['total_losses']; ?></td>
                                <td><?php echo number_format($player['win_rate'], 2); ?>%</td>
                                <td><?php echo $player['player_rank']; ?></td>
                            </tr>
                        <?php endforeach;
                    endif; ?>
                </tbody>
            </table>
        </div>
        
        <?php if ($total_pages > 1): ?>
            <div class="text-center mt-4">
                <div style="display: flex; justify-content: center; margin-bottom: 10px;">
                    <a href="inventory.php?page=1&sort=<?php echo $sort_by; ?>&order=<?php echo $order; ?><?php echo !empty($search) ? '&search='.$search : ''; ?>&active_tab=game-type-deathmatch" style="text-decoration: none; color: #6c757d; margin: 0 10px;">首页</a>
                    <a href="inventory.php?page=<?php echo max(1, $page - 1); ?>&sort=<?php echo $sort_by; ?>&order=<?php echo $order; ?><?php echo !empty($search) ? '&search='.$search : ''; ?>&active_tab=game-type-deathmatch" style="text-decoration: none; color: #6c757d; margin: 0 10px;">上一页</a>
                    <a href="inventory.php?page=<?php echo min($total_pages, $page + 1); ?>&sort=<?php echo $sort_by; ?>&order=<?php echo $order; ?><?php echo !empty($search) ? '&search='.$search : ''; ?>&active_tab=game-type-deathmatch" style="text-decoration: none; color: #6c757d; margin: 0 10px;">下一页</a>
                    <a href="inventory.php?page=<?php echo $total_pages; ?>&sort=<?php echo $sort_by; ?>&order=<?php echo $order; ?><?php echo !empty($search) ? '&search='.$search : ''; ?>&active_tab=game-type-deathmatch" style="text-decoration: none; color: #6c757d; margin: 0 10px;">末页</a>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- 盟主模式内容区 -->
    <div id="game-type-boss" class="card tab-content <?php echo $active_tab == 'game-type-boss' ? 'active' : ''; ?>">
        <div class="card-title">盟主模式数据</div>
        <div class="table-responsive">
            <table class="table data-table">
                <thead>
                    <tr>
                        <th>序号</th>
                        <th>
                            <a href="inventory.php?sort=nickname&order=<?php echo ($sort_by == 'nickname' && $order == 'asc') ? 'desc' : 'asc'; ?><?php echo !empty($search) ? '&search='.$search : ''; ?>&active_tab=game-type-boss">
                                玩家昵称
                                <?php if ($sort_by == 'nickname'): ?>
                                    <i class="fa fa-sort-<?php echo $order == 'asc' ? 'up' : 'down'; ?>"></i>
                                <?php endif; ?>
                            </a>
                        </th>
                        <th>总狙杀数</th>
                        <th>总死亡数</th>
                        <th>KD值</th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    // 获取盟主模式的玩家数据
                    $boss_where = '';
                    if (!empty($search)) {
                        $boss_where = "AND (p.nickname LIKE '%{$search}%' OR p.player_rank LIKE '%{$search}%')";
                    }
                    
                    $boss_sql = "SELECT 
                        p.nickname,
                        SUM(gr.kills) as total_kills,
                        SUM(gr.deaths) as total_deaths,
                        (CASE WHEN SUM(gr.deaths) = 0 THEN SUM(gr.kills) ELSE SUM(gr.kills) / SUM(gr.deaths) END) as kd
                    FROM 
                        players p
                    LEFT JOIN 
                        game_records gr ON p.id = gr.player_id
                    LEFT JOIN
                        games gm ON gr.game_id = gm.id
                    WHERE 
                        gm.game_type = '盟主' {$boss_where}
                    GROUP BY 
                        p.id, p.nickname
                    {$order_sql}
                    LIMIT {$offset}, {$per_page}";
                    
                    $boss_players = $db->getRows($boss_sql);
                    
                    if (empty($boss_players)): ?>
                        <tr>
                            <td colspan="5" class="text-center">暂无数据</td>
                        </tr>
                    <?php else:
                        foreach ($boss_players as $index => $player): ?>
                            <tr>
                                <td><?php echo $offset + $index + 1; ?></td>
                                <td><?php echo $player['nickname']; ?></td>
                                <td><?php echo $player['total_kills']; ?></td>
                                <td><?php echo $player['total_deaths']; ?></td>
                                <td><?php echo number_format($player['kd'], 2); ?></td>
                            </tr>
                        <?php endforeach;
                    endif; ?>
                </tbody>
            </table>
        </div>
        
        <?php if ($total_pages > 1): ?>
            <div class="text-center mt-4">
                <div style="display: flex; justify-content: center; margin-bottom: 10px;">
                    <a href="inventory.php?page=1&sort=<?php echo $sort_by; ?>&order=<?php echo $order; ?><?php echo !empty($search) ? '&search='.$search : ''; ?>&active_tab=game-type-boss" style="text-decoration: none; color: #6c757d; margin: 0 10px;">首页</a>
                    <a href="inventory.php?page=<?php echo max(1, $page - 1); ?>&sort=<?php echo $sort_by; ?>&order=<?php echo $order; ?><?php echo !empty($search) ? '&search='.$search : ''; ?>&active_tab=game-type-boss" style="text-decoration: none; color: #6c757d; margin: 0 10px;">上一页</a>
                    <a href="inventory.php?page=<?php echo min($total_pages, $page + 1); ?>&sort=<?php echo $sort_by; ?>&order=<?php echo $order; ?><?php echo !empty($search) ? '&search='.$search : ''; ?>&active_tab=game-type-boss" style="text-decoration: none; color: #6c757d; margin: 0 10px;">下一页</a>
                    <a href="inventory.php?page=<?php echo $total_pages; ?>&sort=<?php echo $sort_by; ?>&order=<?php echo $order; ?><?php echo !empty($search) ? '&search='.$search : ''; ?>&active_tab=game-type-boss" style="text-decoration: none; color: #6c757d; margin: 0 10px;">末页</a>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <div class="card">
        <div class="card-title">数据导出</div>
        <p>如需导出数据，请<a href="export_data.php">点击这里</a>前往数据导出页面</p>
    </div>
</div>

<!-- 添加JavaScript代码用于标签切换和移除滚动控件 -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 获取所有标签和内容区
    const tabItems = document.querySelectorAll('.tab-item');
    const tabContents = document.querySelectorAll('.tab-content');
    
    // 为每个标签添加点击事件
    tabItems.forEach(tab => {
        tab.addEventListener('click', function(e) {
            e.preventDefault();
            
            // 移除所有标签和内容区的active类
            tabItems.forEach(item => item.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));
            
            // 为当前点击的标签添加active类
            this.classList.add('active');
            
            // 获取对应的内容区ID并显示
            const targetId = this.getAttribute('href').substring(1);
            document.getElementById(targetId).classList.add('active');
            
            // 更新URL参数，但不刷新页面
            const url = new URL(window.location.href);
            url.searchParams.set('active_tab', this.getAttribute('data-tab'));
            
            // 保持搜索参数
            const searchValue = document.querySelector('.search-input').value;
            if (searchValue) {
                url.searchParams.set('search', searchValue);
            } else {
                url.searchParams.delete('search');
            }
            
            window.history.pushState({}, '', url);
            
            // 更新搜索表单中的hidden input
            const activeTabInput = document.querySelector('input[name="active_tab"]');
            if (activeTabInput) {
                activeTabInput.value = this.getAttribute('data-tab');
            }
        });
    });
    
    // 尝试查找并移除滚动控件
    function removeScrollControls() {
        // 查找所有可能的滚动控件
        const scrollControls = document.querySelectorAll('div[style*="position: fixed"][style*="right"][style*="overflow"]');
        scrollControls.forEach(control => {
            control.parentNode.removeChild(control);
        });
        
        // 查找右侧的所有固定定位元素
        const rightFixedElements = document.querySelectorAll('div[style*="position: fixed"][style*="right"]');
        rightFixedElements.forEach(element => {
            if (element.offsetWidth < 50 && element.offsetHeight < 150) {
                element.parentNode.removeChild(element);
            }
        });
    }
    
    // 页面加载后移除滚动控件
    removeScrollControls();
    
    // 监听DOM变化，移除动态添加的滚动控件
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.addedNodes.length) {
                removeScrollControls();
            }
        });
    });
    
    observer.observe(document.body, { childList: true, subtree: true });

    // 添加通知函数
    function showNotification(title, message, type = 'success', showConfirm = false) {
        const notification = document.getElementById('notification');
        const backdrop = document.getElementById('notificationBackdrop');
        const titleElement = document.getElementById('notificationTitle');
        const messageElement = document.getElementById('notificationMessage');
        const button = notification.querySelector('.notification-button');
        
        titleElement.textContent = title;
        messageElement.textContent = message;
        
        notification.className = 'notification ' + type;
        
        // 如果是确认对话框，添加确认和取消按钮
        if (showConfirm) {
            button.style.display = 'none';
            if (!notification.querySelector('.confirm-buttons')) {
                const confirmButtons = document.createElement('div');
                confirmButtons.className = 'confirm-buttons';
                confirmButtons.style.display = 'flex';
                confirmButtons.style.justifyContent = 'center';
                confirmButtons.style.gap = '10px';
                confirmButtons.innerHTML = `
                    <button class="notification-button confirm-yes" style="background-color: #28a745;">确定</button>
                    <button class="notification-button confirm-no" style="background-color: #dc3545;">取消</button>
                `;
                notification.appendChild(confirmButtons);
            } else {
                notification.querySelector('.confirm-buttons').style.display = 'flex';
            }
        } else {
            button.style.display = 'block';
            const confirmButtons = notification.querySelector('.confirm-buttons');
            if (confirmButtons) {
                confirmButtons.style.display = 'none';
            }
        }
        
        notification.style.display = 'block';
        backdrop.style.display = 'block';
        
        return new Promise((resolve) => {
            if (showConfirm) {
                const confirmButtons = notification.querySelector('.confirm-buttons');
                const yesButton = confirmButtons.querySelector('.confirm-yes');
                const noButton = confirmButtons.querySelector('.confirm-no');
                
                yesButton.onclick = () => {
                    hideNotification();
                    resolve(true);
                };
                
                noButton.onclick = () => {
                    hideNotification();
                    resolve(false);
                };
            } else {
                // 如果不是确认对话框，点击确定按钮后执行回调
                button.onclick = () => {
                    hideNotification();
                    resolve(true);
                };
            }
        });
    }
    
    window.hideNotification = function() {
        const notification = document.getElementById('notification');
        const backdrop = document.getElementById('notificationBackdrop');
        
        notification.style.display = 'none';
        backdrop.style.display = 'none';
        
        // 如果需要刷新页面，在这里执行
        if (notification.dataset.shouldReload === 'true') {
            location.reload();
        }
    }

    // 封禁玩家
    document.querySelectorAll('.ban-player').forEach(button => {
        button.addEventListener('click', async function() {
            const playerId = this.dataset.playerId;
            const nickname = this.dataset.nickname;

            // 自定义选择封禁类型弹窗
            const notification = document.getElementById('notification');
            const backdrop = document.getElementById('notificationBackdrop');
            const titleElement = document.getElementById('notificationTitle');
            const messageElement = document.getElementById('notificationMessage');
            const buttonElement = notification.querySelector('.notification-button');

            titleElement.textContent = '请选择封禁类型';
            messageElement.innerHTML = `对玩家 <b>${nickname}</b> 进行封禁，请选择封禁原因：`;
            notification.className = 'notification error';
            buttonElement.style.display = 'none';

            // 动态生成两个按钮
            let typeButtons = notification.querySelector('.ban-type-buttons');
            if (!typeButtons) {
                typeButtons = document.createElement('div');
                typeButtons.className = 'ban-type-buttons';
                typeButtons.style.display = 'flex';
                typeButtons.style.justifyContent = 'center';
                typeButtons.style.gap = '10px';
                typeButtons.innerHTML = `
                    <button class="notification-button cheat-ban" style="background-color: #c0392b;">作弊封禁</button>
                    <button class="notification-button violation-ban" style="background-color: #e67e22;">违规封禁</button>
                    <button class="notification-button cancel-ban" style="background-color: #95a5a6;">取消</button>
                `;
                notification.appendChild(typeButtons);
            } else {
                typeButtons.style.display = 'flex';
            }

            notification.style.display = 'block';
            backdrop.style.display = 'block';

            // 事件绑定
            const cheatBtn = typeButtons.querySelector('.cheat-ban');
            const violationBtn = typeButtons.querySelector('.violation-ban');
            const cancelBtn = typeButtons.querySelector('.cancel-ban');

            // 解绑旧事件
            cheatBtn.onclick = null;
            violationBtn.onclick = null;
            cancelBtn.onclick = null;

            cheatBtn.onclick = async () => {
                notification.style.display = 'none';
                backdrop.style.display = 'none';
                await banPlayerWithType(playerId, 'cheat', nickname);
            };
            violationBtn.onclick = async () => {
                notification.style.display = 'none';
                backdrop.style.display = 'none';
                await banPlayerWithType(playerId, 'violation', nickname);
            };
            cancelBtn.onclick = () => {
                notification.style.display = 'none';
                backdrop.style.display = 'none';
            };
        });
    });

    async function banPlayerWithType(playerId, banType, nickname) {
        try {
            const response = await fetch('api/ban_player.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    player_id: playerId,
                    action: 'ban',
                    ban_type: banType
                })
            });
            const data = await response.json();
            if (data.status === 'success') {
                location.reload();
            } else {
                await showNotification('操作失败', data.message || '封禁失败，请稍后重试', 'error');
            }
        } catch (error) {
            await showNotification('操作失败', error.message || '发生未知错误', 'error');
        }
    }

    // 解除封禁
    document.querySelectorAll('.unban-player').forEach(button => {
        button.addEventListener('click', async function() {
            const playerId = this.dataset.playerId;
            const nickname = this.dataset.nickname;
            
            // 使用通知组件显示确认对话框
            const confirmed = await showNotification(
                '确认解除封禁', 
                `确定要解除玩家 ${nickname} 的封禁吗？`,
                'success',
                true
            );
            
            if (confirmed) {
                try {
                    const response = await fetch('api/ban_player.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        player_id: playerId,
                        action: 'unban'
                    })
                    });
                    
                    const data = await response.json();
                    
                    if (data.status === 'success') {
                        location.reload(); // 直接刷新页面，不显示成功提示
                    } else {
                        await showNotification('操作失败', data.message || '解除封禁失败，请稍后重试', 'error');
                    }
                } catch (error) {
                    await showNotification('操作失败', error.message || '发生未知错误', 'error');
                }
            }
        });
    });
});

// 游戏类型选择器事件处理
document.addEventListener('DOMContentLoaded', function() {
    const gameTypeSelect = document.getElementById('game-type-select');
    if (gameTypeSelect) {
        gameTypeSelect.addEventListener('change', function() {
            const selectedType = this.value;
            const currentUrl = new URL(window.location.href);

            // 更新URL参数
            if (selectedType) {
                currentUrl.searchParams.set('game_type', selectedType);
            } else {
                currentUrl.searchParams.delete('game_type');
            }

            // 重置页码到第一页
            currentUrl.searchParams.set('page', '1');

            // 保持当前的active_tab
            currentUrl.searchParams.set('active_tab', 'player-details');

            // 跳转到新URL
            window.location.href = currentUrl.toString();
        });
    }
});
</script>

<?php
// 包含底部
include 'includes/footer.php';
?>
