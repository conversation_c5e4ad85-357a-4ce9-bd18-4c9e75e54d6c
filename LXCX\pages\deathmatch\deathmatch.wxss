/* 玩家列表 */
.players-list {
  width: 100%;
  padding-bottom: 30rpx; /* 添加底部padding，防止内容被tabBar遮挡 */
}

.players-list .title {
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 20rpx;
  color: #fff;
  text-shadow: 2rpx 2rpx 4rpx rgba(0, 0, 0, 0.5);
}

.loading, .empty {
  text-align: center;
  padding: 40rpx 0;
  color: #999;
  font-size: 28rpx;
}

/* 错误提示样式 */
.error-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}

.error-msg {
  color: #ff4d4f;
  font-size: 28rpx;
  margin: 20rpx 0 30rpx;
  text-align: center;
}

.retry-btn {
  background-color: hsl(0, 17%, 49%);
  color: rgba(255, 255, 255, 0.5);
  font-size: 28rpx;
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
  margin-top: 20rpx;
}

.player-card {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: rgba(255, 255, 255, 0.85);
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(139, 69, 19, 0.2);
  border: 2rpx solid #8B4513;
}

.player-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.player-info {
  flex: 1;
}

.player-name {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 8rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
}

/* VIP标签样式 */
.vip-tag {
  color: #DAA520; /* 金黄色文字 */
  background-color: #FFFACD; /* 浅黄色背景 */
  padding: 2rpx 10rpx;
  margin-left:10rpx;
  border-radius: 6rpx;
  font-size: 26rpx;
  display: inline-block;
}

.player-stats {
  display: flex;
  gap: 20rpx;
  margin-bottom: 8rpx;
}

.player-stats text {
  font-size: 24rpx;
  color: #4A90E2;
  font-weight: 500;
}

.player-summary {
  font-size: 24rpx;
  color: #999;
}

.arrow image {
  width: 32rpx;
  height: 32rpx;
}

.container {
  min-height: 100vh;
  background-image: url('https://img1.lxbl.online/102.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  padding: 30rpx 20rpx;
  position: relative;
}

/* 添加一个半透明遮罩层 */
.container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1;
}

/* 确保内容在遮罩层之上 */
.search-box, .history-box, .players-list {
  position: relative;
  z-index: 2;
}

/* 修改搜索框样式适应武侠主题 */
.search-box {
    display: flex;
    align-items: center;
    background-color: #E4D5B7; /* 浅一点的宣纸色 */
    padding: 12rpx 15rpx;
    margin: 0 20rpx;
    border-radius: 40rpx; /* 圆角卷轴感 */
    border: 1px solid #C8B6A6; /* 边框 */
   
    box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);
}

.search-box::before {
  content: '';
  position: absolute;
  top: -2rpx;
  left: 0;
  right: 0;
  height: 2rpx;
  background: linear-gradient(to right, transparent, #8B4513, transparent);
}

.search-box::after {
  content: '';
  position: absolute;
  bottom: -2rpx;
  left: 0;
  right: 0;
  height: 2rpx;
  background: linear-gradient(to right, transparent, #8B4513, transparent);
}

.search-input {
  flex: 1;
  height: 50rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  background: transparent;
}

/* 排序选项样式 */
.sort-options {
  margin: 10rpx 0;
  position: relative;
  z-index: 2;
}

.sort-buttons {
  display: flex;
  justify-content: space-between;
  padding: 0 20rpx;
}

.sort-item {
  background-color: rgba(255, 255, 255, 0.7);
  padding: 0 20rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  color: #666;
  text-align: center;
}

.sort-item.active {
  background-color: #8B4513;
  color: #fff;
}

/* 玩家卡片古风样式 */
.player-card {
  background: rgba(255, 248, 240, 0.95);
  border: none;
  margin: 30rpx 20rpx;
  padding: 30rpx;
  position: relative;
  box-shadow: 0 4rpx 20rpx rgba(139, 69, 19, 0.15);
  border-radius: 25rpx; /* 圆角卷轴感 */
}

/* 搜索历史 */
.history-box {
  margin-bottom: 30rpx;
  position: relative;
  z-index: 2;
}

.history-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
  font-size: 28rpx;
  color: #666;
  padding: 0 20rpx;
}

.clear-history {
  color: #4A90E2;
  font-size: 24rpx;
}

.history-tags {
  display: flex;
  flex-wrap: wrap;
  padding: 0 20rpx;
}

.history-tag {
  background-color: rgba(255, 255, 255, 0.8);
  color: #666;
  padding: 10rpx 20rpx;
  margin-right: 15rpx;
  margin-bottom: 15rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  border: 1rpx solid #8B4513;
}

/* 底部导航栏占位 */
.tabbar-placeholder {
  height: 110rpx;
  width: 100%;
  margin-bottom: env(safe-area-inset-bottom);
}

/* 修改历史记录样式 */
.history-title text {
  color: #fff;
  text-shadow: 1rpx 1rpx 2rpx rgba(0, 0, 0, 0.5);
}

/* 玩家名称古风样式 */
.player-name {
  font-family: "楷体", "STKaiti";
  color: #4A321F;
  font-size: 34rpx;
  letter-spacing: 2rpx;
}

/* VIP标签古风样式 */
.vip-tag {
  background: linear-gradient(45deg, #DAA520, #FFD700);
  color: #8B4513;
  border: 1rpx solid #8B4513;
  padding: 4rpx 16rpx;
  font-family: "楷体", "STKaiti";
  text-shadow: 0 0 2rpx rgba(139, 69, 19, 0.3);
  border-radius: 35rpx; /* 圆角卷轴感 */
}

/* 历史记录标题古风样式 */
.history-title {
  font-family: "楷体", "STKaiti";
  margin: 30rpx 20rpx;
}

.history-title text {
  color: #F5DEB3;
  font-size: 32rpx;
  letter-spacing: 4rpx;
}

/* 历史标签古风样式 */
.history-tag {
  background: rgba(245, 222, 179, 0.7);
  border: 1rpx solid #8B4513;
  color: #4A321F;
  padding: 8rpx 24rpx;
  margin: 10rpx;
  font-family: "楷体", "STKaiti";
  position: relative;
  transition: all 0.3s;
}

.history-tag:active {
  background: rgba(139, 69, 19, 0.2);
}

/* 玩家统计数据古风样式 */
.player-stats text {
  color: #8B4513;
  font-family: "楷体", "STKaiti";
  border-bottom: 1rpx solid rgba(139, 69, 19, 0.3);
  padding-bottom: 4rpx;
}

/* 列表标题古风样式 */
.players-list .title {
  font-family: "楷体", "STKaiti";
  font-size: 36rpx;
  color: #F5DEB3;
  text-align: center;
  letter-spacing: 8rpx;
  margin: 40rpx 0;
  text-shadow: 2rpx 2rpx 4rpx rgba(0, 0, 0, 0.5);
  position: relative;
}

.players-list .title::before,
.players-list .title::after {
  content: '※';
  color: #DAA520;
  margin: 0 20rpx;
}

/* 清空历史按钮古风样式 */
.clear-history {
  color: #DAA520;
  font-family: "楷体", "STKaiti";
  border: 1rpx solid #DAA520;
  padding: 4rpx 16rpx;
  border-radius: 0;
  background: rgba(218, 165, 32, 0.1);
}

/* 排序选项样式 */
.sort-options {
  padding: 5rpx;
  margin: 10rpx;
  position: relative;
  z-index: 2;
}

.sort-title {
  font-size: 28rpx;
  color: #8B4513;
  margin-bottom: 16rpx;
  font-family: "楷体", "STKaiti";
  letter-spacing: 2rpx;
}

.sort-buttons {
  display: flex;
  justify-content: space-between;
  margin: 20rpx 0;
}

.sort-item {
  width: 150rpx;
  height: 70rpx;
  border-radius: 25%;
  background: #fff;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.08);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #333;
  margin-right: 10rpx;
  border: 3rpx solid #eee;
  transition: border 0.2s, background 0.2s;
}

.sort-item.active {
  border: 2rpx solid #ffb300;
  background: #fffbe6;
  color: #ffb300;
}

.sort-item:last-child {
  margin-right: 0;
}

.sort-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 12rpx;
  vertical-align: middle;
}
