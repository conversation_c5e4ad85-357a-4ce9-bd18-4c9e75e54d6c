<?php
require_once 'config.php';
require_once 'includes/db.php';
require_once 'includes/utils.php';
require_once 'includes/stats_calculator.php';

// 包含头部
include 'includes/header.php';
?>

<div class="main-content">
    <div class="container">
<?php

// 创建统计计算器
$stats = new StatsCalculator($db);

// 获取筛选参数
$sort_by = isset($_GET['sort']) ? Utils::sanitizeInput($_GET['sort']) : 'kills';
$order = isset($_GET['order']) ? Utils::sanitizeInput($_GET['order']) : 'desc';
$search = isset($_GET['search']) ? Utils::sanitizeInput($_GET['search']) : '';

// 获取玩家分页数据
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$page = max(1, $page);
$per_page = 16;
$offset = ($page - 1) * $per_page;

// 获取军衔分页数据
$rank_page = isset($_GET['rank_page']) ? (int)$_GET['rank_page'] : 1;
$rank_page = max(1, $rank_page);
$rank_per_page = 16;
$rank_offset = ($rank_page - 1) * $rank_per_page;

// 获取当前活跃的标签
$active_tab = isset($_GET['active_tab']) ? Utils::sanitizeInput($_GET['active_tab']) : 'player-data';

// 构建查询条件
$where = '';
if (!empty($search)) {
    $where = "WHERE p.nickname LIKE '%{$search}%' OR p.player_rank LIKE '%{$search}%'";
}

// 构建排序条件
$order_sql = '';
switch ($sort_by) {
    case 'nickname':
        $order_sql = "ORDER BY p.nickname {$order}";
        break;
    case 'kills':
        $order_sql = "ORDER BY total_kills {$order}";
        break;
    case 'deaths':
        $order_sql = "ORDER BY total_deaths {$order}";
        break;
    case 'kd':
        $order_sql = "ORDER BY (CASE WHEN total_deaths = 0 THEN total_kills ELSE total_kills / total_deaths END) {$order}";
        break;
    case 'wins':
        $order_sql = "ORDER BY total_wins {$order}";
        break;
    case 'losses':
        $order_sql = "ORDER BY total_losses {$order}";
        break;
    case 'rank':
        $order_sql = "ORDER BY p.player_rank {$order}";
        break;
    default:
        $order_sql = "ORDER BY total_kills {$order}";
        break;
}

// 获取总记录数
$count_sql = "SELECT COUNT(*) as total FROM players p {$where}";
$result = $db->getRow($count_sql);
$total_records = $result['total'];
$total_pages = ceil($total_records / $per_page);

// 获取玩家数据
$sql = "SELECT 
            p.nickname,
            p.player_rank,
            p.id as player_id,
            SUM(g.kills) as total_kills,
            SUM(g.deaths) as total_deaths,
            SUM(g.wins) as total_wins,
            SUM(g.losses) as total_losses,
            (CASE WHEN SUM(g.deaths) = 0 THEN SUM(g.kills) ELSE SUM(g.kills) / SUM(g.deaths) END) as kd,
            (CASE WHEN (SUM(g.wins) + SUM(g.losses)) = 0 THEN 0 ELSE (SUM(g.wins) / (SUM(g.wins) + SUM(g.losses))) * 100 END) as win_rate,
            MAX(g.team) as team
        FROM 
            players p
        LEFT JOIN 
            game_records g ON p.id = g.player_id
        {$where}
        GROUP BY 
            p.id, p.nickname, p.player_rank
        {$order_sql}
        LIMIT {$offset}, {$per_page}";

$players_data = $db->getRows($sql);

// 获取所有玩家的队伍信息，用于计算每个队伍的平均KD值
$team_sql = "SELECT 
                gr.team,
                SUM(gr.kills) as team_kills,
                SUM(gr.deaths) as team_deaths
            FROM 
                game_records gr
            WHERE 
                gr.team IS NOT NULL AND gr.team != ''
            GROUP BY 
                gr.team";
$team_stats = $db->getRows($team_sql);

// 计算每个队伍的平均KD值
$team_kd = [];
foreach ($team_stats as $stat) {
    $team_kd[$stat['team']] = ($stat['team_deaths'] > 0) ? $stat['team_kills'] / $stat['team_deaths'] : $stat['team_kills'];
}

// 为每个玩家计算KPR和DPR
foreach ($players_data as &$player) {
    $total_games = $player['total_wins'] + $player['total_losses'];
    $player['kpr'] = ($total_games > 0) ? $player['total_kills'] / $total_games : 0;
    $player['dpr'] = ($total_games > 0) ? $player['total_deaths'] / $total_games : 0;
}
unset($player);
$players = $players_data;

// 获取汇总统计
$sql = "SELECT 
            COUNT(DISTINCT p.id) as player_count,
            COUNT(DISTINCT gr.game_id) as game_count,
            SUM(gr.kills) as total_kills,
            SUM(gr.deaths) as total_deaths,
            SUM(gr.wins) as total_wins,
            SUM(gr.losses) as total_losses
        FROM 
            players p
        LEFT JOIN 
            game_records gr ON p.id = gr.player_id";

$summary = $db->getRow($sql);

// 获取军衔分布
$rank_stats_all = $stats->getStatsByRank();

// 获取军衔分布数量
$total_rank_records = count($rank_stats_all);
$total_rank_pages = ceil($total_rank_records / $rank_per_page);

// 分页显示军衔数据
$rank_stats = array_slice($rank_stats_all, $rank_offset, $rank_per_page);
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        /* 移除所有滚动条相关样式 */
        body::-webkit-scrollbar,
        div::-webkit-scrollbar,
        .dashboard-container::-webkit-scrollbar,
        .stats-cards::-webkit-scrollbar,
        *::-webkit-scrollbar {
            width: 0 !important;
            height: 0 !important;
            display: none !important;
        }
        
        body, div, .dashboard-container, .stats-cards, * {
            -ms-overflow-style: none !important;  /* IE 和 Edge */
            scrollbar-width: none !important;  /* Firefox */
        }
        
        /* 隐藏右侧滚动控件区域 */
        @media screen and (min-width: 768px) {
            body::after {
                content: '';
                position: fixed;
                top: 0;
                right: 0;
                width: 20px;
                height: 100%;
                background-color: transparent;
                z-index: 9999;
                pointer-events: none;
            }
        }
        
        /* 禁用特定位置的元素 */
        div[style*="position: fixed"][style*="right: 0"] {
            display: none !important;
            visibility: hidden !important;
            opacity: 0 !important;
            pointer-events: none !important;
        }
        
        .dashboard-container {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            padding: 8px;
            padding-right: 25px; /* 增加右侧内边距，防止滚动条出现 */
            max-width: 1200px;
            margin: 0 auto;
            position: relative; /* 添加相对定位 */
            overflow: visible !important; /* 确保不会出现滚动条 */
        }
        
        html, body {
            overflow-x: hidden;
            overflow-y: auto;
        }
        
        .page-title {
            text-align: left;
            margin-bottom: 8px;
            margin-top: 5px;
            color: #2c3e50;
            font-size: 1.3rem;
            font-weight: bold;
        }
        
        .stats-cards {
            display: flex;
            flex-wrap: nowrap;
            justify-content: space-between;
            gap: 5px;
            margin-bottom: 5px;
            overflow: visible !important;
            max-height: 60px;
        }
        
        .stats-card {
            background: #ffffff;
            color: #2c3e50;
            border-radius: 5px;
            padding: 1px 2px;
            box-shadow: 0 1px 5px rgba(0, 0, 0, 0.2);
            transition: transform 0.3s ease;
            width: 15%;
            max-width: 150px;
            height: 55px;
            text-align: center;
            border-left: 2px solid #3498db;
            flex-shrink: 0;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        
        .stats-card:hover {
            transform: translateY(+2px);
        }
        
        .stats-card-title {
            font-size: 0.75rem;
            margin-bottom: 2px;
            color: #7f8c8d;
        }
        
        .stats-card-value {
            font-size: 1.2rem;
            font-weight: bold;
            color: #3498db;
        }
        
        .card {
            background-color: white;
            border-radius: 6px;
            padding: 8px;
            box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
            margin-bottom: 8px;
        }
        
        .card-title {
            font-size: 1.1rem;
            color: #2c3e50;
            margin-bottom: 8px;
            padding-bottom: 5px;
            border-bottom: 1px solid #f1f1f1;
            font-weight: bold;
        }
        
        .table-responsive {
            overflow-x: auto;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .table th, .table td {
            padding: 6px 10px;
            text-align: left;
            border-bottom: 1px solid #f1f1f1;
            font-size: 0.85rem;
        }
        
        .table thead th {
            background-color: #f8f9fa;
            color: #2c3e50;
            font-weight: bold;
        }
        
        .table tbody tr:hover {
            background-color: #f8f9fa;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            margin-top: 10px;
            gap: 4px;
        }
        
        .pagination-link {
            display: inline-block;
            padding: 5px 10px;
            background-color: #f1f1f1;
            color: #333;
            text-decoration: none;
            border-radius: 3px;
            transition: background-color 0.3s;
            font-size: 0.8rem;
        }
        
        .pagination-link:hover {
            background-color: #ddd;
        }
        
        .pagination-active {
            background-color: #3498db;
            color: white;
        }
        
        .form-control {
            padding: 6px 10px;
            border: 1px solid #ddd;
            border-radius: 3px;
            width: 100%;
            font-size: 0.85rem;
        }
        
        .btn {
            padding: 6px 12px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            transition: background-color 0.3s;
            font-size: 0.85rem;
        }
        
        .btn-primary {
            background-color: #3498db;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #2980b9;
        }
        
        .btn-secondary {
            background-color: #95a5a6;
            color: white;
        }
        
        .btn-secondary:hover {
            background-color: #7f8c8d;
        }
        
        .text-center {
            text-align: center;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .stats-cards {
                grid-template-columns: 1fr;
            }
            
            .pagination {
                flex-wrap: wrap;
            }
        }
        
        /* 添加标签式分页导航样式 */
        .tab-navigation {
            display: flex;
            margin-bottom: 8px;
        }
        
        .tab-item {
            padding: 6px 10px;
            border: 1px solid #e74c3c;
            color: #e74c3c;
            font-weight: bold;
            text-decoration: none;
            margin-right: 6px;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 0.85rem;
        }
        
        .tab-item:hover, .tab-item.active {
            background-color: #e74c3c;
            color: white;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>

<div class="dashboard-container">
    <h1 class="page-title">数据汇总</h1>

    <div class="stats-cards">
        <div class="stats-card">
            <div class="stats-card-title">总玩家数</div>
            <div class="stats-card-value"><?php echo $summary['player_count']; ?></div>
        </div>
        <div class="stats-card">
            <div class="stats-card-title">总游戏场次</div>
            <div class="stats-card-value"><?php echo $summary['game_count']; ?></div>
        </div>
        <div class="stats-card">
            <div class="stats-card-title">总狙杀数</div>
            <div class="stats-card-value"><?php echo $summary['total_kills']; ?></div>
        </div>
        <div class="stats-card">
            <div class="stats-card-title">总死亡数</div>
            <div class="stats-card-value"><?php echo $summary['total_deaths']; ?></div>
        </div>
        <div class="stats-card">
            <div class="stats-card-title">总胜场数</div>
            <div class="stats-card-value"><?php echo $summary['total_wins']; ?></div>
        </div>
        <div class="stats-card">
            <div class="stats-card-title">总败场数</div>
            <div class="stats-card-value"><?php echo $summary['total_losses']; ?></div>
        </div>
    </div>

    <div class="card">
        <div class="card-title">玩家搜索</div>
        <form method="GET" action="inventory.php" class="filter-form">
            <div class="form-group" style="display: flex; gap: 10px; align-items: center;">
                <input type="text" name="search" class="form-control" placeholder="搜索玩家昵称或军衔" value="<?php echo $search; ?>" style="max-width: 250px;">
                <button type="submit" class="btn btn-primary">搜索</button>
                <?php if (!empty($search)): ?>
                    <a href="inventory.php" class="btn btn-secondary">重置</a>
                <?php endif; ?>
            </div>
        </form>
    </div>

    <!-- 添加标签导航 -->
    <div class="tab-navigation">
        <a href="#player-data" class="tab-item <?php echo $active_tab == 'player-data' ? 'active' : ''; ?>">玩家数据一览</a>
        <a href="#player-details" class="tab-item <?php echo $active_tab == 'player-details' ? 'active' : ''; ?>">玩家详细数据</a>
        <a href="#rank-data" class="tab-item <?php echo $active_tab == 'rank-data' ? 'active' : ''; ?>">军衔分布</a>
    </div>

    <!-- 玩家数据内容区 -->
    <div id="player-data" class="card tab-content <?php echo $active_tab == 'player-data' ? 'active' : ''; ?>">
        <div class="card-title">玩家数据一览</div>
        <div class="table-responsive">
            <table class="table data-table">
                <thead>
                    <tr>
                        <th>序号</th>
                        <th>
                            <a href="inventory.php?sort=nickname&order=<?php echo ($sort_by == 'nickname' && $order == 'asc') ? 'desc' : 'asc'; ?><?php echo !empty($search) ? '&search='.$search : ''; ?><?php echo isset($_GET['rank_page']) ? '&rank_page='.$rank_page : ''; ?>">
                                玩家昵称
                                <?php if ($sort_by == 'nickname'): ?>
                                    <i class="fa fa-sort-<?php echo $order == 'asc' ? 'up' : 'down'; ?>"></i>
                                <?php endif; ?>
                            </a>
                        </th>
                        <th>
                            <a href="inventory.php?sort=kills&order=<?php echo ($sort_by == 'kills' && $order == 'asc') ? 'desc' : 'asc'; ?><?php echo !empty($search) ? '&search='.$search : ''; ?><?php echo isset($_GET['rank_page']) ? '&rank_page='.$rank_page : ''; ?>">
                                总狙杀数
                                <?php if ($sort_by == 'kills'): ?>
                                    <i class="fa fa-sort-<?php echo $order == 'asc' ? 'up' : 'down'; ?>"></i>
                                <?php endif; ?>
                            </a>
                        </th>
                        <th>
                            <a href="inventory.php?sort=deaths&order=<?php echo ($sort_by == 'deaths' && $order == 'asc') ? 'desc' : 'asc'; ?><?php echo !empty($search) ? '&search='.$search : ''; ?><?php echo isset($_GET['rank_page']) ? '&rank_page='.$rank_page : ''; ?>">
                                总死亡数
                                <?php if ($sort_by == 'deaths'): ?>
                                    <i class="fa fa-sort-<?php echo $order == 'asc' ? 'up' : 'down'; ?>"></i>
                                <?php endif; ?>
                            </a>
                        </th>
                        <th>
                            <a href="inventory.php?sort=kd&order=<?php echo ($sort_by == 'kd' && $order == 'asc') ? 'desc' : 'asc'; ?><?php echo !empty($search) ? '&search='.$search : ''; ?><?php echo isset($_GET['rank_page']) ? '&rank_page='.$rank_page : ''; ?>">
                                KD值
                                <?php if ($sort_by == 'kd'): ?>
                                    <i class="fa fa-sort-<?php echo $order == 'asc' ? 'up' : 'down'; ?>"></i>
                                <?php endif; ?>
                            </a>
                        </th>
                        <th>
                            <a href="inventory.php?sort=wins&order=<?php echo ($sort_by == 'wins' && $order == 'asc') ? 'desc' : 'asc'; ?><?php echo !empty($search) ? '&search='.$search : ''; ?><?php echo isset($_GET['rank_page']) ? '&rank_page='.$rank_page : ''; ?>">
                                总胜场
                                <?php if ($sort_by == 'wins'): ?>
                                    <i class="fa fa-sort-<?php echo $order == 'asc' ? 'up' : 'down'; ?>"></i>
                                <?php endif; ?>
                            </a>
                        </th>
                        <th>
                            <a href="inventory.php?sort=losses&order=<?php echo ($sort_by == 'losses' && $order == 'asc') ? 'desc' : 'asc'; ?><?php echo !empty($search) ? '&search='.$search : ''; ?><?php echo isset($_GET['rank_page']) ? '&rank_page='.$rank_page : ''; ?>">
                                总败场
                                <?php if ($sort_by == 'losses'): ?>
                                    <i class="fa fa-sort-<?php echo $order == 'asc' ? 'up' : 'down'; ?>"></i>
                                <?php endif; ?>
                            </a>
                        </th>
                        <th>胜率</th>
                        <th>
                            <a href="inventory.php?sort=rank&order=<?php echo ($sort_by == 'rank' && $order == 'asc') ? 'desc' : 'asc'; ?><?php echo !empty($search) ? '&search='.$search : ''; ?><?php echo isset($_GET['rank_page']) ? '&rank_page='.$rank_page : ''; ?>">
                                军衔
                                <?php if ($sort_by == 'rank'): ?>
                                    <i class="fa fa-sort-<?php echo $order == 'asc' ? 'up' : 'down'; ?>"></i>
                                <?php endif; ?>
                            </a>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($players)): ?>
                        <tr>
                            <td colspan="9" class="text-center">暂无数据</td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($players as $index => $player): ?>
                            <tr>
                                <td><?php echo $offset + $index + 1; ?></td>
                                <td><?php echo $player['nickname']; ?></td>
                                <td><?php echo $player['total_kills']; ?></td>
                                <td><?php echo $player['total_deaths']; ?></td>
                                <td><?php echo number_format($player['kd'] ?? 0, 2); ?></td>
                                <td><?php echo $player['total_wins']; ?></td>
                                <td><?php echo $player['total_losses']; ?></td>
                                <td><?php echo number_format($player['win_rate'] ?? 0, 2); ?>%</td>
                                <td><?php echo $player['player_rank']; ?></td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
        
        <?php if ($total_pages > 1): ?>
            <div class="text-center mt-4">
                <div style="display: flex; justify-content: center; margin-bottom: 10px;">
                    <a href="inventory.php?page=1&sort=<?php echo $sort_by; ?>&order=<?php echo $order; ?><?php echo !empty($search) ? '&search='.$search : ''; ?><?php echo isset($_GET['rank_page']) ? '&rank_page='.$rank_page : ''; ?>&active_tab=player-data" style="text-decoration: none; color: #6c757d; margin: 0 10px;">首页</a>
                    <a href="inventory.php?page=<?php echo max(1, $page - 1); ?>&sort=<?php echo $sort_by; ?>&order=<?php echo $order; ?><?php echo !empty($search) ? '&search='.$search : ''; ?><?php echo isset($_GET['rank_page']) ? '&rank_page='.$rank_page : ''; ?>&active_tab=player-data" style="text-decoration: none; color: #6c757d; margin: 0 10px;">上一页</a>
                    <a href="inventory.php?page=<?php echo min($total_pages, $page + 1); ?>&sort=<?php echo $sort_by; ?>&order=<?php echo $order; ?><?php echo !empty($search) ? '&search='.$search : ''; ?><?php echo isset($_GET['rank_page']) ? '&rank_page='.$rank_page : ''; ?>&active_tab=player-data" style="text-decoration: none; color: #6c757d; margin: 0 10px;">下一页</a>
                    <a href="inventory.php?page=<?php echo $total_pages; ?>&sort=<?php echo $sort_by; ?>&order=<?php echo $order; ?><?php echo !empty($search) ? '&search='.$search : ''; ?><?php echo isset($_GET['rank_page']) ? '&rank_page='.$rank_page : ''; ?>&active_tab=player-data" style="text-decoration: none; color: #6c757d; margin: 0 10px;">末页</a>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- 玩家详细数据内容区 -->
    <div id="player-details" class="card tab-content <?php echo $active_tab == 'player-details' ? 'active' : ''; ?>">
        <div class="card-title">玩家详细数据</div>
        <div class="table-responsive">
            <table class="table data-table">
                <thead>
                    <tr>
                        <th>序号</th>
                        <th>
                            <a href="inventory.php?sort=nickname&order=<?php echo ($sort_by == 'nickname' && $order == 'asc') ? 'desc' : 'asc'; ?><?php echo !empty($search) ? '&search='.$search : ''; ?>&active_tab=player-details">
                                玩家昵称
                                <?php if ($sort_by == 'nickname'): ?>
                                    <i class="fa fa-sort-<?php echo $order == 'asc' ? 'up' : 'down'; ?>"></i>
                                <?php endif; ?>
                            </a>
                        </th>
                        <th>Impact 影响力</th>
                        <th>KPR 平均每局狙杀</th>
                        <th>DPR 平均每局死亡</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($players)): ?>
                        <tr>
                            <td colspan="5" class="text-center">暂无数据</td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($players as $index => $player): ?>
                            <?php 
                                // 计算Impact影响力 (KD值/场均KD值)
                                $player_kd = $player['kd'] ?? 0;
                                
                                // 计算场均KD - 如果玩家有队伍信息，使用队伍的KD，否则使用总体平均KD
                                $avg_kd = 0;
                                if (!empty($player['team']) && isset($team_kd[$player['team']])) {
                                    $avg_kd = $team_kd[$player['team']];
                                } else {
                                    $avg_kd = ($summary['total_deaths'] > 0) ? $summary['total_kills'] / $summary['total_deaths'] : 0;
                                }
                                
                                // 计算Impact影响力
                                $impact = ($avg_kd > 0) ? $player_kd / $avg_kd : $player_kd;
                            ?>
                            <tr>
                                <td><?php echo $offset + $index + 1; ?></td>
                                <td><?php echo $player['nickname']; ?></td>
                                <td><?php echo number_format($impact, 2); ?></td>
                                <td><?php echo number_format($player['kpr'] ?? 0, 2); ?></td>
                                <td><?php echo number_format($player['dpr'] ?? 0, 2); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
        
        <?php if ($total_pages > 1): ?>
            <div class="text-center mt-4">
                <div style="display: flex; justify-content: center; margin-bottom: 10px;">
                    <a href="inventory.php?page=1&sort=<?php echo $sort_by; ?>&order=<?php echo $order; ?><?php echo !empty($search) ? '&search='.$search : ''; ?>&active_tab=player-details" style="text-decoration: none; color: #6c757d; margin: 0 10px;">首页</a>
                    <a href="inventory.php?page=<?php echo max(1, $page - 1); ?>&sort=<?php echo $sort_by; ?>&order=<?php echo $order; ?><?php echo !empty($search) ? '&search='.$search : ''; ?>&active_tab=player-details" style="text-decoration: none; color: #6c757d; margin: 0 10px;">上一页</a>
                    <a href="inventory.php?page=<?php echo min($total_pages, $page + 1); ?>&sort=<?php echo $sort_by; ?>&order=<?php echo $order; ?><?php echo !empty($search) ? '&search='.$search : ''; ?>&active_tab=player-details" style="text-decoration: none; color: #6c757d; margin: 0 10px;">下一页</a>
                    <a href="inventory.php?page=<?php echo $total_pages; ?>&sort=<?php echo $sort_by; ?>&order=<?php echo $order; ?><?php echo !empty($search) ? '&search='.$search : ''; ?>&active_tab=player-details" style="text-decoration: none; color: #6c757d; margin: 0 10px;">末页</a>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- 军衔分布内容区 -->
    <div id="rank-data" class="card tab-content <?php echo $active_tab == 'rank-data' ? 'active' : ''; ?>">
        <div class="card-title">军衔分布</div>
        <div class="table-responsive">
            <table class="table">
                <thead>
                    <tr>
                        <th>军衔</th>
                        <th>玩家数量</th>
                        <th>总狙杀数</th>
                        <th>总死亡数</th>
                        <th>平均KD值</th>
                        <th>总胜场</th>
                        <th>总败场</th>
                        <th>胜率</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($rank_stats)): ?>
                        <tr>
                            <td colspan="8" class="text-center">暂无数据</td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($rank_stats as $index => $rank): ?>
                            <tr>
                                <td><?php echo !empty($rank['player_rank']) ? $rank['player_rank'] : '未知'; ?></td>
                                <td><?php echo $rank['player_count']; ?></td>
                                <td><?php echo $rank['total_kills']; ?></td>
                                <td><?php echo $rank['total_deaths']; ?></td>
                                <td><?php echo number_format($rank['kd'] ?? 0, 2); ?></td>
                                <td><?php echo $rank['total_wins']; ?></td>
                                <td><?php echo $rank['total_losses']; ?></td>
                                <td><?php echo $rank['win_rate'] ?? '0%'; ?></td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
        
        <?php if ($total_rank_pages > 1): ?>
            <div class="text-center mt-4">
                <div style="display: flex; justify-content: center; margin-bottom: 10px;">
                    <a href="inventory.php?rank_page=1&page=<?php echo $page; ?>&sort=<?php echo $sort_by; ?>&order=<?php echo $order; ?><?php echo !empty($search) ? '&search='.$search : ''; ?>&active_tab=rank-data" style="text-decoration: none; color: #6c757d; margin: 0 10px;">首页</a>
                    <a href="inventory.php?rank_page=<?php echo max(1, $rank_page - 1); ?>&page=<?php echo $page; ?>&sort=<?php echo $sort_by; ?>&order=<?php echo $order; ?><?php echo !empty($search) ? '&search='.$search : ''; ?>&active_tab=rank-data" style="text-decoration: none; color: #6c757d; margin: 0 10px;">上一页</a>
                    <a href="inventory.php?rank_page=<?php echo min($total_rank_pages, $rank_page + 1); ?>&page=<?php echo $page; ?>&sort=<?php echo $sort_by; ?>&order=<?php echo $order; ?><?php echo !empty($search) ? '&search='.$search : ''; ?>&active_tab=rank-data" style="text-decoration: none; color: #6c757d; margin: 0 10px;">下一页</a>
                    <a href="inventory.php?rank_page=<?php echo $total_rank_pages; ?>&page=<?php echo $page; ?>&sort=<?php echo $sort_by; ?>&order=<?php echo $order; ?><?php echo !empty($search) ? '&search='.$search : ''; ?>&active_tab=rank-data" style="text-decoration: none; color: #6c757d; margin: 0 10px;">末页</a>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <div class="card">
        <div class="card-title">数据导出</div>
        <p>如需导出数据，请<a href="export_data.php">点击这里</a>前往数据导出页面</p>
    </div>
</div>

<!-- 添加JavaScript代码用于标签切换和移除滚动控件 -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 获取所有标签和内容区
    const tabItems = document.querySelectorAll('.tab-item');
    const tabContents = document.querySelectorAll('.tab-content');
    
    // 为每个标签添加点击事件
    tabItems.forEach(tab => {
        tab.addEventListener('click', function(e) {
            e.preventDefault();
            
            // 移除所有标签和内容区的active类
            tabItems.forEach(item => item.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));
            
            // 为当前点击的标签添加active类
            this.classList.add('active');
            
            // 获取对应的内容区ID并显示
            const targetId = this.getAttribute('href').substring(1);
            document.getElementById(targetId).classList.add('active');
            
            // 更新URL参数，但不刷新页面
            const url = new URL(window.location.href);
            url.searchParams.set('active_tab', targetId);
            window.history.pushState({}, '', url);
        });
    });
    
    // 尝试查找并移除滚动控件
    function removeScrollControls() {
        // 查找所有可能的滚动控件
        const scrollControls = document.querySelectorAll('div[style*="position: fixed"][style*="right"][style*="overflow"]');
        scrollControls.forEach(control => {
            control.parentNode.removeChild(control);
        });
        
        // 查找右侧的所有固定定位元素
        const rightFixedElements = document.querySelectorAll('div[style*="position: fixed"][style*="right"]');
        rightFixedElements.forEach(element => {
            if (element.offsetWidth < 50 && element.offsetHeight < 150) {
                element.parentNode.removeChild(element);
            }
        });
    }
    
    // 页面加载后移除滚动控件
    removeScrollControls();
    
    // 监听DOM变化，移除动态添加的滚动控件
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.addedNodes.length) {
                removeScrollControls();
            }
        });
    });
    
    observer.observe(document.body, { childList: true, subtree: true });
});
</script>

    </div><!-- 结束 .container -->
</div><!-- 结束 .main-content -->

<?php
// 包含底部<!-- 结束 .container -->
</div><!-- 结束 .main-content -->

<?php
include 'includes/footer.php';
?> 