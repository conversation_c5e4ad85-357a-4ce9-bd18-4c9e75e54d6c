/* 全局样式 */
:root {
    --primary: #4361ee;
    --primary-light: #5a73f0;
    --primary-dark: #3a4fcc;
    --secondary: #6c757d;
    --success: #2ecc71;
    --danger: #e74c3c;
    --warning: #f39c12;
    --info: #3498db;
    --light: #f8f9fa;
    --dark: #212529;
    --gray-100: #f8f9fa;
    --gray-200: #e9ecef;
    --gray-300: #dee2e6;
    --gray-400: #ced4da;
    --gray-500: #adb5bd;
    --gray-600: #6c757d;
    --gray-700: #495057;
    --gray-800: #343a40;
    --gray-900: #212529;
    --sidebar-width: 250px;
    --sidebar-width-collapsed: 60px;
    --header-height: 60px;
    --font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
    --border-radius: 8px;
    --transition-speed: 0.3s;
    --box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    --content-padding: 25px;
}

body {
    font-family: var(--font-family);
    margin: 0;
    padding: 0;
    background-color: #f5f7fa;
    color: var(--gray-800);
    line-height: 1.6;
    height: 100%;
    overflow: hidden;
}

html {
    height: 100%;
    margin: 0;
    padding: 0;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
    box-sizing: border-box;
}

/* 头部 */
header {
    background-color: white;
    color: var(--gray-800);
    padding: 15px 0;
    box-shadow: var(--box-shadow);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    font-size: 24px;
    font-weight: bold;
}

.nav-links ul {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
}

.nav-links li {
    margin-left: 20px;
}

.nav-links a {
    color: var(--gray-800);
    text-decoration: none;
    font-size: 16px;
    transition: all 0.3s ease;
}

.nav-links a:hover {
    color: var(--primary);
}

/* 主内容区 */
.main-content {
    padding: var(--content-padding);
    background-color: #f5f7fa;
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    height: calc(100vh - var(--header-height));
    position: relative;
    z-index: 5;
    display: block;
    width: 100%;
}

/* 确保内容区域中的元素正确显示 */
.main-content > * {
    max-width: 100%;
}

/* 页面标题 */
.page-title {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--gray-300);
    color: var(--gray-800);
}

/* 卡片样式 */
.card {
    background-color: white;
    border-radius: var(--border-radius);
    margin-bottom: 25px;
    box-shadow: var(--box-shadow);
    overflow: hidden;
    border: none;
    transition: box-shadow 0.3s ease, transform 0.2s ease;
    padding: 20px;
}

.card:hover {
    box-shadow: 0 8px 25px rgba(67, 97, 238, 0.1);
}

.card-header {
    padding: 18px 25px;
    border-bottom: 1px solid var(--gray-200);
    background-color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--gray-800);
    margin: 0;
}

.card-body {
    padding: 25px;
}

.card-actions {
    display: flex;
    gap: 10px;
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--gray-700);
    font-size: 14px;
}

.form-control {
    width: 90%;
    padding: 12px 15px;
    font-size: 14px;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    transition: border-color 0.3s, box-shadow 0.3s;
    background-color: white;
}
.form-control.date {
    width: 50%;
}


.form-control:focus {
    border-color: var(--primary);
    outline: none;
    box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.1);
}

.form-select {
    width: 100%;
    padding: 12px 15px;
    font-size: 14px;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    background-color: white;
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%236c757d'%3E%3Cpath d='M7 10l5 5 5-5z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 15px center;
    background-size: 16px;
    transition: border-color 0.3s, box-shadow 0.3s;
}

.form-select:focus {
    border-color: var(--primary);
    outline: none;
    box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.1);
}

.form-text {
    font-size: 13px;
    color: var(--gray-600);
    margin-top: 5px;
}

.filter-form {
    padding: 20px;
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    align-items: center;
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 10px 20px;
    font-size: 14px;
    font-weight: 500;
    line-height: 1.5;
    text-align: center;
    text-decoration: none;
    white-space: nowrap;
    vertical-align: middle;
    cursor: pointer;
    user-select: none;
    border: 1px solid transparent;
    border-radius: var(--border-radius);
    transition: all 0.2s ease-in-out;
    position: relative;
    overflow: hidden;
}

.btn::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background: rgba(255, 255, 255, 0.1);
    opacity: 0;
    transition: opacity 0.2s ease;
}

.btn:hover::after {
    opacity: 1;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

.btn-lg {
    padding: 14px 24px;
    font-size: 16px;
}

.btn i {
    margin-right: 8px;
}

.btn-primary {
    background-color: var(--primary);
    color: white;
    border-color: var(--primary);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
}

.btn-success {
    background-color: var(--success);
    color: white;
    border-color: var(--success);
}

.btn-success:hover {
    background-color: #27ae60;
    border-color: #27ae60;
}

.btn-danger {
    background-color: var(--danger);
    color: white;
    border-color: var(--danger);
}

.btn-danger:hover {
    background-color: #c0392b;
    border-color: #c0392b;
}

.btn-secondary {
    background-color: var(--secondary);
    color: white;
    border-color: var(--secondary);
}

.btn-secondary:hover {
    background-color: #5a6268;
    border-color: #5a6268;
}

.btn-outline-primary {
    background-color: transparent;
    color: var(--primary);
    border-color: var(--primary);
}

.btn-outline-primary:hover {
    background-color: var(--primary);
    color: white;
}

.btn-outline-secondary {
    background-color: transparent;
    color: var(--secondary);
    border-color: var(--secondary);
}

.btn-outline-secondary:hover {
    background-color: var(--secondary);
    color: white;
}

/* 表格样式 */
.table-responsive {
    overflow-x: auto;
    border-radius: var(--border-radius);
    margin: 0;
}

.table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin-bottom: 0;
}

.table th, .table td {
    padding: 15px;
    text-align: left;
    vertical-align: middle;
    border-bottom: 1px solid var(--gray-200);
    font-size: 14px;
}

.table th {
    font-weight: 600;
    color: var(--gray-700);
    background-color: rgba(0, 0, 0, 0.02);
    position: relative;
    white-space: nowrap;
}

.table tbody tr:last-child td {
    border-bottom: none;
}

.table tbody tr:hover {
    background-color: rgba(67, 97, 238, 0.02);
}

.data-table .action-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    justify-content: flex-start;
}

/* 徽章样式 */
.badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 10px;
    font-size: 12px;
    font-weight: 500;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    border-radius: 50px;
    text-transform: capitalize;
}

.badge i {
    margin-right: 4px;
    font-size: 10px;
}

.bg-primary {
    background-color: var(--primary);
    color: #fff;
}

.bg-secondary {
    background-color: var(--secondary);
    color: #fff;
}

.bg-success {
    background-color: var(--success);
    color: #fff;
}

.bg-danger {
    background-color: var(--danger);
    color: #fff;
}

.bg-warning {
    background-color: var(--warning);
    color: #fff;
}

.bg-info {
    background-color: var(--info);
    color: #fff;
}

/* 分页样式 */
.pagination-container {
    display: flex;
    justify-content: center;
    margin: 25px 0 10px;
    padding: 15px 0;
}

.pagination {
    display: flex;
    padding: 0;
    margin: 0;
    list-style: none;
    border-radius: var(--border-radius);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.page-item {
    display: inline-block;
}

.page-link {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px 15px;
    margin-left: -1px;
    line-height: 1.25;
    color: var(--gray-700);
    background-color: #fff;
    border: 1px solid var(--gray-300);
    text-decoration: none;
    font-size: 14px;
    min-width: 40px;
    transition: all 0.2s ease;
}

.page-link:hover {
    z-index: 2;
    color: var(--primary);
    text-decoration: none;
    background-color: var(--gray-100);
    border-color: var(--gray-300);
}

.page-item.active .page-link {
    z-index: 3;
    color: #fff;
    background-color: var(--primary);
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.25);
}

.page-item.disabled .page-link {
    color: var(--gray-500);
    pointer-events: none;
    cursor: auto;
    background-color: #fff;
    border-color: var(--gray-300);
}

/* 警告框样式 */
.alert {
    position: relative;
    padding: 15px 20px;
    margin-bottom: 20px;
    border-radius: var(--border-radius);
    font-size: 14px;
    border-left: 4px solid transparent;
    background-color: #fff;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

.alert:before {
    font-family: "Font Awesome 6 Free";
    font-weight: 900;
    margin-right: 10px;
    font-size: 16px;
    vertical-align: middle;
    display: inline-block;
}

.alert-success {
    color: #2d7741;
    background-color: #e8f8ee;
    border-left-color: var(--success);
}

.alert-success:before {
    content: "\f058";
    color: var(--success);
}

.alert-danger {
    color: #b3372a;
    background-color: #fdedeb;
    border-left-color: var(--danger);
}

.alert-danger:before {
    content: "\f057";
    color: var(--danger);
}

.alert-info {
    color: #2674a6;
    background-color: #e5f2fa;
    border-left-color: var(--info);
}

.alert-info:before {
    content: "\f05a";
    color: var(--info);
}

.alert-warning {
    color: #c37a0e;
    background-color: #fff7e6;
    border-left-color: var(--warning);
}

.alert-warning:before {
    content: "\f071";
    color: var(--warning);
}

/* 数据卡片样式 */
.stats-cards {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 25px;
    margin-bottom: 25px;
}

.stats-card {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 25px;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border: none;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.stats-card-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    background-color: rgba(67, 97, 238, 0.1);
    color: var(--primary);
}

.stats-card-icon i {
    font-size: 22px;
}

.stats-card-content {
    flex: 1;
}

.stats-card-value {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 5px;
    color: var(--gray-800);
    position: relative;
    z-index: 1;
}

.stats-card-title {
    font-size: 14px;
    color: var(--gray-600);
    margin: 0;
    position: relative;
    z-index: 1;
    white-space: nowrap;
}

/* 图片和内容预览 */
.image-preview {
    display: inline-block;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.image-preview img {
    display: block;
    max-width: 100%;
    height: auto;
    transition: transform 0.3s ease;
}

.image-preview:hover img {
    transform: scale(1.05);
}

/* 开关按钮 */
.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 25px;
    margin: 0;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--gray-300);
    transition: .4s;
    border-radius: 25px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 17px;
    width: 17px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: var(--primary);
}

input:focus + .slider {
    box-shadow: 0 0 1px var(--primary);
}

input:checked + .slider:before {
    transform: translateX(25px);
}

/* 侧边栏改进样式 */
.side-nav {
    background: linear-gradient(180deg, #2c3e50 0%, #1a2a38 100%);
    box-shadow: 0 0 20px rgba(0,0,0,0.2);
}

.side-nav .nav-section-title {
    opacity: 0.7;
    font-weight: 500;
    letter-spacing: 1px;
    font-size: 11px;
}

.nav-link {
    border-radius: 8px;
    margin: 5px 0;
    transition: all 0.2s ease;
}

.nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    padding-left: 20px;
}

.nav-link.active {
    background: linear-gradient(90deg, var(--primary), var(--primary-light));
    box-shadow: 0 5px 15px rgba(67, 97, 238, 0.3);
}

/* 顶部导航栏美化 */
.top-header {
    border-bottom: 1px solid rgba(0,0,0,0.05);
}

.header-right .user-menu {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 5px 10px;
    border-radius: 50px;
    transition: all 0.2s ease;
    cursor: pointer;
}

.header-right .user-menu:hover {
    background-color: var(--gray-100);
}

.user-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: var(--primary);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

.user-info {
    font-size: 14px;
    font-weight: 500;
}

/* 针对不同游戏类型的标签样式 */
.game-type {
    display: inline-flex;
    align-items: center;
    padding: 4px 12px;
    border-radius: 50px;
    font-size: 12px;
    font-weight: 500;
}

.game-type-assassination {
    background-color: #ffebee;
    color: #e53935;
}

.game-type-deathmatch {
    background-color: #e8f5e9;
    color: #43a047;
}

.game-type-alliance {
    background-color: #e3f2fd;
    color: #1e88e5;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--gray-100);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--gray-400);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gray-500);
}

/* 响应式调整 */
@media (max-width: 992px) {
    .stats-cards {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .stats-cards {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .card-header {
        padding: 12px 15px;
    }
    
    .card-body {
        padding: 15px;
    }
    
    .table th, .table td {
        padding: 10px;
    }
    
    .filter-form {
        padding: 10px;
    }
    
    .pagination-container {
        padding: 0 15px 15px;
    }
    
    .main-content {
        height: calc(100vh - 50px);
        padding: 15px;
    }
}

@media (max-width: 576px) {
    .card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .card-actions {
        align-self: flex-end;
    }
    
    .table-responsive {
        margin: 0 -15px;
        width: calc(100% + 30px);
        border-radius: 0;
    }
    
    .table th, .table td {
        padding: 8px;
        font-size: 13px;
    }
    
    .btn {
        padding: 6px 12px;
        font-size: 13px;
    }
    
    .btn-sm {
        padding: 4px 8px;
        font-size: 12px;
    }
}

/* 新增样式 - 表格排序 */
.table th.sort-asc, .table th.sort-desc {
    position: relative;
    background-color: rgba(67, 97, 238, 0.05);
}

.table th.sort-asc::after, .table th.sort-desc::after {
    position: absolute;
    right: 10px;
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    font-size: 14px;
}

.table th.sort-asc::after {
    content: "\f0de"; /* fa-sort-up */
    color: var(--primary);
}

.table th.sort-desc::after {
    content: "\f0dd"; /* fa-sort-down */
    color: var(--primary);
}

/* 数据编辑表格 */
.edit-table {
    width: 100%;
    border-collapse: collapse;
}

.edit-table th, .edit-table td {
    padding: 10px;
    border: 1px solid var(--gray-300);
}

.edit-table input[type="text"],
.edit-table input[type="number"] {
    width: 100%;
    padding: 8px;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
}

/* Excel表格预览 */
.excel-preview {
    max-height: 600px;
    overflow-y: auto;
    margin-bottom: 20px;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    background-color: white;
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(67, 97, 238, 0.1);
    border-radius: 50%;
    border-top-color: var(--primary);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* 进度条 */
.progress-bar-container {
    width: 100%;
    height: 8px;
    background-color: var(--gray-200);
    border-radius: 4px;
    overflow: hidden;
    margin: 10px 0;
}

.progress-bar-fill {
    height: 100%;
    background-color: var(--primary);
    border-radius: 4px;
    transition: width 0.5s ease;
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.5s;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.slide-in {
    animation: slideIn 0.5s;
}

@keyframes slideIn {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* 修复打印样式 */
@media print {
    .side-nav, 
    .top-header,
    .pagination-container,
    .card-actions,
    .filter-form {
        display: none !important;
    }
    
    .content-wrapper {
        margin-left: 0 !important;
        width: 100% !important;
    }
    
    .main-content {
        padding: 0 !important;
        overflow: visible !important;
        height: auto !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid var(--gray-300) !important;
        margin-bottom: 15px !important;
        page-break-inside: avoid !important;
    }
    
    .table-responsive {
        overflow: visible !important;
    }
}

/* 兼容旧样式 */
.pagination-link {
    margin: 0 5px;
    padding: 8px 12px;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    color: var(--primary);
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination-link:hover {
    background-color: var(--gray-100);
}

.pagination-active {
    background-color: var(--primary);
    color: white;
    border-color: var(--primary);
}

/* 侧边栏收起样式 */
.side-nav.collapsed {
    width: var(--sidebar-width-collapsed);
}

.side-nav.collapsed .logo-text,
.side-nav.collapsed .nav-text,
.side-nav.collapsed .nav-section-title,
.side-nav.collapsed .sidebar-footer {
    opacity: 0;
    visibility: hidden;
}

.side-nav.collapsed .nav-icon {
    margin-right: 0;
}

.side-nav.collapsed .logo {
    justify-content: center;
}

.side-nav.collapsed .nav-link {
    justify-content: center;
    padding: 10px 0;
}

.side-nav.collapsed ~ .content-wrapper {
    margin-left: var(--sidebar-width-collapsed);
    width: calc(100% - var(--sidebar-width-collapsed));
}

body.sidebar-collapsed .content-wrapper {
    margin-left: var(--sidebar-width-collapsed);
    width: calc(100% - var(--sidebar-width-collapsed));
}

/* 移动设备侧边栏样式 */
@media (max-width: 768px) {
    .side-nav {
        transform: translateX(-100%);
        z-index: 1050;
    }
    
    body.sidebar-collapsed .side-nav {
        transform: translateX(0);
        width: var(--sidebar-width);
    }
    
    body.sidebar-collapsed .sidebar-backdrop {
        display: block;
        opacity: 1;
    }
    
    .content-wrapper {
        margin-left: 0 !important;
        width: 100% !important;
    }
    
    .mobile-toggle-btn {
        display: flex;
    }
}

/* 侧边栏遮罩层 */
.sidebar-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1040;
    display: none;
    opacity: 0;
    transition: opacity 0.3s;
}

/* 按钮波纹效果 */
.btn {
    position: relative;
    overflow: hidden;
}

.ripple {
    position: absolute;
    width: 10px;
    height: 10px;
    background-color: rgba(255, 255, 255, 0.4);
    border-radius: 50%;
    transform: scale(0);
    animation: ripple-effect 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-effect {
    0% {
        transform: scale(0);
        opacity: 0.5;
    }
    100% {
        transform: scale(40);
        opacity: 0;
    }
}

/* 移动切换按钮 */
.mobile-toggle-btn {
    display: none;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    padding: 0;
    background: transparent;
    border: none;
    color: var(--gray-700);
    font-size: 20px;
    cursor: pointer;
    margin-right: 15px;
}

@media (max-width: 768px) {
    .mobile-toggle-btn {
        display: flex;
    }
}

/* 页面标题样式 */
.current-page-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--gray-800);
    margin: 0;
}

/* 回到顶部按钮动画 */
.back-to-top {
    transition: all 0.3s ease;
}

.back-to-top:hover {
    background-color: var(--primary-dark);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

/* 图片预览样式 */
.image-overlay {
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
} 