<?php
require_once 'config.php';
require_once 'includes/db.php';
require_once 'includes/utils.php';
require_once 'includes/ExcelHandler.php';

use admin\includes\ExcelHandler;

// 检查ID参数
if (!isset($_GET['id']) || empty($_GET['id'])) {
    $_SESSION['message'] = '缺少游戏ID参数';
    $_SESSION['message_type'] = 'danger';
    header('Location: index.php');
    exit;
}

$game_id = (int)$_GET['id'];

// 获取游戏数据
$sql = "SELECT * FROM games WHERE id = {$game_id}";
$game = $db->getRow($sql);

if (!$game) {
    $_SESSION['message'] = '未找到指定游戏数据';
    $_SESSION['message_type'] = 'danger';
    header('Location: index.php');
    exit;
}

$unique_id = $game['unique_id'];
$tmp_file = __DIR__ . "/tmp/{$unique_id}.json";

if (file_exists($tmp_file)) {
    $records = json_decode(file_get_contents($tmp_file), true);
    $is_pending = true;
    
    // 修复：为每个记录添加或更新losses字段
    for ($i = 0; $i < count($records); $i++) {
        if (!isset($records[$i]['losses']) || $records[$i]['losses'] === 0) {
            // 根据游戏类型和队伍情况设置losses
            if ($game['game_type'] == '暗杀' || $game['game_type'] == '死斗') {
                // 根据队伍胜场数自动计算败场
                // 通常情况下，如果一个队伍有胜场，另一个队伍就有败场
                $team = $records[$i]['team'];
                $wins = isset($records[$i]['wins']) ? (int)$records[$i]['wins'] : 0;
                
                if ($team == '流星') {
                    // 流星队
                    if ($wins > 0) {
                        // 如果有胜场，通常败场为0
                        $records[$i]['losses'] = 0;
                    } else {
                        // 如果没有胜场，可能有败场
                        $records[$i]['losses'] = 1;
                    }
                } else if ($team == '蝴蝶') {
                    // 蝴蝶队
                    if ($wins > 0) {
                        // 如果有胜场，通常败场为0
                        $records[$i]['losses'] = 0;
                    } else {
                        // 如果没有胜场，可能有败场
                        $records[$i]['losses'] = 1;
                    }
                } else {
                    $records[$i]['losses'] = 0;
                }
            } else {
                // 默认情况下，设置losses为0
                $records[$i]['losses'] = 0;
            }
        }
    }
    
    // 更新临时文件
    file_put_contents($tmp_file, json_encode($records, JSON_UNESCAPED_UNICODE));
} else {
    // 兼容老数据，直接查数据库
    $sql = "SELECT gr.*, p.nickname, p.virtual_ip, p.player_rank, 
            COALESCE(gr.wins, 0) as wins, 
            COALESCE(gr.losses, 0) as losses,
            COALESCE(gr.kills, 0) as kills,
            COALESCE(gr.deaths, 0) as deaths,
            COALESCE(gr.team, '') as team
            FROM game_records gr
            JOIN players p ON gr.player_id = p.id
            WHERE gr.game_id = {$game_id}
            ORDER BY gr.id ASC";
    $records = $db->getRows($sql);
    $is_pending = false;
}

// 添加调试信息
if (empty($records)) {
    error_log("No records found for game_id: {$game_id}");
    // 检查game_records表中是否有数据
    $check_sql = "SELECT COUNT(*) as count FROM game_records WHERE game_id = {$game_id}";
    $check_result = $db->getRow($check_sql);
    error_log("Number of records in game_records: " . $check_result['count']);
}

$success = '';
$error = '';

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['save_records'])) {
        $success_count = 0;
        $error_count = 0;
        if ($is_pending) {
            // 只有临时数据时才插入
            foreach ($_POST['records'] as $idx => $record) {
                try {
                    $nickname = Utils::sanitizeInput($record['nickname']);
                    $virtual_ip = Utils::sanitizeInput($record['virtual_ip']);
                    $player_rank = Utils::sanitizeInput($record['player_rank']);
                    $kills = isset($record['kills']) ? (int)$record['kills'] : 0;
                    $deaths = isset($record['deaths']) ? (int)$record['deaths'] : 0;
                    $team = isset($record['team']) ? Utils::sanitizeInput($record['team']) : '';
                    $wins = isset($record['wins']) ? (int)$record['wins'] : 0;
                    $losses = isset($record['losses']) ? (int)$record['losses'] : 0;

                    // 检查玩家是否已存在
                    $sql = "SELECT id FROM players WHERE nickname = '{$db->escape($nickname)}' LIMIT 1";
                    $player = $db->getRow($sql);
                    if (!$player) {
                        $player_data = [
                            'nickname' => $nickname,
                            'virtual_ip' => $virtual_ip,
                            'player_rank' => $player_rank,
                            'first_seen' => date('Y-m-d H:i:s'),
                            'last_seen' => date('Y-m-d H:i:s')
                        ];
                        $player_id = $db->insert('players', $player_data);
                    } else {
                        $player_id = $player['id'];
                        // 可选：更新玩家信息
                        $sql = "UPDATE players SET 
                                virtual_ip = '{$db->escape($virtual_ip)}',
                                player_rank = '{$db->escape($player_rank)}',
                                last_seen = NOW()
                                WHERE id = {$player_id}";
                        $db->query($sql);
                    }
                    // 插入 game_record
                    if ($player_id) {
                        $game_record = [
                            'game_id' => $game_id,
                            'player_id' => $player_id,
                            'game_type' => $game['game_type'],
                            'virtual_ip' => $virtual_ip,
                            'player_rank' => $player_rank,
                            'kills' => $kills,
                            'deaths' => $deaths,
                            'team' => $team,
                            'wins' => $wins,
                            'losses' => $losses
                        ];
                        $db->insert('game_records', $game_record);
                        $success_count++;
                    } else {
                        $error_count++;
                    }
                } catch (Exception $e) {
                    $error_count++;
                    continue;
                }
            }
            // 删除临时文件
            if (file_exists($tmp_file)) unlink($tmp_file);
            $success = "成功保存 {$success_count} 条记录";
            // 重新查数据库渲染
            $sql = "SELECT gr.*, p.nickname, p.virtual_ip, p.player_rank, 
                    COALESCE(gr.wins, 0) as wins, 
                    COALESCE(gr.losses, 0) as losses,
                    COALESCE(gr.kills, 0) as kills,
                    COALESCE(gr.deaths, 0) as deaths,
                    COALESCE(gr.team, '') as team
                    FROM game_records gr
                    JOIN players p ON gr.player_id = p.id
                    WHERE gr.game_id = {$game_id}
                    ORDER BY gr.id ASC";
            $records = $db->getRows($sql);
            $is_pending = false;
        } else {
            // 更新游戏记录
            $success_count = 0;
            $error_count = 0;
            
            foreach ($_POST['records'] as $record_id => $record) {
                try {
                    $record_id = (int)$record_id;
                    
                    // 验证数据
                    $kills = isset($record['kills']) ? (int)$record['kills'] : 0;
                    $deaths = isset($record['deaths']) ? (int)$record['deaths'] : 0;
                    $team = isset($record['team']) ? Utils::sanitizeInput($record['team']) : '';
                    $wins = isset($record['wins']) ? (int)$record['wins'] : 0;
                    $losses = isset($record['losses']) ? (int)$record['losses'] : 0;
                    $nickname = isset($record['nickname']) ? Utils::sanitizeInput($record['nickname']) : '';
                    
                    // 更新记录
                    $sql = "UPDATE game_records gr 
                            JOIN players p ON gr.player_id = p.id 
                            SET 
                            gr.kills = {$kills},
                            gr.deaths = {$deaths},
                            gr.team = '{$db->escape($team)}',
                            gr.wins = {$wins},
                            gr.losses = {$losses},
                            gr.virtual_ip = '{$db->escape($record['virtual_ip'] ?? $record['virtual_ip'] ?? '')}',
                            gr.player_rank = '{$db->escape($record['player_rank'] ?? $record['player_rank'] ?? '')}',
                            gr.game_type = '{$db->escape($game['game_type'])}',
                            p.nickname = '{$db->escape($nickname)}'
                            WHERE gr.id = {$record_id} AND gr.game_id = {$game_id}";
                    
                    if ($db->query($sql)) {
                        $success_count++;
                    } else {
                        $error_count++;
                    }
                } catch (mysqli_sql_exception $e) {
                    // 检查是否是重复昵称错误
                    if (strpos($e->getMessage(), 'Duplicate entry') !== false && strpos($e->getMessage(), 'nickname') !== false) {
                        $error = "保存失败：昵称 '{$nickname}' 已被其他玩家使用，请使用其他昵称。";
                    } else {
                        $error = "保存失败：数据更新过程中发生错误。";
                    }
                    $error_count++;
                    continue;
                }
            }
            
            if ($error_count > 0) {
                if (empty($error)) {
                    $error = "保存失败：{$error_count} 条记录更新失败";
                }
            } else {
                $success = "成功更新 {$success_count} 条记录";
                Utils::logActivity('更新数据', "更新游戏数据：ID {$game_id}，更新 {$success_count} 条记录");
                
                // 重新获取游戏记录
                $sql = "SELECT gr.*, p.nickname, p.virtual_ip, p.player_rank, 
                        COALESCE(gr.wins, 0) as wins, 
                        COALESCE(gr.losses, 0) as losses,
                        COALESCE(gr.kills, 0) as kills,
                        COALESCE(gr.deaths, 0) as deaths,
                        COALESCE(gr.team, '') as team
                        FROM game_records gr
                        JOIN players p ON gr.player_id = p.id
                        WHERE gr.game_id = {$game_id}
                        ORDER BY gr.id ASC";
                $records = $db->getRows($sql);
            }
        }
    }
}

// Excel文件路径
$excel_path = EXCEL_DIR . $game['excel_file'];

// 包含头部 - 移到了所有可能重定向的代码之后
include 'includes/header.php';
?>
<h1 class="page-title">表格编辑</h1>

<?php if (!empty($error)): ?>
    <div class="alert alert-danger"><?php echo $error; ?></div>
<?php endif; ?>

<?php if (!empty($success)): ?>
    <div class="alert alert-success"><?php echo $success; ?></div>
<?php endif; ?>



<div class="card">
    <div class="card-title">表格数据编辑</div>
    <?php if (empty($records)): ?>
        <div class="alert alert-info">暂无数据记录</div>
    <?php else: ?>
        <form method="POST" action="create.php?id=<?php echo $game_id; ?>">

        <div class="form-group" style="margin-top: 20px;">
                <button type="submit" name="save_records" class="btn btn-primary">保存修改</button>
                <a href="index.php" class="btn btn-secondary">返回列表</a>
            </div>
            <div class="excel-preview">
                <table class="edit-table table-centered">
                    <thead>
                        <tr>
                            <th style="width: 15%;">玩家昵称</th>
                            <th style="width: 15%;">大厅虚拟IP</th>
                            <th style="width: 15%;">大厅军衔</th>
                            <th style="width: 10%;">狙杀</th>
                            <th style="width: 10%;">死亡</th>
                            <th style="width: 15%;">分组</th>
                            <th style="width: 10%;">胜场</th>
                            <th style="width: 10%;">败场</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if ($is_pending): ?>
                            <?php foreach ($records as $idx => $record): ?>
                                <tr data-id="<?php echo $idx; ?>">
                                    <td class="text-center">
                                        <input type="text" name="records[<?php echo $idx; ?>][nickname]" value="<?php echo htmlspecialchars($record['nickname']); ?>" class="form-control input-sm text-center">
                                    </td>
                                    <td class="text-center"><?php echo htmlspecialchars($record['virtual_ip']); ?></td>
                                    <td class="text-center"><?php echo $record['player_rank'] ? htmlspecialchars($record['player_rank']) : '无数据'; ?></td>
                                    <td class="text-center">
                                        <input type="number" name="records[<?php echo $idx; ?>][kills]" value="<?php echo $record['kills']; ?>" min="0" class="form-control input-sm text-center">
                                    </td>
                                    <td class="text-center">
                                        <input type="number" name="records[<?php echo $idx; ?>][deaths]" value="<?php echo $record['deaths']; ?>" min="0" class="form-control input-sm text-center">
                                    </td>
                                    <td class="text-center">
                                        <select name="records[<?php echo $idx; ?>][team]" class="form-select form-control input-sm text-center">
                                            <option value="流星" <?php echo $record['team'] == '流星' ? 'selected' : ''; ?>>流星</option>
                                            <option value="蝴蝶" <?php echo $record['team'] == '蝴蝶' ? 'selected' : ''; ?>>蝴蝶</option>
                                        </select>
                                    </td>
                                    <td class="text-center">
                                        <input type="number" name="records[<?php echo $idx; ?>][wins]" value="<?php echo isset($record['wins']) ? (int)$record['wins'] : 0; ?>" min="0" class="form-control input-sm text-center">
                                    </td>
                                    <td class="text-center">
                                        <input type="number" name="records[<?php echo $idx; ?>][losses]" value="<?php echo isset($record['losses']) ? (int)$record['losses'] : 0; ?>" min="0" class="form-control input-sm text-center">
                                    </td>
                                    <input type="hidden" name="records[<?php echo $idx; ?>][virtual_ip]" value="<?php echo htmlspecialchars($record['virtual_ip']); ?>">
                                    <input type="hidden" name="records[<?php echo $idx; ?>][player_rank]" value="<?php echo htmlspecialchars($record['player_rank']); ?>">
                                </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <?php foreach ($records as $record): ?>
                                <tr data-id="<?php echo $record['id']; ?>">
                                    <td class="text-center">
                                        <input type="text" name="records[<?php echo $record['id']; ?>][nickname]" value="<?php echo $record['nickname']; ?>" class="form-control input-sm text-center">
                                    </td>
                                    <td class="text-center"><?php echo $record['virtual_ip']; ?></td>
                                    <td class="text-center"><?php echo $record['player_rank'] ? $record['player_rank'] : '无数据'; ?></td>
                                    <td class="text-center">
                                        <input type="number" name="records[<?php echo $record['id']; ?>][kills]" value="<?php echo $record['kills']; ?>" min="0" class="form-control input-sm text-center">
                                    </td>
                                    <td class="text-center">
                                        <input type="number" name="records[<?php echo $record['id']; ?>][deaths]" value="<?php echo $record['deaths']; ?>" min="0" class="form-control input-sm text-center">
                                    </td>
                                    <td class="text-center">
                                        <select name="records[<?php echo $record['id']; ?>][team]" class="form-select form-control input-sm text-center">
                                            <option value="流星" <?php echo $record['team'] == '流星' ? 'selected' : ''; ?>>流星</option>
                                            <option value="蝴蝶" <?php echo $record['team'] == '蝴蝶' ? 'selected' : ''; ?>>蝴蝶</option>
                                        </select>
                                    </td>
                                    <td class="text-center">
                                        <input type="number" name="records[<?php echo $record['id']; ?>][wins]" value="<?php echo $record['wins']; ?>" min="0" class="form-control input-sm text-center">
                                    </td>
                                    <td class="text-center">
                                        <input type="number" name="records[<?php echo $record['id']; ?>][losses]" value="<?php echo isset($record['losses']) ? (int)$record['losses'] : 0; ?>" min="0" class="form-control input-sm text-center">
                                    </td>
                                    <input type="hidden" name="records[<?php echo $record['id']; ?>][virtual_ip]" value="<?php echo htmlspecialchars($record['virtual_ip'] ?? ''); ?>">
                                    <input type="hidden" name="records[<?php echo $record['id']; ?>][player_rank]" value="<?php echo htmlspecialchars($record['player_rank'] ?? ''); ?>">
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
            
            
        </form>
    <?php endif; ?>
</div>
<div class="card">
    <div class="card-title">游戏信息</div>
    <div class="row">
        <div class="col-md-6">
            <p><strong>唯一ID：</strong> <?php echo $game['unique_id']; ?></p>
            <p><strong>游戏类型：</strong> 
                <span class="game-type game-type-<?php echo $game['game_type'] == '暗杀' ? 'assassination' : ($game['game_type'] == '死斗' ? 'deathmatch' : 'alliance'); ?>">
                    <?php echo $game['game_type']; ?>
                </span>
            </p>
            <p><strong>上传时间：</strong> <?php echo date('Y-m-d H:i:s', strtotime($game['upload_time'])); ?></p>
            <p><strong>Excel文件：</strong> <?php echo $game['excel_file']; ?></p>
        </div>
        <div class="col-md-6">
            <div  style="max-width: 300px;">
                <img src="<?php echo SITE_URL . 'uploads/images/' . $game['image_file']; ?>" alt="游戏图片" style="max-width: 100%;">
            </div>
        </div>
    </div>
</div>

<style>
.table-centered th, 
.table-centered td {
    text-align: center;
    vertical-align: middle !important;
}

.edit-table {
    width: 100%;
    border-collapse: collapse;
}

.edit-table th, 
.edit-table td {
    padding: 8px;
    border: 1px solid #ddd;
}

.edit-table thead {
    background-color: #f5f5f5;
}

.edit-table .form-control.input-sm {
    height: 30px;
    padding: 5px 10px;
    font-size: 12px;
}

.edit-table input[type="number"] {
    width: 80px;
    margin: 0 auto;
}

.edit-table select.form-select {
    width: 100px;
    margin: 0 auto;
}
</style>

<script>
// 页面加载完后初始化表格编辑功能
document.addEventListener('DOMContentLoaded', function() {
    initExcelEditor();
});

// 保存单元格编辑
function saveExcelEdit(recordId, field, value) {
    // 直接使用AJAX更新数据
    fetch('ajax_handler.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `action=save_excel_cell&record_id=${recordId}&field=${field}&value=${value}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            // 成功保存
            showToast('单元格已更新', 'success');
        } else {
            showToast('保存失败：' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('保存失败：网络错误', 'error');
    });
}

// 初始化Excel编辑器
function initExcelEditor() {
    // 表格已使用表单元素直接编辑，无需额外初始化
}
</script>

<?php
// 包含底部
include 'includes/footer.php';
?>