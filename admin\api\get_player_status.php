<?php
require_once '../includes/utils.php';
require_once '../includes/db.php';

// 验证API密钥
if (!Utils::validateApiKey()) {
    Utils::apiResponse(false, '无效的API密钥');
}

// 获取玩家昵称
$nickname = isset($_GET['nickname']) ? Utils::sanitizeInput($_GET['nickname']) : '';

if (empty($nickname)) {
    Utils::apiResponse(false, '缺少玩家昵称参数');
}

// 查询玩家状态
$player = $db->getRow("SELECT id, nickname, is_banned FROM players WHERE nickname = '" . $db->escape($nickname) . "'");

if (!$player) {
    Utils::apiResponse(false, '玩家不存在');
}

Utils::apiResponse(true, '获取成功', [
    'player_id' => $player['id'],
    'nickname' => $player['nickname'],
    'is_banned' => (bool)$player['is_banned']
]); 