<?php
require_once 'config.php';
require_once 'includes/utils.php';
require_once 'includes/db.php';
require_once '../includes/formatting.php';
require_once '../includes/shortcodes.php';
require_once '../includes/markdown.php';

// 检查用户是否已登录且为管理员
Utils::checkLogin();

$announcement = [
    'id' => null,
    'title' => '',
    'content' => '',
    'status' => 0,
    'priority' => 0,
    'valid_from' => date('Y-m-d'),
    'valid_to' => date('Y-m-d', strtotime('+30 days'))
];

$editing = false;
$errors = [];

// 如果是编辑现有公告
if (isset($_GET['id'])) {
    $id = intval($_GET['id']);
    $sql = "SELECT * FROM announcements WHERE id = " . $db->escape($id);
    $loaded_announcement = $db->getRow($sql);
    
    if ($loaded_announcement) {
        $announcement = $loaded_announcement;
        $editing = true;
    } else {
        $_SESSION['error_message'] = "找不到指定的公告";
        header("Location: announcement_list.php");
        exit;
    }
}

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // 验证并获取表单数据
    $announcement['title'] = trim($_POST['title'] ?? '');
    $announcement['content'] = trim($_POST['content'] ?? '');
    $announcement['status'] = isset($_POST['status']) ? 1 : 0;
    $announcement['priority'] = isset($_POST['priority']) ? 1 : 0;
    $announcement['valid_from'] = $_POST['valid_from'] ?? date('Y-m-d');
    $announcement['valid_to'] = $_POST['valid_to'] ?? date('Y-m-d', strtotime('+30 days'));
    
    // 表单验证
    if (empty($announcement['title'])) {
        $errors[] = "公告标题不能为空";
    }
    
    if (empty($announcement['content'])) {
        $errors[] = "公告内容不能为空";
    }
    
    if (empty($announcement['valid_from']) || !strtotime($announcement['valid_from'])) {
        $errors[] = "请输入有效的开始日期";
    }
    
    if (empty($announcement['valid_to']) || !strtotime($announcement['valid_to'])) {
        $errors[] = "请输入有效的结束日期";
    }
    
    if (strtotime($announcement['valid_from']) > strtotime($announcement['valid_to'])) {
        $errors[] = "开始日期不能晚于结束日期";
    }
    
    // 如果没有错误，保存到数据库
    if (empty($errors)) {
        if ($editing) {
            // 更新现有公告
            $data = [
                'title' => $announcement['title'],
                'content' => $announcement['content'],
                'status' => $announcement['status'],
                'priority' => $announcement['priority'],
                'valid_from' => $announcement['valid_from'],
                'valid_to' => $announcement['valid_to'],
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            $where = "id = " . $db->escape($announcement['id']);
            $db->update('announcements', $data, $where);
            
            $_SESSION['success_message'] = "公告已成功更新";
        } else {
            // 创建新公告
            $data = [
                'title' => $announcement['title'],
                'content' => $announcement['content'],
                'status' => $announcement['status'],
                'priority' => $announcement['priority'],
                'valid_from' => $announcement['valid_from'],
                'valid_to' => $announcement['valid_to'],
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            $db->insert('announcements', $data);
            
            $_SESSION['success_message'] = "公告已成功创建";
        }
        
        header("Location: announcement_list.php");
        exit;
    }
}

$page_title = $editing ? "编辑公告" : "创建公告";
include 'includes/header.php';
?>

<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3"><?php echo $editing ? "编辑公告" : "创建公告"; ?></h1>
        <div>
        
            <a href="announcement_list.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> 返回列表
            </a>
        </div>
    </div>
    
    <?php if (!empty($errors)): ?>
        <div class="alert alert-danger">
            <ul class="mb-0">
                <?php foreach ($errors as $error): ?>
                    <li><?php echo htmlspecialchars($error); ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>
    
    <div class="card">
        <div class="card-body">
            <form method="post" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF'] . ($editing ? "?id=" . $announcement['id'] : "")); ?>">
                <div class="mb-3">
                    <label for="title" class="form-label">公告标题 <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="title" name="title" 
                           value="<?php echo htmlspecialchars($announcement['title']); ?>" required>
                </div>
                
                <div class="mb-3">
                    <label for="content" class="form-label">公告内容 <span class="text-danger">*</span></label>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <textarea class="form-control font-monospace" id="content" name="content" rows="10" required><?php echo htmlspecialchars($announcement['content']); ?></textarea>
                        </div>
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header">
                                    预览
                                </div>
                                <div class="card-body" id="preview-container">
                                    <div id="editor-preview" class="markdown-content"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="valid_from" class="form-label">开始日期 <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="valid_from" name="valid_from" 
                                   value="<?php echo htmlspecialchars($announcement['valid_from']); ?>" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="valid_to" class="form-label">结束日期 <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="valid_to" name="valid_to" 
                                   value="<?php echo htmlspecialchars($announcement['valid_to']); ?>" required>
                        </div>
                    </div>
                </div>
                
                <div class="mb-3 form-check">
                    <input type="checkbox" class="form-check-input" id="status" name="status" 
                           <?php echo $announcement['status'] ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="status">激活公告</label>
                    <small class="form-text text-muted d-block">激活的公告将在前台显示，否则作为草稿保存</small>
                </div>
                
                <div class="mb-4 form-check">
                    <input type="checkbox" class="form-check-input" id="priority" name="priority" 
                           <?php echo $announcement['priority'] ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="priority">高优先级</label>
                    <small class="form-text text-muted d-block">高优先级的公告将在列表中靠前显示</small>
                </div>
                
                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> <?php echo $editing ? "更新公告" : "保存公告"; ?>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>


<script src="https://cdn.jsdelivr.net/npm/marked@4.3.0/marked.min.js"></script>
<script>
document.addEventListener("DOMContentLoaded", function() {
    const contentEditor = document.getElementById('content');
    const previewElement = document.getElementById('editor-preview');
    
    // 配置Marked选项
    marked.setOptions({
        breaks: true,      // 识别回车为<br>
        gfm: true,         // 支持Github风格的Markdown
        headerIds: true,   // 为标题生成ID
        mangle: false,     // 不转义内联HTML
        sanitize: false,   // 不净化输出
        smartLists: true,  // 使用更智能的列表行为
        smartypants: true, // 使用更智能的标点符号
        xhtml: false       // 不使用XHTML关闭标签
    });
    
    // 实时预览功能
    function updatePreview() {
        const content = contentEditor.value;
        
        // 发送AJAX请求，使用PHP后端处理短代码和基本格式
        fetch('../includes/ajax_handler.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'action=process_content&content=' + encodeURIComponent(content)
        })
        .then(response => response.text())
        .then(processedContent => {
            // 使用marked进一步处理Markdown
            previewElement.innerHTML = marked.parse(processedContent);
            
            // 代码高亮
            if (typeof Prism !== 'undefined') {
                Prism.highlightAllUnder(previewElement);
            }
        })
        .catch(error => {
            console.error('处理内容时出错:', error);
            // 出错时回退到仅使用marked
            previewElement.innerHTML = marked.parse(content);
        });
    }
    
    // 初始预览
    updatePreview();
    
    // 输入时更新预览
    contentEditor.addEventListener('input', updatePreview);
    
    // 快捷工具按钮
    const addToolbarButtons = () => {
        const toolbarDiv = document.createElement('div');
        toolbarDiv.className = 'btn-group btn-group-sm editor-toolbar';
        toolbarDiv.setAttribute('role', 'group');
        toolbarDiv.setAttribute('aria-label', '编辑器工具栏');
        
        // 添加工具栏按钮
        [
            { icon: 'fas fa-heading', title: '标题', action: () => insertAtCursor('## ', '') },
            { icon: 'fas fa-bold', title: '粗体', action: () => insertAtCursor('**', '**') },
            { icon: 'fas fa-italic', title: '斜体', action: () => insertAtCursor('*', '*') },
            { icon: 'fas fa-list-ul', title: '无序列表', action: () => insertAtCursor('- ', '') },
            { icon: 'fas fa-list-ol', title: '有序列表', action: () => insertAtCursor('1. ', '') },
            { icon: 'fas fa-link', title: '链接', action: () => insertAtCursor('[链接文本](', ')') },
            { icon: 'fas fa-image', title: '图片', action: () => insertAtCursor('![图片描述](', ')') },
            { icon: 'fas fa-code', title: '代码', action: () => insertAtCursor('`', '`') },
            { icon: 'fas fa-quote-right', title: '引用', action: () => insertAtCursor('> ', '') }
        ].forEach(btn => {
            const button = document.createElement('button');
            button.type = 'button';
            button.className = 'btn btn-outline-secondary';
            button.title = btn.title;
            button.innerHTML = `<i class="${btn.icon}"></i>`;
            button.addEventListener('click', btn.action);
            toolbarDiv.appendChild(button);
        });
        
        // 添加工具栏到编辑器前
        contentEditor.parentNode.insertBefore(toolbarDiv, contentEditor);
    };
    
    // 在光标位置插入文本
    function insertAtCursor(textBefore, textAfter) {
        if (!contentEditor) return;
        
        const startPos = contentEditor.selectionStart;
        const endPos = contentEditor.selectionEnd;
        const selectedText = contentEditor.value.substring(startPos, endPos);
        
        contentEditor.value = 
            contentEditor.value.substring(0, startPos) + 
            textBefore + selectedText + textAfter + 
            contentEditor.value.substring(endPos);
        
        // 重新设置光标位置
        contentEditor.selectionStart = startPos + textBefore.length;
        contentEditor.selectionEnd = startPos + textBefore.length + selectedText.length;
        
        // 更新预览
        updatePreview();
        
        // 聚焦编辑器
        contentEditor.focus();
    }
    
    // 添加工具栏按钮
    addToolbarButtons();
});
</script>

<?php
include 'includes/footer.php';
?> 