<view class="container">
  <!-- 加载中 -->
  <view class="loading" wx:if="{{loading}}">
    <text>加载中...</text>
  </view>
  
  <block wx:elif="{{announcement}}">
    <!-- 公告内容区域 -->
    <view class="announcement-wrapper">
      <!-- 公告标题 -->
      <view class="announcement-title">{{announcement.title}}</view>
      
      <!-- 发布时间 -->
      <view class="announcement-meta">
        <text>发布时间：{{announcement.update_time || announcement.created_at || '未知时间'}}</text>
        <text wx:if="{{announcement.author}}">发布人：{{announcement.author}}</text>
      </view>
      
      <!-- 公告内容 -->
      <view class="announcement-content">
        <rich-text nodes="{{announcement.content}}" bindtap="onRichTextTap"></rich-text>
      </view>
      
      <!-- 底部安全区域，防止内容被底部栏遮挡 -->
      <view class="safe-bottom-area"></view>
    </view>
  </block>
  
  <!-- 错误提示 -->
  <view class="error" wx:else>
    <text>无法加载公告内容</text>
  </view>
</view> 