<view class="container">
  <view class="header">
    <view class="banner-container">
      <view class="banner-wrapper">
        <image class="banner-image" src="https://lxbl.online/HDIMG/input.jpg" mode="aspectFill"></image>
        <view class="title-wrapper">
          <text class="title">公告中心</text>
        </view>
      </view>
    </view>
  </view>
  
  <!-- 公告列表 -->
  <view class="announcements-list">
    <!-- 加载中状态 -->
    <view class="loading" wx:if="{{loading}}">
      <text>正在加载...</text>
    </view>
    
    <!-- 错误状态 -->
    <view class="error-box" wx:elif="{{errorMsg}}">
      <icon type="warn" size="50"></icon>
      <text class="error-msg">{{errorMsg}}</text>
      <button class="retry-btn" bindtap="retryLoading">重新加载</button>
    </view>
    
    <!-- 无数据状态 -->
    <view class="empty" wx:elif="{{announcements.length === 0}}">
      <text>暂无公告</text>
    </view>
    
    <!-- 公告列表项 -->
    <view 
      class="announcement-item {{item.is_top ? 'top-item' : ''}}" 
      wx:for="{{announcements}}" 
      wx:key="id" 
      bindtap="onTapAnnouncement"
      data-id="{{item.id}}"
    >
      <view class="announcement-info">
        <view class="announcement-title">
          <text wx:if="{{item.is_top}}" class="top-badge">置顶</text>
          <text>{{item.title}}</text>
          <text class="new-badge" wx:if="{{item.is_new}}">新</text>
        </view>
        <view class="announcement-time">{{item.publish_time || '未知时间'}}</view>
      </view>
     
    </view>
  </view>
</view> 