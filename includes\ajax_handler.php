<?php
/**
 * AJAX请求处理文件
 */

// 引入必要的文件
require_once '../admin/config.php';
require_once 'formatting.php';
require_once 'shortcodes.php';

// 设置响应头
header('Content-Type: text/plain; charset=UTF-8');

// 检查请求操作
$action = $_POST['action'] ?? '';

// 处理内容格式化请求
if ($action === 'process_content' && isset($_POST['content'])) {
    $content = $_POST['content'];
    
    // 使用formatting.php中的format_content函数完整处理内容
    $processed = format_content($content);
    
    // 返回处理后的内容
    echo $processed;
    exit;
}

// 默认响应
echo '错误：未知的请求操作';
exit; 