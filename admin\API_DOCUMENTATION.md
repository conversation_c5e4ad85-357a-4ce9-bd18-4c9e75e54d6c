# BDLX 游戏统计 API 文档

## 基础信息
- 基础URL: `/admin/api.php`
- 响应格式: JSON
- 字符编码: UTF-8

## API 接口列表

### 1. 获取玩家详细数据
**接口**: `GET /admin/api.php?path=player_details`

**参数**:
- `nickname` (必需): 玩家昵称
- `game_type` (可选): 游戏类型 (`暗杀`, `死斗`, `盟主`)

**响应示例**:
```json
{
  "status": "success",
  "message": "获取玩家详细数据成功",
  "data": {
    "nickname": "炫皇烈火见真情",
    "player_rank": "中将vip1",
    "game_type": "暗杀",
    "total_kills": 92,
    "total_deaths": 44,
    "total_wins": 110,
    "total_losses": 50,
    "total_games": 160,
    "win_rate": 68.75,
    "kd": 2.09,
    "impact": 1.55,
    "kpr": 0.58,
    "dpr": 0.28,
    "team": "流星",
    "avg_kd": 1.34,
    "other_game_types": {
      "死斗": {
        "kills": 28,
        "deaths": 15,
        "wins": 44,
        "losses": 35,
        "kd": 1.87
      }
    }
  }
}
```

### 2. 获取玩家在不同游戏类型下的统计数据
**接口**: `GET /admin/api.php?path=player_stats_by_game_type`

**参数**:
- `nickname` (必需): 玩家昵称

**响应示例**:
```json
{
  "status": "success",
  "message": "获取玩家游戏类型统计数据成功",
  "data": {
    "nickname": "炫皇烈火见真情",
    "player_rank": "中将vip1",
    "stats_by_game_type": {
      "暗杀": {
        "game_type": "暗杀",
        "total_kills": 92,
        "total_deaths": 44,
        "total_wins": 110,
        "total_losses": 50,
        "total_games": 160,
        "kd": 2.09,
        "impact": 1.55,
        "kpr": 0.58,
        "dpr": 0.28,
        "team": "流星",
        "avg_kd": 1.34
      },
      "死斗": {
        "game_type": "死斗",
        "total_kills": 28,
        "total_deaths": 15,
        "total_wins": 44,
        "total_losses": 35,
        "total_games": 79,
        "kd": 1.87,
        "impact": 2.55,
        "kpr": 0.35,
        "dpr": 0.19,
        "team": "蝴蝶",
        "avg_kd": 0.73
      },
      "盟主": {
        "game_type": "盟主",
        "total_kills": 0,
        "total_deaths": 0,
        "total_wins": 0,
        "total_losses": 0,
        "total_games": 0,
        "kd": 0,
        "impact": 0,
        "kpr": 0,
        "dpr": 0,
        "team": null,
        "avg_kd": 0
      }
    }
  }
}
```

### 3. 批量获取玩家统计数据
**接口**: `POST /admin/api.php?path=players_batch_stats`

**请求体**:
```json
{
  "nicknames": ["炫皇烈火见真情", "其他玩家"],
  "game_type": "暗杀"
}
```

**参数**:
- `nicknames` (必需): 玩家昵称数组
- `game_type` (可选): 游戏类型

**响应示例**:
```json
{
  "status": "success",
  "message": "批量获取玩家统计数据成功",
  "data": {
    "炫皇烈火见真情": {
      "nickname": "炫皇烈火见真情",
      "player_rank": "中将vip1",
      "game_type": "暗杀",
      "total_kills": 92,
      "total_deaths": 44,
      "total_wins": 110,
      "total_losses": 50,
      "total_games": 160,
      "win_rate": 68.75,
      "kd": 2.09,
      "impact": 2.09,
      "kpr": 0.58,
      "dpr": 0.28,
      "team": "流星"
    },
    "其他玩家": {
      "error": "未找到指定玩家",
      "nickname": "其他玩家"
    }
  }
}
```

## 数据字段说明

### 统计指标
- **total_kills**: 总击杀数
- **total_deaths**: 总死亡数
- **total_wins**: 总胜利数
- **total_losses**: 总失败数
- **total_games**: 总游戏场次
- **win_rate**: 胜率 (%)
- **kd**: KD比值 (击杀/死亡)
- **impact**: Impact影响力 (玩家KD / 平均KD)
- **kpr**: KPR (每场击杀数)
- **dpr**: DPR (每场死亡数)
- **avg_kd**: 平均KD值 (用于计算Impact)

### 游戏类型
- `暗杀`: 暗杀模式
- `死斗`: 死斗模式  
- `盟主`: 盟主模式

## 错误响应
```json
{
  "status": "error",
  "message": "错误描述",
  "data": null
}
```

## 使用示例

### JavaScript 调用示例
```javascript
// 获取单个玩家详细数据
fetch('/admin/api.php?path=player_details&nickname=炫皇烈火见真情&game_type=暗杀')
  .then(response => response.json())
  .then(data => console.log(data));

// 批量获取玩家数据
fetch('/admin/api.php?path=players_batch_stats', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    nicknames: ['炫皇烈火见真情', '其他玩家'],
    game_type: '暗杀'
  })
})
.then(response => response.json())
.then(data => console.log(data));
```
