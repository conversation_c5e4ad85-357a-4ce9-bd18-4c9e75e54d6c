<?php
require_once 'config.php';
require_once 'includes/db.php';
require_once 'includes/utils.php';

// 包含头部
include 'includes/header.php';

// 获取游戏类型参数
$game_type = isset($_GET['type']) ? Utils::sanitizeInput($_GET['type']) : 'assassination';
$search = isset($_GET['search']) ? Utils::sanitizeInput($_GET['search']) : '';
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$per_page = 20;
$offset = ($page - 1) * $per_page;

// 映射游戏类型到对应的视图和中文名称
$type_map = [
    'assassination' => ['view' => 'assassination_games', 'name' => '暗杀'],
    'deathmatch' => ['view' => 'deathmatch_games', 'name' => '死斗'],
    'boss' => ['view' => 'boss_games', 'name' => '盟主']
];

// 确保游戏类型有效
if (!isset($type_map[$game_type])) {
    $game_type = 'assassination';
}

$view_name = $type_map[$game_type]['view'];
$type_name = $type_map[$game_type]['name'];

// 构建查询条件
$where = '';
if (!empty($search)) {
    $where = "WHERE nickname LIKE '%{$db->escape($search)}%' OR player_rank LIKE '%{$db->escape($search)}%'";
}

// 获取总记录数
$count_sql = "SELECT COUNT(*) as total FROM {$view_name} {$where}";
$result = $db->getRow($count_sql);
$total_records = $result['total'];
$total_pages = ceil($total_records / $per_page);

// 获取数据
$sql = "SELECT * FROM {$view_name} {$where} ORDER BY kills DESC LIMIT {$offset}, {$per_page}";
$records = $db->getRows($sql);
?>

<div class="main-content">
    <div class="container">
        <h1 class="page-title"><?php echo $type_name; ?>游戏数据查看</h1>
        
        <!-- 游戏类型选择 -->
        <div class="card mb-3">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-filter me-2"></i>数据筛选
            </div>
            <div class="card-body">
                <div class="d-flex flex-column flex-md-row justify-content-between align-items-center gap-3">
                    <div class="game-type-selector">
                        <label class="form-label small mb-2"><i class="fas fa-gamepad me-1"></i>选择游戏类型：</label>
                        <div class="btn-group" role="group">
                            <a href="?type=assassination" class="btn <?php echo $game_type == 'assassination' ? 'btn-primary' : 'btn-outline-primary'; ?>">
                                <i class="fas fa-crosshairs me-1"></i>暗杀
                            </a>
                            <a href="?type=deathmatch" class="btn <?php echo $game_type == 'deathmatch' ? 'btn-primary' : 'btn-outline-primary'; ?>">
                                <i class="fas fa-skull me-1"></i>死斗
                            </a>
                            <a href="?type=boss" class="btn <?php echo $game_type == 'boss' ? 'btn-primary' : 'btn-outline-primary'; ?>">
                                <i class="fas fa-crown me-1"></i>盟主
                            </a>
                        </div>
                    </div>
                    <div class="search-form">
                        <form class="d-flex" method="GET" action="">
                            <input type="hidden" name="type" value="<?php echo $game_type; ?>">
                            <div class="input-group">
                                <input type="text" name="search" class="form-control" placeholder="搜索玩家或军衔..." value="<?php echo htmlspecialchars($search ?? ''); ?>">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i>搜索
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 数据表格 -->
        <div class="card">
            <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                <div>
                    <i class="fas fa-table me-2"></i><strong><?php echo $type_name; ?>游戏数据</strong>
                </div>
                <div class="small">
                    共 <span class="badge bg-light text-dark"><?php echo $total_records; ?></span> 条记录
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover table-bordered">
                        <thead class="table-light">
                            <tr>
                                <th><i class="fas fa-user me-1"></i>玩家昵称</th>
                                <th><i class="fas fa-network-wired me-1"></i>虚拟IP</th>
                                <th><i class="fas fa-medal me-1"></i>军衔</th>
                                <th><i class="fas fa-crosshairs me-1"></i>狙杀</th>
                                <th><i class="fas fa-skull me-1"></i>死亡</th>
                                <th><i class="fas fa-chart-line me-1"></i>KD值</th>
                                <th><i class="fas fa-users me-1"></i>分组</th>
                                <th><i class="fas fa-trophy me-1"></i>胜场</th>
                                <th><i class="fas fa-times-circle me-1"></i>败场</th>
                                <th><i class="fas fa-percentage me-1"></i>胜率</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (count($records) > 0): ?>
                                <?php foreach ($records as $record): ?>
                                    <?php 
                                        $kd = $record['deaths'] > 0 ? round($record['kills'] / $record['deaths'], 2) : $record['kills'];
                                        $total_games = $record['wins'] + $record['losses'];
                                        $win_rate = $total_games > 0 ? round(($record['wins'] / $total_games) * 100, 2) : 0;
                                    ?>
                                    <tr>
                                        <td class="fw-bold"><?php echo htmlspecialchars($record['nickname'] ?? ''); ?></td>
                                        <td><?php echo htmlspecialchars($record['virtual_ip'] ?? ''); ?></td>
                                        <td><?php echo htmlspecialchars($record['player_rank'] ?? ''); ?></td>
                                        <td class="text-center text-success fw-bold"><?php echo $record['kills']; ?></td>
                                        <td class="text-center text-danger"><?php echo $record['deaths']; ?></td>
                                        <td class="text-center fw-bold"><?php echo $kd; ?></td>
                                        <td class="text-center">
                                            <?php if ($record['team'] == '流星'): ?>
                                                <span class="badge bg-primary"><?php echo htmlspecialchars($record['team'] ?? ''); ?></span>
                                            <?php elseif ($record['team'] == '蝴蝶'): ?>
                                                <span class="badge bg-warning text-dark"><?php echo htmlspecialchars($record['team'] ?? ''); ?></span>
                                            <?php else: ?>
                                                <?php echo htmlspecialchars($record['team'] ?? ''); ?>
                                            <?php endif; ?>
                                        </td>
                                        <td class="text-center text-success"><?php echo $record['wins']; ?></td>
                                        <td class="text-center text-danger"><?php echo $record['losses']; ?></td>
                                        <td class="text-center">
                                            <div class="progress" style="height: 20px;">
                                                <div class="progress-bar bg-success" role="progressbar" style="width: <?php echo $win_rate; ?>%;" 
                                                     aria-valuenow="<?php echo $win_rate; ?>" aria-valuemin="0" aria-valuemax="100">
                                                    <?php echo $win_rate; ?>%
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="10" class="text-center py-4">
                                        <div class="alert alert-info mb-0">
                                            <i class="fas fa-info-circle me-2"></i>没有找到记录
                                        </div>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页 -->
                <?php if ($total_pages > 1): ?>
                <div class="d-flex justify-content-center mt-4">
                    <nav aria-label="数据分页">
                        <ul class="pagination">
                            <?php if ($page > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?type=<?php echo $game_type; ?>&page=<?php echo ($page - 1); ?>&search=<?php echo urlencode($search); ?>">
                                        <i class="fas fa-chevron-left"></i> 上一页
                                    </a>
                                </li>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                    <a class="page-link" href="?type=<?php echo $game_type; ?>&page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>"><?php echo $i; ?></a>
                                </li>
                            <?php endfor; ?>
                            
                            <?php if ($page < $total_pages): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?type=<?php echo $game_type; ?>&page=<?php echo ($page + 1); ?>&search=<?php echo urlencode($search); ?>">
                                        下一页 <i class="fas fa-chevron-right"></i>
                                    </a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<style>
/* 自定义样式 */
.game-type-selector .btn-group {
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.search-form .input-group {
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.table {
    vertical-align: middle;
}

.table thead th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    white-space: nowrap;
}

.table tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05) !important;
}

.progress {
    border-radius: 20px;
    overflow: hidden;
    box-shadow: inset 0 1px 2px rgba(0,0,0,0.1);
}

.progress-bar {
    transition: width 0.6s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    text-shadow: 0 1px 1px rgba(0,0,0,0.3);
}

.pagination .page-link {
    color: #4361ee;
    border-radius: 0.2rem;
    margin: 0 2px;
}

.pagination .page-item.active .page-link {
    background-color: #4361ee;
    border-color: #4361ee;
}

.badge {
    font-weight: 500;
    padding: 0.35em 0.65em;
}

@media (max-width: 768px) {
    .d-flex.flex-column.flex-md-row {
        gap: 1rem !important;
    }
    
    .game-type-selector, .search-form {
        width: 100%;
    }
    
    .table-responsive {
        border: 1px solid #dee2e6;
        border-radius: 0.25rem;
    }
}
</style>

<?php include 'includes/footer.php'; ?> 