<?php
require_once __DIR__ . '/../../admin/config.php';

class Utils {
    // 生成5位随机字符串（数字和字母组合）
    private static function generateRandomString($length = 5) {
        $characters = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $randomString = '';
        for ($i = 0; $i < $length; $i++) {
            $randomString .= $characters[mt_rand(0, strlen($characters) - 1)];
        }
        return $randomString;
    }

    // 生成唯一ID
    public static function generateUniqueId() {
        return self::generateRandomString(5) . '_' . date('YmdHis') . '_' . mt_rand(1000, 9999);
    }
    
    // 生成缩略图
    public static function generateThumbnail($sourcePath, $targetPath, $maxWidth = 50, $maxHeight = 50) {
        list($width, $height, $type) = getimagesize($sourcePath);
        
        // 计算缩放比例
        $ratio = min($maxWidth / $width, $maxHeight / $height);
        $new_width = round($width * $ratio);
        $new_height = round($height * $ratio);
        
        // 创建新图像
        $thumb = imagecreatetruecolor($new_width, $new_height);
        
        // 保持PNG透明度
        if ($type == IMAGETYPE_PNG) {
            imagealphablending($thumb, false);
            imagesavealpha($thumb, true);
        }
        
        // 根据图片类型创建源图像
        switch ($type) {
            case IMAGETYPE_JPEG:
                $source = imagecreatefromjpeg($sourcePath);
                break;
            case IMAGETYPE_PNG:
                $source = imagecreatefrompng($sourcePath);
                break;
            case IMAGETYPE_GIF:
                $source = imagecreatefromgif($sourcePath);
                break;
            default:
                return false;
        }
        
        // 调整图像大小
        imagecopyresampled($thumb, $source, 0, 0, 0, 0, $new_width, $new_height, $width, $height);
        
        // 保存缩略图
        $result = false;
        switch ($type) {
            case IMAGETYPE_JPEG:
                $result = imagejpeg($thumb, $targetPath, 90);
                break;
            case IMAGETYPE_PNG:
                $result = imagepng($thumb, $targetPath, 9);
                break;
            case IMAGETYPE_GIF:
                $result = imagegif($thumb, $targetPath);
                break;
        }
        
        // 释放内存
        imagedestroy($source);
        imagedestroy($thumb);
        
        return $result;
    }
    
    // 安全地上传文件
    public static function uploadFile($file, $targetDir, $allowedTypes = [], $customUniqueId = null) {
        // 检查上传目录
        if (!file_exists($targetDir)) {
            if (!mkdir($targetDir, 0755, true)) {
                return [
                    'success' => false,
                    'error' => '无法创建上传目录'
                ];
            }
        }
        
        // 检查文件是否存在
        if (!isset($file['name']) || !isset($file['tmp_name']) || $file['error'] != 0) {
            return [
                'success' => false,
                'error' => '文件上传失败或未选择文件'
            ];
        }
        
        // 检查文件类型
        $fileType = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!empty($allowedTypes) && !in_array($fileType, $allowedTypes)) {
            return [
                'success' => false,
                'error' => '不支持的文件类型，允许的类型：' . implode(', ', $allowedTypes)
            ];
        }
        
        // 生成唯一文件名
        $uniqueId = $customUniqueId ? $customUniqueId : self::generateUniqueId();
        $newFileName = $uniqueId . '.' . $fileType;
        $targetFilePath = $targetDir . $newFileName;
        
        // 移动文件
        if (move_uploaded_file($file['tmp_name'], $targetFilePath)) {
            $result = [
                'success' => true,
                'file_name' => $newFileName,
                'original_name' => $file['name'],
                'file_path' => $targetFilePath,
                'unique_id' => $uniqueId,
                'file_type' => $fileType
            ];
            
            // 如果是图片文件且上传到images目录，则生成缩略图
            if (in_array($fileType, ['jpg', 'jpeg', 'png', 'gif']) && strpos($targetDir, IMAGES_DIR) !== false) {
                $thumbFileName = $uniqueId . '_thumb.' . $fileType;
                $thumbPath = THUMBS_DIR . $thumbFileName;
                
                if (self::generateThumbnail($targetFilePath, $thumbPath)) {
                    $result['thumb_name'] = $thumbFileName;
                    $result['thumb_path'] = $thumbPath;
                }
            }
            
            return $result;
        }
        
        return [
            'success' => false,
            'error' => '文件保存失败'
        ];
    }
    
    // 验证API密钥
    public static function validateApiKey() {
        global $db;
        $apiKey = null;
        
        // 检查请求头
        $headers = getallheaders();
        if (isset($headers['X-API-Key'])) {
            $apiKey = $headers['X-API-Key'];
        }
        
        // 检查URL参数
        if (!$apiKey && isset($_GET['api_key'])) {
            $apiKey = $_GET['api_key'];
        }
        
        // 如果没有提供API密钥，返回false
        if (!$apiKey) {
            return false;
        }
        
        // 首先尝试从数据库查询活跃的API密钥
        if ($db) {
            $sql = "SELECT * FROM api_settings WHERE api_key = '" . $db->escape($apiKey) . "' AND is_active = 1";
            $result = $db->getRow($sql);
            
            if ($result) {
                // 如果找到了匹配的API密钥，更新最后使用时间
                $now = date('Y-m-d H:i:s');
                $db->update('api_settings', ['last_used' => $now], "id = " . $result['id']);
                return true;
            }
        }
        
        // 如果数据库查询失败或者没有找到，回退到配置文件中的API_KEY
        if ($apiKey === API_KEY) {
            return true;
        }
        
        return false;
    }
    
    // 输出API响应
    public static function apiResponse($status, $message, $data = null) {
        // 自动记录API访问日志
        $path = isset($_GET['path']) ? $_GET['path'] : '';
        Utils::logActivity('API访问', $path);
        header('Content-Type: application/json');
        echo json_encode([
            'status' => $status,
            'message' => $message,
            'data' => $data
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 检查登录状态
    public static function checkLogin() {
        // 在任何输出之前启动会话
        if (session_status() == PHP_SESSION_NONE && !headers_sent()) {
            session_start();
        }
        
        // 如果未登录，重定向到登录页面
        if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
            if (!headers_sent()) {
                header('Location: login.php');
                exit;
            } else {
                echo '<script>window.location.href = "login.php";</script>';
                exit;
            }
        }
    }
    
    // 记录日志
    public static function logActivity($action, $details = '') {
        // 判断日志类型
        if ($action === 'API访问') {
            $logFile = '../logs/api.log';
        } else {
            $logFile = '../logs/activity.log';
        }
        $logDir = dirname($logFile);
        // 确保日志目录存在
        if (!file_exists($logDir)) {
            mkdir($logDir, 0755, true);
        }
        $timestamp = date('Y-m-d H:i:s');
        $ip = $_SERVER['REMOTE_ADDR'];
        $user = isset($_SESSION['admin_username']) ? $_SESSION['admin_username'] : 'guest';
        $logMessage = "[{$timestamp}] [{$ip}] [{$user}] {$action}: {$details}\n";
        file_put_contents($logFile, $logMessage, FILE_APPEND);
    }
    
    // 清理输入数据
    public static function sanitizeInput($data) {
        $data = trim($data);
        $data = stripslashes($data);
        $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
        return $data;
    }
    
    // 生成CSRFToken
    public static function generateCsrfToken() {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }
    
    // 验证CSRFToken
    public static function validateCsrfToken($token) {
        if (!isset($_SESSION['csrf_token']) || $token !== $_SESSION['csrf_token']) {
            return false;
        }
        return true;
    }
    
    // 检查当前用户是否为管理员
    public static function isAdmin() {
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }
        
        return isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true;
    }
    
    /**
     * 格式化文件大小，转换为可读形式
     * 
     * @param int $bytes 文件大小（字节）
     * @param int $precision 小数点后位数
     * @return string 格式化后的文件大小
     */
    public static function formatFileSize($bytes, $precision = 2) {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, $precision) . ' ' . $units[$pow];
    }
    
    // 检查并创建目录
    public static function ensureDirectoryExists($dir) {
        if (!file_exists($dir)) {
            return mkdir($dir, 0755, true);
        }
        return true;
    }
    
    // 删除文件
    public static function deleteFile($filePath) {
        if (file_exists($filePath) && is_file($filePath)) {
            return unlink($filePath);
        }
        return false;
    }
    
    /**
     * 解析Markdown内容为HTML
     * 
     * @param string $markdown Markdown文本
     * @return string 解析后的HTML
     */
    public static function parseMarkdown($markdown) {
        if (empty($markdown)) return '';
        
        $html = $markdown;
        
        // 收集所有标题用于生成目录
        $toc = [];
        $hasToc = strpos($html, '[toc]') !== false;
        
        if ($hasToc) {
            // 收集所有标题
            preg_match_all('/^(#{1,3}) (.*?)$/m', $html, $matches, PREG_SET_ORDER);
            foreach ($matches as $match) {
                $level = strlen($match[1]);
                $title = trim($match[2]);
                $anchor = 'heading-' . urlencode(strtolower(str_replace(' ', '-', $title)));
                $toc[] = [
                    'level' => $level,
                    'title' => $title,
                    'anchor' => $anchor
                ];
            }
        }
        
        // 图片（需要在链接之前处理）
        $html = preg_replace_callback('/!\[(.*?)\]\((.*?)\)/', function($matches) {
            $alt = $matches[1];
            $url = $matches[2];
            
            // 处理图片URL
            if (strpos($url, 'http') !== 0 && strpos($url, '//') !== 0) {
                // 移除URL中可能存在的SITE_URL
                $url = str_replace(SITE_URL, '', $url);
                // 移除开头的斜杠
                $url = ltrim($url, '/');
                // 添加SITE_URL
                $url = SITE_URL . $url;
            }
            
            // 处理特殊情况：如果URL包含 /BDLX/uploads/ 但没有完整的SITE_URL
            if (strpos($url, '/BDLX/uploads/') !== false && strpos($url, SITE_URL) === false) {
                $url = str_replace('/BDLX/uploads/', SITE_URL . 'uploads/', $url);
            }
            
            // 移除URL中的多余斜杠
            $url = preg_replace('#([^:])//+#', '$1/', $url);
            
            return sprintf(
                '<div class="article-image">
                    <img src="%s" alt="%s" class="img-fluid" onerror="this.onerror=null; this.src=\'%suploads/images/default.png\';">
                    %s
                </div>',
                $url,
                htmlspecialchars($alt),
                SITE_URL,
                !empty($alt) ? sprintf('<div class="image-caption">%s</div>', htmlspecialchars($alt)) : ''
            );
        }, $html);
        
        // 标题（添加锚点）
        $html = preg_replace_callback('/^(#{1,3}) (.*?)$/m', function($matches) {
            $level = strlen($matches[1]);
            $title = trim($matches[2]);
            $anchor = 'heading-' . urlencode(strtolower(str_replace(' ', '-', $title)));
            return sprintf('<h%d id="%s">%s</h%d>', $level, $anchor, $title, $level);
        }, $html);
        
        // 生成目录HTML
        if ($hasToc) {
            $tocHtml = '<div class="article-toc"><h2>目录</h2><ul class="toc-list">';
            foreach ($toc as $item) {
                $indent = ($item['level'] - 1) * 20;
                $tocHtml .= sprintf(
                    '<li style="margin-left: %dpx;"><a href="#%s">%s</a></li>',
                    $indent,
                    $item['anchor'],
                    htmlspecialchars($item['title'])
                );
            }
            $tocHtml .= '</ul></div>';
            
            // 替换 [toc] 标记
            $html = str_replace('[toc]', $tocHtml, $html);
        }
        
        // 水平分隔符（处理连续的短横线或星号）
        $html = preg_replace('/^[\-\*]{3,}$/m', '<hr>', $html);
        
        // 粗体和斜体
        $html = preg_replace('/\*\*(.*?)\*\*/', '<strong>$1</strong>', $html);
        $html = preg_replace('/\*(.*?)\*/', '<em>$1</em>', $html);
        
        // 列表
        $html = preg_replace('/^- (.*?)$/m', '<li>$1</li>', $html);
        $html = preg_replace('/<li>(.*?)<\/li>(\n<li>|$)/s', '<ul><li>$1</li>$2</ul>', $html);
        
        // 链接
        $html = preg_replace('/\[(.*?)\]\((.*?)\)/', '<a href="$2" target="_blank">$1</a>', $html);
        
        // 段落
        $html = preg_replace('/^(?!<h|<ul|<\/ul|<li|<\/li|<a|<img|<div)(.*?)$/m', '<p>$1</p>', $html);
        
        // 空行
        $html = preg_replace('/(<\/[^>]+>)\n\n/', '$1<br><br>', $html);
        
        return $html;
    }
    
    /**
     * 递归删除目录及其内容
     * 
     * @param string $dir 要删除的目录路径
     * @return bool 是否成功删除
     */
    public static function deleteDirectory($dir) {
        if (!file_exists($dir)) {
            return true;
        }
        
        if (!is_dir($dir)) {
            return unlink($dir);
        }
        
        foreach (scandir($dir) as $item) {
            if ($item == '.' || $item == '..') {
                continue;
            }
            
            if (!self::deleteDirectory($dir . DIRECTORY_SEPARATOR . $item)) {
                return false;
            }
        }
        
        return rmdir($dir);
    }
    
    /**
     * 检查AJAX请求的登录状态
     * 不进行重定向，而是返回JSON错误响应
     */
    public static function checkLoginAjax() {
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }
        
        if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => '未登录或会话已过期，请重新登录',
                'auth_required' => true
            ]);
            exit;
        }
    }
    
    /**
     * 清理文件名，移除非法字符
     * 
     * @param string $filename 原始文件名
     * @return string 清理后的文件名
     */
    public static function sanitizeFilename($filename) {
        // 移除非法字符
        $filename = preg_replace('/[^\w\s.-]/', '', $filename);
        // 替换空格为下划线
        $filename = str_replace(' ', '_', $filename);
        // 确保文件名不为空
        if (empty($filename)) {
            $filename = 'file_' . time();
        }
        return $filename;
    }
} 