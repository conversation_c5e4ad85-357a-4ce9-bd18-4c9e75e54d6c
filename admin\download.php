<?php
require_once 'config.php';
require_once 'includes/utils.php';

// 检查登录状态
Utils::checkLogin();

if (!isset($_GET['file']) || !isset($_GET['type'])) {
    die('参数错误');
}

$filename = $_GET['file'];
$type = $_GET['type'];

// 验证文件名安全性
if (!preg_match('/^[a-zA-Z0-9_\-\.]+$/', $filename)) {
    error_log("下载请求包含非法文件名: {$filename}");
    die('非法文件名');
}

// 根据类型确定目录
switch ($type) {
    case 'excel':
        $directory = EXCEL_DIR;
        // 根据文件扩展名确定MIME类型
        $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
        if ($extension === 'xlsx') {
            $mime_type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
        } elseif ($extension === 'csv') {
            $mime_type = 'text/csv';
        } elseif ($extension === 'json') {
            $mime_type = 'application/json';
        } elseif ($extension === 'zip') {
            $mime_type = 'application/zip';
        } else {
            $mime_type = 'application/octet-stream';
        }
        break;
    case 'image':
        $directory = IMAGES_DIR;
        $mime_type = 'image/jpeg';
        break;
    default:
        die('不支持的文件类型');
}

$filepath = $directory . $filename;

// 验证文件是否存在
if (!file_exists($filepath)) {
    error_log("请求下载的文件不存在: {$filepath}");
    die('文件不存在');
}

// 验证文件是否可读
if (!is_readable($filepath)) {
    error_log("文件不可读: {$filepath}");
    die('文件访问错误');
}

// 获取文件大小
$filesize = filesize($filepath);
if ($filesize === false) {
    error_log("无法获取文件大小: {$filepath}");
    die('文件读取错误');
}

// 设置响应头
header('Content-Description: File Transfer');
header('Content-Type: ' . $mime_type);
header('Content-Disposition: attachment; filename="' . $filename . '"');
header('Content-Length: ' . $filesize);
header('Cache-Control: must-revalidate');
header('Pragma: public');
header('Expires: 0');

// 清空输出缓冲
ob_clean();
flush();

// 读取并输出文件内容
if ($fp = fopen($filepath, 'rb')) {
    while (!feof($fp) && connection_status() == 0) {
        print(fread($fp, 8192));
        flush();
    }
    fclose($fp);
    
    // 记录下载活动
    Utils::logActivity('下载文件', "下载了文件: {$filename}");
} else {
    error_log("无法打开文件进行读取: {$filepath}");
    die('文件读取失败');
}