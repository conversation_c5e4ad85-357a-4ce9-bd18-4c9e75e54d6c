<?php
require_once 'config.php';
require_once 'includes/db.php';
require_once 'includes/utils.php';
require_once 'includes/stats_calculator.php';

// 验证API密钥
// 开发环境下允许跳过API密钥验证，注意：生产环境应移除此代码
$is_dev_mode = true; // 开发模式标志

if ($is_dev_mode) {
    // 开发模式下打印API密钥请求信息
    $headers = getallheaders();
    $api_key_header = isset($headers['X-API-Key']) ? $headers['X-API-Key'] : 'not set';
    $api_key_param = isset($_GET['api_key']) ? $_GET['api_key'] : 'not set';
    
    if (!Utils::validateApiKey() && $is_dev_mode) {
        // 开发模式下即使验证失败也继续执行
        error_log("API密钥验证失败，但处于开发模式，继续执行。Header: $api_key_header, Param: $api_key_param");
    }
} elseif (!Utils::validateApiKey()) {
    Utils::apiResponse('error', '无效的API密钥', null);
}

// 获取请求方法和路径
$method = $_SERVER['REQUEST_METHOD'];
$path = isset($_GET['path']) ? $_GET['path'] : '';

// 创建统计计算器实例
$stats = new StatsCalculator($db);

// API路由处理
switch ($path) {
    // 获取玩家列表
    case 'players':
        if ($method === 'GET') {
            $allStats = $stats->getAllPlayerStats();
            Utils::apiResponse('success', '获取玩家列表成功', $allStats);
        } else {
            Utils::apiResponse('error', '不支持的请求方法', null);
        }
        break;
    
    // 获取特定玩家详情
    case 'player':
        if ($method === 'GET') {
            if (!isset($_GET['nickname'])) {
                Utils::apiResponse('error', '缺少玩家昵称参数', null);
            }
            
            $nickname = Utils::sanitizeInput($_GET['nickname']);
            $playerStats = $stats->getPlayerStats($nickname);
            
            if ($playerStats) {
                Utils::apiResponse('success', '获取玩家详情成功', $playerStats);
            } else {
                Utils::apiResponse('error', '未找到指定玩家', null);
            }
        } else {
            Utils::apiResponse('error', '不支持的请求方法', null);
        }
        break;
    
    // 获取玩家详细数据（Impact, KPR, DPR）
    case 'player_details':
        if ($method === 'GET') {
            if (!isset($_GET['nickname'])) {
                Utils::apiResponse('error', '缺少玩家昵称参数', null);
            }

            $nickname = Utils::sanitizeInput($_GET['nickname']);
            $game_type = isset($_GET['game_type']) ? Utils::sanitizeInput($_GET['game_type']) : null;

            // 验证游戏类型
            $valid_types = ['暗杀', '死斗', '盟主'];
            if ($game_type && !in_array($game_type, $valid_types)) {
                Utils::apiResponse('error', '无效的游戏类型', null);
            }

            // 首先检查玩家是否存在
            $check_sql = "SELECT id, nickname, player_rank FROM players WHERE nickname = '{$nickname}'";
            $player_check = $db->getRow($check_sql);

            if (!$player_check) {
                Utils::apiResponse('error', '未找到指定玩家', null);
            }

            // 获取玩家基本数据（根据游戏类型筛选）
            if ($game_type) {
                // 如果指定了游戏类型，只查询该游戏类型的数据
                $sql = "SELECT
                          p.id as player_id,
                          p.nickname,
                          p.player_rank,
                          COALESCE(SUM(gr.kills), 0) as total_kills,
                          COALESCE(SUM(gr.deaths), 0) as total_deaths,
                          COALESCE(SUM(gr.wins), 0) as total_wins,
                          COALESCE(SUM(gr.losses), 0) as total_losses,
                          (CASE WHEN COALESCE(SUM(gr.deaths), 0) = 0 THEN COALESCE(SUM(gr.kills), 0) ELSE COALESCE(SUM(gr.kills), 0) / COALESCE(SUM(gr.deaths), 1) END) as kd,
                          MAX(gr.team) as team
                        FROM
                          players p
                        LEFT JOIN
                          game_records gr ON p.id = gr.player_id
                        LEFT JOIN
                          games gm ON gr.game_id = gm.id
                        WHERE
                          p.nickname = '{$nickname}' AND (gm.game_type = '{$game_type}' OR gm.game_type IS NULL)
                        GROUP BY
                          p.id, p.nickname, p.player_rank";
            } else {
                // 如果没有指定游戏类型，查询所有数据
                $sql = "SELECT
                          p.id as player_id,
                          p.nickname,
                          p.player_rank,
                          COALESCE(SUM(gr.kills), 0) as total_kills,
                          COALESCE(SUM(gr.deaths), 0) as total_deaths,
                          COALESCE(SUM(gr.wins), 0) as total_wins,
                          COALESCE(SUM(gr.losses), 0) as total_losses,
                          (CASE WHEN COALESCE(SUM(gr.deaths), 0) = 0 THEN COALESCE(SUM(gr.kills), 0) ELSE COALESCE(SUM(gr.kills), 0) / COALESCE(SUM(gr.deaths), 1) END) as kd,
                          MAX(gr.team) as team
                        FROM
                          players p
                        LEFT JOIN
                          game_records gr ON p.id = gr.player_id
                        WHERE
                          p.nickname = '{$nickname}'
                        GROUP BY
                          p.id, p.nickname, p.player_rank";
            }

            $player = $db->getRow($sql);

            if (!$player) {
                Utils::apiResponse('error', '查询玩家数据失败', null);
            }

            // 获取队伍的KD数据（根据游戏类型筛选）
            if ($game_type) {
                $team_sql = "SELECT
                                gr.team,
                                SUM(gr.kills) as team_kills,
                                SUM(gr.deaths) as team_deaths
                            FROM
                                game_records gr
                            INNER JOIN
                                games gm ON gr.game_id = gm.id
                            WHERE
                                gr.team IS NOT NULL AND gr.team != '' AND gm.game_type = '{$game_type}'
                            GROUP BY
                                gr.team";

                // 获取总体统计数据，用于计算平均KD（根据游戏类型筛选）
                $summary_sql = "SELECT
                                  SUM(gr.kills) as total_kills,
                                  SUM(gr.deaths) as total_deaths
                                FROM
                                  game_records gr
                                INNER JOIN
                                  games gm ON gr.game_id = gm.id
                                WHERE gm.game_type = '{$game_type}'";
            } else {
                $team_sql = "SELECT
                                gr.team,
                                SUM(gr.kills) as team_kills,
                                SUM(gr.deaths) as team_deaths
                            FROM
                                game_records gr
                            WHERE
                                gr.team IS NOT NULL AND gr.team != ''
                            GROUP BY
                                gr.team";

                // 获取总体统计数据，用于计算平均KD
                $summary_sql = "SELECT
                                  SUM(gr.kills) as total_kills,
                                  SUM(gr.deaths) as total_deaths
                                FROM
                                  game_records gr";
            }

            $team_stats = $db->getRows($team_sql);
            $summary = $db->getRow($summary_sql);

            // 计算每个队伍的平均KD值
            $team_kd = [];
            if ($team_stats) {
                foreach ($team_stats as $stat) {
                    $team_kd[$stat['team']] = ($stat['team_deaths'] > 0) ? $stat['team_kills'] / $stat['team_deaths'] : $stat['team_kills'];
                }
            }

            // 计算Impact影响力 (KD值/场均KD值)
            $player_kd = $player['kd'] ?? 0;

            // 计算场均KD - 如果玩家有队伍信息，使用队伍的KD，否则使用总体平均KD
            $avg_kd = 0;
            if (!empty($player['team']) && isset($team_kd[$player['team']])) {
                $avg_kd = $team_kd[$player['team']];
            } else {
                $avg_kd = ($summary && $summary['total_deaths'] > 0) ? $summary['total_kills'] / $summary['total_deaths'] : 1;
            }

            // 计算Impact影响力
            $impact = ($avg_kd > 0) ? $player_kd / $avg_kd : $player_kd;

            // 计算KPR和DPR
            $total_games = $player['total_wins'] + $player['total_losses'];
            $kpr = ($total_games > 0) ? $player['total_kills'] / $total_games : 0;
            $dpr = ($total_games > 0) ? $player['total_deaths'] / $total_games : 0;

            // 计算胜率
            $win_rate = ($total_games > 0) ? ($player['total_wins'] / $total_games) * 100 : 0;

            // 获取玩家在其他游戏类型下的简要统计（如果当前查询指定了游戏类型）
            $other_game_types_summary = null;
            if ($game_type) {
                $other_types = array_diff(['暗杀', '死斗', '盟主'], [$game_type]);
                $other_stats = [];

                foreach ($other_types as $other_type) {
                    $other_sql = "SELECT
                                    COALESCE(SUM(gr.kills), 0) as kills,
                                    COALESCE(SUM(gr.deaths), 0) as deaths,
                                    COALESCE(SUM(gr.wins), 0) as wins,
                                    COALESCE(SUM(gr.losses), 0) as losses
                                  FROM
                                    players p
                                  LEFT JOIN
                                    game_records gr ON p.id = gr.player_id
                                  LEFT JOIN
                                    games gm ON gr.game_id = gm.id
                                  WHERE
                                    p.nickname = '{$nickname}' AND gm.game_type = '{$other_type}'";
                    $other_data = $db->getRow($other_sql);

                    if ($other_data && ($other_data['kills'] > 0 || $other_data['deaths'] > 0)) {
                        $other_kd = ($other_data['deaths'] > 0) ? $other_data['kills'] / $other_data['deaths'] : $other_data['kills'];
                        $other_stats[$other_type] = [
                            'kills' => (int)$other_data['kills'],
                            'deaths' => (int)$other_data['deaths'],
                            'wins' => (int)$other_data['wins'],
                            'losses' => (int)$other_data['losses'],
                            'kd' => round($other_kd, 2)
                        ];
                    }
                }

                if (!empty($other_stats)) {
                    $other_game_types_summary = $other_stats;
                }
            }

            // 准备返回数据
            $player_details = [
                'nickname' => $player['nickname'],
                'player_rank' => $player['player_rank'],
                'game_type' => $game_type,
                'total_kills' => (int)$player['total_kills'],
                'total_deaths' => (int)$player['total_deaths'],
                'total_wins' => (int)$player['total_wins'],
                'total_losses' => (int)$player['total_losses'],
                'total_games' => $total_games,
                'win_rate' => round($win_rate, 2),
                'kd' => round($player_kd, 2),
                'impact' => round($impact, 2),
                'kpr' => round($kpr, 2),
                'dpr' => round($dpr, 2),
                'team' => $player['team'],
                'avg_kd' => round($avg_kd, 2),
                'other_game_types' => $other_game_types_summary
            ];

            Utils::apiResponse('success', '获取玩家详细数据成功', $player_details);
        } else {
            Utils::apiResponse('error', '不支持的请求方法', null);
        }
        break;
    
    // 获取游戏场次列表
    case 'games':
        if ($method === 'GET') {
            $sql = "SELECT g.id, g.unique_id, g.excel_file, g.image_file, g.game_type, g.upload_time,
                           COUNT(DISTINCT gr.player_id) as player_count
                    FROM games g
                    LEFT JOIN game_records gr ON g.id = gr.game_id
                    GROUP BY g.id
                    ORDER BY g.upload_time DESC";
            
            $games = $db->getRows($sql);
            Utils::apiResponse('success', '获取游戏场次列表成功', $games);
        } else {
            Utils::apiResponse('error', '不支持的请求方法', null);
        }
        break;
    
    // 获取特定游戏场次详情
    case 'game':
        if ($method === 'GET') {
            if (!isset($_GET['id'])) {
                Utils::apiResponse('error', '缺少游戏ID参数', null);
            }
            
            $gameId = (int)$_GET['id'];
            
            // 获取游戏基本信息
            $sql = "SELECT * FROM games WHERE id = {$gameId}";
            $game = $db->getRow($sql);
            
            if (!$game) {
                Utils::apiResponse('error', '未找到指定游戏', null);
            }
            
            // 获取游戏记录
            $sql = "SELECT p.nickname, p.virtual_ip, p.player_rank, gr.kills, gr.deaths, gr.team, gr.wins, gr.losses
                    FROM game_records gr
                    JOIN players p ON gr.player_id = p.id
                    WHERE gr.game_id = {$gameId}
                    ORDER BY gr.kills DESC";
            
            $records = $db->getRows($sql);
            
            // 合并结果
            $result = [
                'game_info' => $game,
                'records' => $records
            ];
            
            Utils::apiResponse('success', '获取游戏详情成功', $result);
        } else {
            Utils::apiResponse('error', '不支持的请求方法', null);
        }
        break;
    
    // 获取军衔统计
    case 'ranks':
        if ($method === 'GET') {
            $rankStats = $stats->getStatsByRank();
            Utils::apiResponse('success', '获取军衔统计成功', $rankStats);
        } else {
            Utils::apiResponse('error', '不支持的请求方法', null);
        }
        break;
    
    // 获取公告列表
    case 'announcements':
        if ($method === 'GET') {
            $sql = "SELECT id, title, created_at as create_time, updated_at as update_time FROM announcements WHERE status = 1 ORDER BY updated_at DESC";
            $announcements = $db->getRows($sql);
            Utils::apiResponse('success', '获取公告列表成功', $announcements);
        } else {
            Utils::apiResponse('error', '不支持的请求方法', null);
        }
        break;
    
    // 获取特定公告详情
    case 'announcement':
        if ($method === 'GET') {
            if (!isset($_GET['id'])) {
                Utils::apiResponse('error', '缺少公告ID参数', null);
            }
            
            $announcementId = (int)$_GET['id'];
            $sql = "SELECT id, title, content, status, priority, valid_from, valid_to, created_at as create_time, updated_at as update_time FROM announcements WHERE id = {$announcementId} AND status = 1";
            $announcement = $db->getRow($sql);
            
            if ($announcement) {
                Utils::apiResponse('success', '获取公告详情成功', $announcement);
            } else {
                Utils::apiResponse('error', '未找到指定公告', null);
            }
        } else {
            Utils::apiResponse('error', '不支持的请求方法', null);
        }
        break;
    
    // 获取文章列表
    case 'articles':
        if ($method === 'GET') {
            $sql = "SELECT a.id, a.title, a.category_id, c.name as category_name, a.create_time, a.update_time 
                   FROM articles a
                   LEFT JOIN article_categories c ON a.category_id = c.id
                   WHERE a.published = 1 AND (c.is_hidden = 0 OR c.is_hidden IS NULL)
                   ORDER BY a.update_time DESC";
            $articles = $db->getRows($sql);
            Utils::apiResponse('success', '获取文章列表成功', $articles);
        } else {
            Utils::apiResponse('error', '不支持的请求方法', null);
        }
        break;
    
    // 获取特定文章详情
    case 'article':
        if ($method === 'GET') {
            if (!isset($_GET['id'])) {
                Utils::apiResponse('error', '缺少文章ID参数', null);
            }
            
            $articleId = (int)$_GET['id'];
            $sql = "SELECT a.*, c.name as category_name, c.is_hidden as category_hidden
                   FROM articles a
                   LEFT JOIN article_categories c ON a.category_id = c.id
                   WHERE a.id = {$articleId} AND a.published = 1";
            
            $article = $db->getRow($sql);
            
            if (!$article) {
                Utils::apiResponse('error', '文章不存在或未发布', null);
            }
            
            // 检查文章所属分类是否隐藏
            if ($article['category_hidden']) {
                Utils::apiResponse('error', '文章所属分类已隐藏', null);
            }
            
            // 移除敏感字段
            unset($article['category_hidden']);
            
            Utils::apiResponse('success', '获取文章详情成功', $article);
        } else {
            Utils::apiResponse('error', '不支持的请求方法', null);
        }
        break;
    
    // 获取文章分类列表
    case 'article_categories':
        if ($method === 'GET') {
            $sql = "SELECT id, name, description, is_default FROM article_categories WHERE is_hidden = 0 ORDER BY is_default DESC, name ASC";
            $categories = $db->getRows($sql);
            Utils::apiResponse('success', '获取文章分类列表成功', $categories);
        } else {
            Utils::apiResponse('error', '不支持的请求方法', null);
        }
        break;
    
    // 根据分类获取文章列表
    case 'articles_by_category':
        if ($method === 'GET') {
            if (!isset($_GET['category_id'])) {
                Utils::apiResponse('error', '缺少分类ID参数', null);
            }
            
            $categoryId = (int)$_GET['category_id'];
            
            // 首先检查分类是否存在且未隐藏
            $category_check_sql = "SELECT id, is_hidden FROM article_categories WHERE id = {$categoryId}";
            $category = $db->getRow($category_check_sql);
            
            if (!$category) {
                Utils::apiResponse('error', '分类不存在', null);
            }
            
            if ($category['is_hidden']) {
                Utils::apiResponse('error', '分类已隐藏', []);
            }
            
            $sql = "SELECT a.id, a.title, a.category_id, c.name as category_name, a.create_time, a.update_time 
                   FROM articles a
                   LEFT JOIN article_categories c ON a.category_id = c.id
                   WHERE a.published = 1 AND (c.is_hidden = 0 OR c.is_hidden IS NULL)" . 
                   ($categoryId > 0 ? " AND a.category_id = {$categoryId}" : "") . 
                   " ORDER BY a.update_time DESC";
            $articles = $db->getRows($sql);
            Utils::apiResponse('success', '获取分类文章列表成功', $articles);
        } else {
            Utils::apiResponse('error', '不支持的请求方法', null);
        }
        break;
    
    // 获取文章摘要
    case 'article_summary':
        if ($method === 'GET') {
            if (!isset($_GET['id'])) {
                Utils::apiResponse('error', '缺少文章ID参数', null);
            }

            $articleId = (int)$_GET['id'];
            $sql = "SELECT id, title, summary, category_id, create_time, update_time
                   FROM articles
                   WHERE id = {$articleId} AND published = 1";
            $article = $db->getRow($sql);

            if ($article) {
                Utils::apiResponse('success', '获取文章摘要成功', $article);
            } else {
                Utils::apiResponse('error', '未找到指定文章', null);
            }
        } else {
            Utils::apiResponse('error', '不支持的请求方法', null);
        }
        break;

    // 批量获取文章摘要
    case 'article_summaries_batch':
        if ($method === 'GET') {
            // 检查是否提供了文章ID参数
            if (!isset($_GET['ids'])) {
                Utils::apiResponse('error', '缺少文章ID参数', null);
            }

            $idsParam = Utils::sanitizeInput($_GET['ids']);

            // 解析文章ID列表，支持逗号分隔的字符串或JSON数组
            $articleIds = [];
            if (strpos($idsParam, '[') === 0) {
                // JSON数组格式
                $decodedIds = json_decode($idsParam, true);
                if (json_last_error() === JSON_ERROR_NONE && is_array($decodedIds)) {
                    $articleIds = array_map('intval', $decodedIds);
                } else {
                    Utils::apiResponse('error', '无效的JSON格式', null);
                }
            } else {
                // 逗号分隔的字符串格式
                $articleIds = array_map('intval', explode(',', $idsParam));
            }

            // 过滤掉无效的ID（小于等于0的值）并重新索引数组
            $articleIds = array_values(array_filter($articleIds, function($id) {
                return $id > 0;
            }));

            // 检查是否有有效的ID
            if (empty($articleIds)) {
                Utils::apiResponse('error', '没有提供有效的文章ID', null);
            }

            // 限制批量查询的数量，防止性能问题
            $maxBatchSize = 50;
            if (count($articleIds) > $maxBatchSize) {
                Utils::apiResponse('error', "批量查询数量不能超过{$maxBatchSize}个", null);
            }

            // 构建SQL查询，使用IN子句
            $idsString = implode(',', $articleIds);
            $sql = "SELECT a.id, a.title, a.summary, a.category_id, c.name as category_name, a.create_time, a.update_time
                   FROM articles a
                   LEFT JOIN article_categories c ON a.category_id = c.id
                   WHERE a.id IN ({$idsString}) AND a.published = 1
                   ORDER BY FIELD(a.id, {$idsString})";

            $articles = $db->getRows($sql);

            // 创建结果数组，包含成功和失败的信息
            $result = [
                'requested_ids' => $articleIds,
                'found_articles' => [],
                'not_found_ids' => [],
                'total_requested' => count($articleIds),
                'total_found' => 0
            ];

            // 处理找到的文章
            $foundIds = [];
            if ($articles) {
                foreach ($articles as $article) {
                    $result['found_articles'][] = $article;
                    $foundIds[] = (int)$article['id'];
                }
            }

            // 找出未找到的文章ID并重新索引数组
            $result['not_found_ids'] = array_values(array_diff($articleIds, $foundIds));
            $result['total_found'] = count($result['found_articles']);

            // 返回结果
            if ($result['total_found'] > 0) {
                $message = "批量获取文章摘要成功，找到{$result['total_found']}篇文章";
                if (!empty($result['not_found_ids'])) {
                    $message .= "，" . count($result['not_found_ids']) . "篇文章未找到或未发布";
                }
                Utils::apiResponse('success', $message, $result);
            } else {
                Utils::apiResponse('error', '未找到任何指定的文章或文章未发布', $result);
            }
        } else {
            Utils::apiResponse('error', '不支持的请求方法', null);
        }
        break;
    
    // 获取玩家游戏场次数量
    case 'player_game_count':
        if ($method === 'GET') {
            if (!isset($_GET['nickname'])) {
                Utils::apiResponse('error', '缺少玩家昵称参数', null);
            }
            
            $nickname = Utils::sanitizeInput($_GET['nickname']);
            $gameCount = $stats->getPlayerGameCount($nickname);
            
            if ($gameCount) {
                Utils::apiResponse('success', '获取包含该玩家的游戏场次数量成功', $gameCount);
            } else {
                Utils::apiResponse('error', '未找到指定玩家', null);
            }
        } else {
            Utils::apiResponse('error', '不支持的请求方法', null);
        }
        break;
    
    // 获取特定玩家的游戏场次列表
    case 'player_games':
        if ($method === 'GET') {
            if (!isset($_GET['nickname'])) {
                Utils::apiResponse('error', '缺少玩家昵称参数', null);
            }
            
            $nickname = Utils::sanitizeInput($_GET['nickname']);
            
            // 首先获取玩家ID
            $playerSql = "SELECT id FROM players WHERE nickname = '{$nickname}'";
            $player = $db->getRow($playerSql);
            
            if (!$player) {
                Utils::apiResponse('error', '未找到指定玩家', null);
            }
            
            $playerId = $player['id'];
            
            // 获取该玩家参与的所有游戏场次及详情
            $sql = "SELECT 
                      g.id, 
                      g.game_type, 
                      g.upload_time as game_time,
                      p.virtual_ip, 
                      gr.team, 
                      gr.kills, 
                      gr.deaths, 
                      gr.wins, 
                      gr.losses
                    FROM 
                      game_records gr
                    JOIN 
                      games g ON gr.game_id = g.id
                    JOIN
                      players p ON gr.player_id = p.id
                    WHERE 
                      gr.player_id = {$playerId}
                    ORDER BY 
                      g.upload_time DESC";
            
            $games = $db->getRows($sql);
            
            if ($games) {
                Utils::apiResponse('success', '获取玩家游戏场次列表成功', $games);
            } else {
                Utils::apiResponse('success', '该玩家暂无游戏记录', []);
            }
        } else {
            Utils::apiResponse('error', '不支持的请求方法', null);
        }
        break;
    
    // 搜索玩家
    case 'search_players':
        if ($method === 'GET') {
            if (!isset($_GET['keyword'])) {
                Utils::apiResponse('error', '缺少关键词参数', null);
            }
            
            $keyword = Utils::sanitizeInput($_GET['keyword']);
            
            // 使用LIKE进行模糊匹配
            $sql = "SELECT 
                      p.id, 
                      p.nickname, 
                      p.virtual_ip, 
                      p.player_rank,
                      SUM(g.kills) as total_kills,
                      SUM(g.deaths) as total_deaths,
                      (CASE WHEN SUM(g.deaths) = 0 THEN SUM(g.kills) ELSE SUM(g.kills) / SUM(g.deaths) END) as kd
                    FROM 
                      players p
                    LEFT JOIN 
                      game_records g ON p.id = g.player_id
                    WHERE 
                      p.nickname LIKE '%{$keyword}%' OR
                      p.virtual_ip LIKE '%{$keyword}%'
                    GROUP BY 
                      p.id, p.nickname, p.virtual_ip, p.player_rank
                    ORDER BY 
                      kd DESC";
            
            $players = $db->getRows($sql);
            
            if ($players) {
                Utils::apiResponse('success', '搜索玩家成功', $players);
            } else {
                Utils::apiResponse('success', '未找到匹配的玩家', []);
            }
        } else {
            Utils::apiResponse('error', '不支持的请求方法', null);
        }
        break;
    
    // 获取玩家胜率和KD统计
    case 'player_stats':
        if ($method === 'GET') {
            if (!isset($_GET['nickname'])) {
                Utils::apiResponse('error', '缺少玩家昵称参数', null);
            }
            
            $nickname = Utils::sanitizeInput($_GET['nickname']);
            
            $sql = "SELECT 
                      p.nickname,
                      SUM(g.wins) as total_wins,
                      SUM(g.losses) as total_losses,
                      SUM(g.kills) as total_kills,
                      SUM(g.deaths) as total_deaths,
                      (CASE 
                          WHEN (SUM(g.wins) + SUM(g.losses)) > 0 
                          THEN CONCAT(ROUND((SUM(g.wins) * 100.0) / (SUM(g.wins) + SUM(g.losses)), 2), '%')
                          ELSE '0%' 
                      END) as win_rate,
                      (CASE 
                          WHEN SUM(g.deaths) > 0 
                          THEN ROUND(SUM(g.kills) * 1.0 / SUM(g.deaths), 2)
                          ELSE SUM(g.kills)
                      END) as kd
                    FROM 
                      players p
                    LEFT JOIN 
                      game_records g ON p.id = g.player_id
                    WHERE 
                      p.nickname = '{$nickname}'
                    GROUP BY 
                      p.nickname";
            
            $stats = $db->getRow($sql);
            
            if ($stats) {
                Utils::apiResponse('success', '获取玩家统计数据成功', $stats);
            } else {
                Utils::apiResponse('error', '未找到指定玩家', null);
            }
        } else {
            Utils::apiResponse('error', '不支持的请求方法', null);
        }
        break;
    
    // 获取玩家封禁状态和类型
    case 'player_ban_status':
        if ($method === 'GET') {
            if (!isset($_GET['nickname'])) {
                Utils::apiResponse('error', '缺少玩家昵称参数', null);
            }
            
            $nickname = Utils::sanitizeInput($_GET['nickname']);
            
            // 查询玩家封禁状态和类型
            $sql = "SELECT id, nickname, is_banned, ban_type FROM players WHERE nickname = '" . $db->escape($nickname) . "'";
            $player = $db->getRow($sql);
            
            if (!$player) {
                Utils::apiResponse('error', '玩家不存在', null);
            }
            
            Utils::apiResponse('success', '获取玩家封禁状态成功', [
                'player_id' => $player['id'],
                'nickname' => $player['nickname'],
                'is_banned' => (bool)$player['is_banned'],
                'ban_type' => $player['ban_type']
            ]);
        } else {
            Utils::apiResponse('error', '不支持的请求方法', null);
        }
        break;
    
    // 批量获取玩家统计数据
    case 'player_stats_batch':
        if ($method === 'GET') {
            // 检查是否提供了玩家昵称参数
            if (!isset($_GET['nicknames'])) {
                Utils::apiResponse('error', '缺少玩家昵称参数', null);
            }

            $nicknamesParam = $_GET['nicknames']; // 不进行sanitizeInput处理，保留原始JSON格式
            
            // 调试信息
            error_log("接收到的nicknames参数: " . print_r($nicknamesParam, true));

            // 解析玩家昵称列表，支持逗号分隔的字符串或JSON数组
            $nicknames = [];
            if (strpos($nicknamesParam, '[') === 0) {
                // JSON数组格式
                $decodedNicknames = json_decode($nicknamesParam, true);
                error_log("JSON解析结果: " . print_r($decodedNicknames, true) . ", 错误码: " . json_last_error());
                
                if (json_last_error() === JSON_ERROR_NONE && is_array($decodedNicknames)) {
                    $nicknames = $decodedNicknames;
                } else {
                    $error_msg = 'JSON解析错误: ' . json_last_error_msg();
                    error_log($error_msg);
                    Utils::apiResponse('error', '无效的JSON格式: ' . $error_msg, null);
                }
            } else {
                // 逗号分隔的字符串格式
                $nicknames = explode(',', $nicknamesParam);
            }

            // 过滤掉空的昵称并重新索引数组
            $nicknames = array_values(array_filter($nicknames, function($nickname) {
                return !empty(trim($nickname));
            }));
            
            error_log("处理后的昵称数组: " . print_r($nicknames, true));

            // 检查是否有有效的昵称
            if (empty($nicknames)) {
                Utils::apiResponse('error', '没有提供有效的玩家昵称', null);
            }

            // 限制批量查询的数量，防止性能问题
            $maxBatchSize = 50;
            if (count($nicknames) > $maxBatchSize) {
                Utils::apiResponse('error', "批量查询数量不能超过{$maxBatchSize}个", null);
            }

            try {
                // 构建SQL查询，使用IN子句
                $escapedNicknames = array_map(function($nickname) use ($db) {
                    return "'" . $db->escape(trim($nickname)) . "'";
                }, $nicknames);
                
                $nicknamesString = implode(',', $escapedNicknames);
                error_log("SQL查询中的昵称字符串: " . $nicknamesString);
                
                $sql = "SELECT 
                          p.id as player_id,
                          p.nickname,
                          p.player_rank,
                          SUM(g.wins) as total_wins,
                          SUM(g.losses) as total_losses,
                          SUM(g.kills) as total_kills,
                          SUM(g.deaths) as total_deaths,
                          (CASE 
                              WHEN (SUM(g.wins) + SUM(g.losses)) > 0 
                              THEN CONCAT(ROUND((SUM(g.wins) * 100.0) / (SUM(g.wins) + SUM(g.losses)), 2), '%')
                              ELSE '0%' 
                          END) as win_rate,
                          (CASE 
                              WHEN SUM(g.deaths) > 0 
                              THEN ROUND(SUM(g.kills) * 1.0 / SUM(g.deaths), 2)
                              ELSE SUM(g.kills)
                          END) as kd
                        FROM 
                          players p
                        LEFT JOIN 
                          game_records g ON p.id = g.player_id
                        WHERE 
                          p.nickname IN ({$nicknamesString})
                        GROUP BY 
                          p.id, p.nickname, p.player_rank";
                
                $players_stats = $db->getRows($sql);
                error_log("查询结果: " . print_r($players_stats, true));
                
                // 创建结果数组，包含成功和失败的信息
                $result = [
                    'requested_nicknames' => $nicknames,
                    'found_players' => [],
                    'not_found_nicknames' => [],
                    'total_requested' => count($nicknames),
                    'total_found' => 0
                ];
                
                // 处理找到的玩家
                $foundNicknames = [];
                if ($players_stats) {
                    foreach ($players_stats as $stats) {
                        $result['found_players'][] = $stats;
                        $foundNicknames[] = $stats['nickname'];
                    }
                }
                
                // 找出未找到的玩家昵称
                $result['not_found_nicknames'] = array_values(array_diff($nicknames, $foundNicknames));
                $result['total_found'] = count($result['found_players']);
                
                // 返回结果
                if ($result['total_found'] > 0) {
                    $message = "批量获取玩家统计数据成功，找到{$result['total_found']}名玩家";
                    if (!empty($result['not_found_nicknames'])) {
                        $message .= "，" . count($result['not_found_nicknames']) . "名玩家未找到";
                    }
                    Utils::apiResponse('success', $message, $result);
                } else {
                    Utils::apiResponse('error', '未找到任何指定的玩家', $result);
                }
            } catch (Exception $e) {
                error_log("批量获取玩家统计数据出错: " . $e->getMessage());
                Utils::apiResponse('error', '处理请求时发生错误: ' . $e->getMessage(), null);
            }
        } else {
            Utils::apiResponse('error', '不支持的请求方法', null);
        }
        break;
    
    // 获取游戏类型数据
    case 'game_type_status':
        if ($method === 'GET') {
            $game_type = isset($_GET['type']) ? Utils::sanitizeInput($_GET['type']) : '暗杀';
            $player_id = isset($_GET['player_id']) ? (int)$_GET['player_id'] : null;
            $search = isset($_GET['search']) ? Utils::sanitizeInput($_GET['search']) : '';
            $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
            $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 20;
            $sort_by = isset($_GET['sort']) ? Utils::sanitizeInput($_GET['sort']) : 'kills';
            $order = isset($_GET['order']) ? Utils::sanitizeInput($_GET['order']) : 'desc';

            // 验证游戏类型
            $valid_types = ['暗杀', '死斗', '盟主'];
            if (!in_array($game_type, $valid_types)) {
                Utils::apiResponse('error', '无效的游戏类型', null);
            }

            // 计算偏移量
            $offset = ($page - 1) * $limit;

            // 构建查询条件
            $where = "WHERE gm.game_type = '{$game_type}'";
            if (!empty($search)) {
                $where .= " AND (p.nickname LIKE '%{$search}%' OR p.player_rank LIKE '%{$search}%')";
            }
            if ($player_id) {
                $where .= " AND p.id = {$player_id}";
            }

            // 构建排序条件
            $order_sql = '';
            switch ($sort_by) {
                case 'nickname':
                    $order_sql = "ORDER BY p.nickname {$order}";
                    break;
                case 'kills':
                    $order_sql = "ORDER BY total_kills {$order}";
                    break;
                case 'deaths':
                    $order_sql = "ORDER BY total_deaths {$order}";
                    break;
                case 'kd':
                    $order_sql = "ORDER BY (CASE WHEN total_deaths = 0 THEN total_kills ELSE total_kills / total_deaths END) {$order}";
                    break;
                case 'wins':
                    $order_sql = "ORDER BY total_wins {$order}";
                    break;
                case 'losses':
                    $order_sql = "ORDER BY total_losses {$order}";
                    break;
                case 'rank':
                    $order_sql = "ORDER BY p.player_rank {$order}";
                    break;
                default:
                    $order_sql = "ORDER BY total_kills {$order}";
                    break;
            }

            // 获取总记录数
            $count_sql = "SELECT COUNT(DISTINCT p.id) as total 
                        FROM players p
                        LEFT JOIN game_records gr ON p.id = gr.player_id
                        LEFT JOIN games gm ON gr.game_id = gm.id
                        {$where}";
            $result = $db->getRow($count_sql);
            $total_records = $result['total'];
            $total_pages = ceil($total_records / $limit);

            // 构建SQL查询
            $sql = "SELECT 
                        p.id as player_id,
                        p.nickname,
                        p.player_rank,
                        p.is_banned,
                        SUM(gr.kills) as total_kills,
                        SUM(gr.deaths) as total_deaths,
                        SUM(gr.wins) as total_wins,
                        SUM(gr.losses) as total_losses,
                        (CASE WHEN SUM(gr.deaths) = 0 THEN SUM(gr.kills) ELSE SUM(gr.kills) / SUM(gr.deaths) END) as kd,
                        (CASE WHEN (SUM(gr.wins) + SUM(gr.losses)) = 0 THEN 0 ELSE (SUM(gr.wins) / (SUM(gr.wins) + SUM(gr.losses))) * 100 END) as win_rate
                    FROM 
                        players p
                    LEFT JOIN 
                        game_records gr ON p.id = gr.player_id
                    LEFT JOIN
                        games gm ON gr.game_id = gm.id
                    {$where}
                    GROUP BY 
                        p.id, p.nickname, p.player_rank, p.is_banned
                    {$order_sql}
                    LIMIT {$offset}, {$limit}";

            $players_data = $db->getRows($sql);

            // 计算KPR和DPR
            foreach ($players_data as &$player) {
                $total_games = $player['total_wins'] + $player['total_losses'];
                $player['kpr'] = ($total_games > 0) ? $player['total_kills'] / $total_games : 0;
                $player['dpr'] = ($total_games > 0) ? $player['total_deaths'] / $total_games : 0;
                
                // 格式化数值
                $player['kd'] = number_format($player['kd'], 2);
                $player['win_rate'] = number_format($player['win_rate'], 2);
                $player['kpr'] = number_format($player['kpr'], 2);
                $player['dpr'] = number_format($player['dpr'], 2);
            }

            // 构建响应
            Utils::apiResponse('success', '获取' . $game_type . '模式数据成功', [
                'game_type' => $game_type,
                'total_records' => $total_records,
                'total_pages' => $total_pages,
                'current_page' => $page,
                'players' => $players_data
            ]);
        } else {
            Utils::apiResponse('error', '不支持的请求方法', null);
        }
        break;
    
    // 获取玩家在游戏类型下的详细数据
    case 'game_type_player_details':
        if ($method === 'GET') {
            $player_id = isset($_GET['player_id']) ? (int)$_GET['player_id'] : null;
            $nickname = isset($_GET['nickname']) ? Utils::sanitizeInput($_GET['nickname']) : null;
            $game_type = isset($_GET['type']) ? Utils::sanitizeInput($_GET['type']) : null;

            // 验证参数
            if (!$player_id && !$nickname) {
                Utils::apiResponse('error', '缺少玩家ID或昵称参数', null);
            }

            // 构建查询条件
            $where = '';
            if ($player_id) {
                $where = "WHERE p.id = {$player_id}";
            } else if ($nickname) {
                $where = "WHERE p.nickname = '{$nickname}'";
            }

            // 获取玩家基本信息
            $player_sql = "SELECT p.id, p.nickname, p.player_rank, p.is_banned, p.virtual_ip FROM players p {$where}";
            $player_info = $db->getRow($player_sql);

            if (!$player_info) {
                Utils::apiResponse('error', '未找到该玩家', null);
            }

            // 更新where条件，使用找到的player_id
            $player_id = $player_info['id'];
            $where = "WHERE p.id = {$player_id}";

            // 添加游戏类型筛选
            $game_type_where = '';
            if ($game_type) {
                $game_type_where = " AND gm.game_type = '{$game_type}'";
            }

            // 获取玩家在各游戏类型下的战绩
            $stats_sql = "SELECT 
                            gm.game_type,
                            SUM(gr.kills) as total_kills,
                            SUM(gr.deaths) as total_deaths,
                            SUM(gr.wins) as total_wins,
                            SUM(gr.losses) as total_losses,
                            (CASE WHEN SUM(gr.deaths) = 0 THEN SUM(gr.kills) ELSE SUM(gr.kills) / SUM(gr.deaths) END) as kd,
                            (CASE WHEN (SUM(gr.wins) + SUM(gr.losses)) = 0 THEN 0 ELSE (SUM(gr.wins) / (SUM(gr.wins) + SUM(gr.losses))) * 100 END) as win_rate,
                            COUNT(DISTINCT gr.game_id) as game_count
                        FROM 
                            players p
                        LEFT JOIN 
                            game_records gr ON p.id = gr.player_id
                        LEFT JOIN
                            games gm ON gr.game_id = gm.id
                        {$where} {$game_type_where}
                        GROUP BY 
                            gm.game_type";

            $stats = $db->getRows($stats_sql);

            // 格式化数据
            foreach ($stats as &$stat) {
                $stat['kd'] = number_format($stat['kd'], 2);
                $stat['win_rate'] = number_format($stat['win_rate'], 2) . '%';
                
                // 计算KPR和DPR - 修正计算方式
                $total_matches = $stat['total_wins'] + $stat['total_losses'];
                $stat['kpr'] = ($total_matches > 0) ? 
                    number_format(($stat['total_kills'] / $total_matches) * 100, 2) . '%' : 
                    '0.00%';
                $stat['dpr'] = ($total_matches > 0) ? 
                    number_format(($stat['total_deaths'] / $total_matches) * 100, 2) . '%' : 
                    '0.00%';
            }

            // 获取玩家最近10场比赛记录
            $recent_games_sql = "SELECT 
                                    g.id as game_id,
                                    g.game_type,
                                    g.upload_time,
                                    gr.kills,
                                    gr.deaths,
                                    gr.wins,
                                    gr.losses,
                                    gr.team
                                FROM 
                                    game_records gr
                                JOIN 
                                    games g ON gr.game_id = g.id
                                WHERE 
                                    gr.player_id = {$player_id}
                                " . ($game_type ? "AND g.game_type = '{$game_type}'" : "") . "
                                ORDER BY 
                                    g.upload_time DESC
                                LIMIT 10";

            $recent_games = $db->getRows($recent_games_sql);

            // 获取玩家总体数据汇总
            $summary_sql = "SELECT 
                                SUM(gr.kills) as total_kills,
                                SUM(gr.deaths) as total_deaths,
                                SUM(gr.wins) as total_wins,
                                SUM(gr.losses) as total_losses,
                                COUNT(DISTINCT gr.game_id) as total_games, /* 统计玩家参与的总游戏场次数(去重) */
                                (SUM(gr.wins) + SUM(gr.losses)) as total_matches, /* 用于计算KPR和DPR的总场次 */
                                (CASE WHEN SUM(gr.deaths) = 0 THEN SUM(gr.kills) ELSE SUM(gr.kills) / SUM(gr.deaths) END) as overall_kd,
                                (CASE WHEN (SUM(gr.wins) + SUM(gr.losses)) = 0 THEN 0 ELSE (SUM(gr.wins) / (SUM(gr.wins) + SUM(gr.losses))) * 100 END) as overall_win_rate
                            FROM 
                                game_records gr
                            JOIN
                                games g ON gr.game_id = g.id
                            WHERE 
                                gr.player_id = {$player_id}
                            " . ($game_type ? "AND g.game_type = '{$game_type}'" : "");

            $summary = $db->getRow($summary_sql);

            if ($summary) {
                $summary['overall_kd'] = number_format($summary['overall_kd'], 2);
                $summary['overall_win_rate'] = number_format($summary['overall_win_rate'], 2) . '%';
                
                // 计算总体KPR和DPR - 使用total_matches而不是total_games
                $total_matches = $summary['total_matches']; // 使用新字段
                $summary['overall_kpr'] = ($total_matches > 0) ? 
                    number_format(($summary['total_kills'] / $total_matches) * 100, 2) . '%' : 
                    '0.00%';
                $summary['overall_dpr'] = ($total_matches > 0) ? 
                    number_format(($summary['total_deaths'] / $total_matches) * 100, 2) . '%' : 
                    '0.00%';
                
                // 为了向后兼容，保留total_games字段
                unset($summary['total_matches']); // 移除临时字段，保持API返回格式不变
            }

            // 构建响应
            Utils::apiResponse('success', '获取玩家详细数据成功', [
                'player_info' => $player_info,
                'game_stats' => $stats,
                'recent_games' => $recent_games,
                'summary' => $summary
            ]);
        } else {
            Utils::apiResponse('error', '不支持的请求方法', null);
        }
        break;
    
    // 获取游戏类型汇总统计
    case 'game_type_summary':
        if ($method === 'GET') {
            // 获取游戏类型统计数据
            $sql = "SELECT 
                        gm.game_type,
                        COUNT(DISTINCT gm.id) as game_count,
                        COUNT(DISTINCT gr.player_id) as player_count,
                        SUM(gr.kills) as total_kills,
                        SUM(gr.deaths) as total_deaths,
                        SUM(gr.wins) as total_wins,
                        SUM(gr.losses) as total_losses
                    FROM 
                        games gm
                    LEFT JOIN 
                        game_records gr ON gm.id = gr.game_id
                    WHERE 
                        gm.game_type IS NOT NULL AND gm.game_type != ''
                    GROUP BY 
                        gm.game_type";

            $stats = $db->getRows($sql);

            // 计算KD和胜率
            foreach ($stats as &$type) {
                $type['kd'] = ($type['total_deaths'] > 0) ? 
                            number_format($type['total_kills'] / $type['total_deaths'], 2) : 
                            number_format($type['total_kills'], 2);
                            
                $total_games = $type['total_wins'] + $type['total_losses'];
                $type['win_rate'] = ($total_games > 0) ? 
                                    number_format(($type['total_wins'] / $total_games) * 100, 2) . '%' : 
                                    '0.00%';
                                    
                // 计算平均每场击杀和死亡
                $type['avg_kills_per_game'] = ($type['game_count'] > 0) ?
                                            number_format($type['total_kills'] / $type['game_count'], 2) :
                                            '0.00';
                                            
                $type['avg_deaths_per_game'] = ($type['game_count'] > 0) ?
                                            number_format($type['total_deaths'] / $type['game_count'], 2) :
                                            '0.00';
                                            
                // 计算平均每名玩家击杀和死亡
                $type['avg_kills_per_player'] = ($type['player_count'] > 0) ?
                                                number_format($type['total_kills'] / $type['player_count'], 2) :
                                                '0.00';
                                                
                $type['avg_deaths_per_player'] = ($type['player_count'] > 0) ?
                                                number_format($type['total_deaths'] / $type['player_count'], 2) :
                                                '0.00';
            }

            // 获取总体汇总数据
            $summary_sql = "SELECT 
                                COUNT(DISTINCT gm.id) as total_games,
                                COUNT(DISTINCT gr.player_id) as total_players,
                                SUM(gr.kills) as total_kills,
                                SUM(gr.deaths) as total_deaths,
                                SUM(gr.wins) as total_wins,
                                SUM(gr.losses) as total_losses
                            FROM 
                                games gm
                            LEFT JOIN 
                                game_records gr ON gm.id = gr.game_id";

            $summary = $db->getRow($summary_sql);

            if ($summary) {
                $summary['kd'] = ($summary['total_deaths'] > 0) ? 
                                number_format($summary['total_kills'] / $summary['total_deaths'], 2) : 
                                number_format($summary['total_kills'], 2);
                                
                $total_games = $summary['total_wins'] + $summary['total_losses'];
                $summary['win_rate'] = ($total_games > 0) ? 
                                    number_format(($summary['total_wins'] / $total_games) * 100, 2) . '%' : 
                                    '0.00%';
                                    
                // 计算平均每场击杀和死亡
                $summary['avg_kills_per_game'] = ($summary['total_games'] > 0) ?
                                                number_format($summary['total_kills'] / $summary['total_games'], 2) :
                                                '0.00';
                                                
                $summary['avg_deaths_per_game'] = ($summary['total_games'] > 0) ?
                                                number_format($summary['total_deaths'] / $summary['total_games'], 2) :
                                                '0.00';
                                                
                // 计算平均每名玩家击杀和死亡
                $summary['avg_kills_per_player'] = ($summary['total_players'] > 0) ?
                                                number_format($summary['total_kills'] / $summary['total_players'], 2) :
                                                '0.00';
                                                
                $summary['avg_deaths_per_player'] = ($summary['total_players'] > 0) ?
                                                number_format($summary['total_deaths'] / $summary['total_players'], 2) :
                                                '0.00';
            }

            // 构建响应
            Utils::apiResponse('success', '获取游戏类型汇总统计成功', [
                'game_types' => $stats,
                'summary' => $summary
            ]);
        } else {
            Utils::apiResponse('error', '不支持的请求方法', null);
        }
        break;
    
    // 获取玩家游戏图片
    case 'player_game_images':
        if ($method === 'GET') {
            if (!isset($_GET['nickname'])) {
                Utils::apiResponse('error', '缺少玩家昵称参数', null);
            }
            
            $nickname = Utils::sanitizeInput($_GET['nickname']);
            
            // 获取玩家ID
            $player_sql = "SELECT id FROM players WHERE nickname = '{$nickname}'";
            $player = $db->getRow($player_sql);
            
            if (!$player) {
                Utils::apiResponse('error', '未找到该玩家', null);
            }
            
            $player_id = $player['id'];
            
            // 获取该玩家参与的所有游戏场次及其图片
            $sql = "SELECT DISTINCT 
                        g.id as game_id,
                        g.game_type,
                        g.image_file,
                        g.upload_time as game_time,
                        gr.team,
                        gr.kills,
                        gr.deaths,
                        gr.wins,
                        gr.losses
                    FROM 
                        game_records gr
                    JOIN 
                        games g ON gr.game_id = g.id
                    WHERE 
                        gr.player_id = {$player_id}
                        AND g.image_file IS NOT NULL 
                        AND g.image_file != ''
                    ORDER BY 
                        g.upload_time DESC";
            
            $games = $db->getRows($sql);
            
            if (!$games) {
                Utils::apiResponse('success', '该玩家暂无游戏图片记录', []);
            }
            
            // 处理图片路径，添加完整URL
            $base_url = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://{$_SERVER['HTTP_HOST']}";
            foreach ($games as &$game) {
                if ($game['image_file']) {
                       $game['image_url'] =  $base_url . '/BDLX/uploads/images/' . $game['image_file'];
		  //   $game['image_url'] =  'https://img1.lxbl.online/images/' . $game['image_file'];
                } else {
                    $game['image_url'] = null;
                }
            }
            
            Utils::apiResponse('success', '获取玩家游戏图片记录成功', [
                'player_nickname' => $nickname,
                'total_images' => count($games),
                'games' => $games
            ]);
        } else {
            Utils::apiResponse('error', '不支持的请求方法', null);
        }
        break;
    
    // 获取最新规则通知
    case 'latest_rule':
        if ($method === 'GET') {
            $sql = "SELECT id, title, content, status, created_at, updated_at 
                   FROM rules 
                   WHERE status = 1 
                   ORDER BY created_at DESC 
                   LIMIT 1";
            
            $rule = $db->getRow($sql);
            
            if ($rule) {
                // 格式化日期
                $rule['created_at'] = date('Y-m-d H:i:s', strtotime($rule['created_at']));
                $rule['updated_at'] = date('Y-m-d H:i:s', strtotime($rule['updated_at']));
                
                Utils::apiResponse('success', '获取最新规则通知成功', $rule);
            } else {
                Utils::apiResponse('success', '暂无规则通知', null);
            }
        } else {
            Utils::apiResponse('error', '不支持的请求方法', null);
        }
        break;
    
    // 获取随机幸运帖
    case 'luckpost':
        if ($method === 'GET') {
            $jsonFile = __DIR__ . '/luckpost.json';
            $jsonData = file_get_contents($jsonFile);
            $data = json_decode($jsonData, true);

            if (!$data || !is_array($data)) {
                Utils::apiResponse('error', '数据读取失败', null);
            }

            $randIndex = array_rand($data);
            $luck = $data[$randIndex]['xst'];

            Utils::apiResponse('success', '获取幸运帖成功', [
                'xst' => $luck
            ]);
        } else {
            Utils::apiResponse('error', '不支持的请求方法', null);
        }
        break;
    
    // API信息
    case '':
    case 'info':
        Utils::apiResponse('success', 'API服务运行正常', [
            'version' => '1.0.0',
            'endpoints' => [
                '/api.php?path=players' => '获取所有玩家列表',
                '/api.php?path=player&nickname=玩家昵称' => '获取特定玩家详情',
                '/api.php?path=player_details&nickname=玩家昵称' => '获取玩家详细数据（Impact影响力、KPR、DPR）',
                '/api.php?path=games' => '获取所有游戏场次列表',
                '/api.php?path=game&id=游戏ID' => '获取特定游戏场次详情',
                '/api.php?path=ranks' => '获取军衔统计数据',
                '/api.php?path=announcements' => '获取公告列表',
                '/api.php?path=announcement&id=公告ID' => '获取特定公告详情',
                '/api.php?path=articles' => '获取文章列表',
                '/api.php?path=article&id=文章ID' => '获取特定文章详情',
                '/api.php?path=article_categories' => '获取文章分类列表（仅显示未隐藏的分类）',
                '/api.php?path=articles_by_category&category_id=分类ID' => '获取指定分类的文章列表',
                '/api.php?path=article_summary&id=文章ID' => '获取文章摘要',
                '/api.php?path=article_summaries_batch&ids=文章ID列表' => '批量获取文章摘要（支持逗号分隔或JSON数组格式）',
                '/api.php?path=player_game_count&nickname=玩家昵称' => '获取包含该玩家的游戏场次数量',
                '/api.php?path=player_games&nickname=玩家昵称' => '获取特定玩家的游戏场次列表',
                '/api.php?path=player_game_images&nickname=玩家昵称' => '获取特定玩家的游戏场次图片',
                '/api.php?path=search_players&keyword=关键词' => '搜索玩家',
                '/api.php?path=player_stats&nickname=玩家昵称' => '获取玩家胜率和KD统计',
                '/api.php?path=player_ban_status&nickname=玩家昵称' => '获取玩家封禁状态',
                '/api.php?path=player_stats_batch&nicknames=玩家昵称列表' => '批量获取玩家统计数据（支持逗号分隔或JSON数组格式）',
                '/api.php?path=game_type_status&type=游戏类型' => '获取特定游戏类型的玩家数据',
                '/api.php?path=game_type_player_details&nickname=玩家昵称&type=游戏类型' => '获取玩家在特定游戏类型下的详细数据',
                '/api.php?path=game_type_summary' => '获取所有游戏类型的汇总统计数据',
                '/api.php?path=latest_rule' => '获取最新规则通知',
                '/api.php?path=luckpost' => '获取随机幸运帖'
            ]
        ]);
        break;
    
    // 获取玩家在不同游戏类型下的统计数据
    case 'player_stats_by_game_type':
        if ($method === 'GET') {
            if (!isset($_GET['nickname'])) {
                Utils::apiResponse('error', '缺少玩家昵称参数', null);
            }

            $nickname = Utils::sanitizeInput($_GET['nickname']);

            // 首先检查玩家是否存在
            $check_sql = "SELECT id, nickname, player_rank FROM players WHERE nickname = '{$nickname}'";
            $player_check = $db->getRow($check_sql);

            if (!$player_check) {
                Utils::apiResponse('error', '未找到指定玩家', null);
            }

            // 获取所有游戏类型
            $game_types = ['暗杀', '死斗', '盟主'];
            $stats_by_type = [];

            foreach ($game_types as $game_type) {
                // 获取该游戏类型下的玩家数据
                $sql = "SELECT
                          p.id as player_id,
                          p.nickname,
                          p.player_rank,
                          COALESCE(SUM(gr.kills), 0) as total_kills,
                          COALESCE(SUM(gr.deaths), 0) as total_deaths,
                          COALESCE(SUM(gr.wins), 0) as total_wins,
                          COALESCE(SUM(gr.losses), 0) as total_losses,
                          (CASE WHEN COALESCE(SUM(gr.deaths), 0) = 0 THEN COALESCE(SUM(gr.kills), 0) ELSE COALESCE(SUM(gr.kills), 0) / COALESCE(SUM(gr.deaths), 1) END) as kd,
                          MAX(gr.team) as team
                        FROM
                          players p
                        LEFT JOIN
                          game_records gr ON p.id = gr.player_id
                        LEFT JOIN
                          games gm ON gr.game_id = gm.id
                        WHERE
                          p.nickname = '{$nickname}' AND (gm.game_type = '{$game_type}' OR gm.game_type IS NULL)
                        GROUP BY
                          p.id, p.nickname, p.player_rank";

                $player_data = $db->getRow($sql);

                if ($player_data && ($player_data['total_kills'] > 0 || $player_data['total_deaths'] > 0)) {
                    // 获取该游戏类型下的队伍统计
                    $team_sql = "SELECT
                                    gr.team,
                                    SUM(gr.kills) as team_kills,
                                    SUM(gr.deaths) as team_deaths
                                FROM
                                    game_records gr
                                INNER JOIN
                                    games gm ON gr.game_id = gm.id
                                WHERE
                                    gr.team IS NOT NULL AND gr.team != '' AND gm.game_type = '{$game_type}'
                                GROUP BY
                                    gr.team";
                    $team_stats = $db->getRows($team_sql);

                    // 获取该游戏类型下的总体统计
                    $summary_sql = "SELECT
                                      SUM(gr.kills) as total_kills,
                                      SUM(gr.deaths) as total_deaths
                                    FROM
                                      game_records gr
                                    INNER JOIN
                                      games gm ON gr.game_id = gm.id
                                    WHERE gm.game_type = '{$game_type}'";
                    $summary = $db->getRow($summary_sql);

                    // 计算队伍平均KD
                    $team_kd = [];
                    if ($team_stats) {
                        foreach ($team_stats as $stat) {
                            $team_kd[$stat['team']] = ($stat['team_deaths'] > 0) ? $stat['team_kills'] / $stat['team_deaths'] : $stat['team_kills'];
                        }
                    }

                    // 计算平均KD
                    $player_kd = $player_data['kd'] ?? 0;
                    $avg_kd = 0;
                    if (!empty($player_data['team']) && isset($team_kd[$player_data['team']])) {
                        $avg_kd = $team_kd[$player_data['team']];
                    } else {
                        $avg_kd = ($summary && $summary['total_deaths'] > 0) ? $summary['total_kills'] / $summary['total_deaths'] : 1;
                    }

                    // 计算Impact、KPR、DPR
                    $impact = ($avg_kd > 0) ? $player_kd / $avg_kd : $player_kd;
                    $total_games = $player_data['total_wins'] + $player_data['total_losses'];
                    $kpr = ($total_games > 0) ? $player_data['total_kills'] / $total_games : 0;
                    $dpr = ($total_games > 0) ? $player_data['total_deaths'] / $total_games : 0;

                    $stats_by_type[$game_type] = [
                        'game_type' => $game_type,
                        'total_kills' => (int)$player_data['total_kills'],
                        'total_deaths' => (int)$player_data['total_deaths'],
                        'total_wins' => (int)$player_data['total_wins'],
                        'total_losses' => (int)$player_data['total_losses'],
                        'total_games' => $total_games,
                        'kd' => round($player_kd, 2),
                        'impact' => round($impact, 2),
                        'kpr' => round($kpr, 2),
                        'dpr' => round($dpr, 2),
                        'team' => $player_data['team'],
                        'avg_kd' => round($avg_kd, 2)
                    ];
                } else {
                    // 如果该游戏类型下没有数据
                    $stats_by_type[$game_type] = [
                        'game_type' => $game_type,
                        'total_kills' => 0,
                        'total_deaths' => 0,
                        'total_wins' => 0,
                        'total_losses' => 0,
                        'total_games' => 0,
                        'kd' => 0,
                        'impact' => 0,
                        'kpr' => 0,
                        'dpr' => 0,
                        'team' => null,
                        'avg_kd' => 0
                    ];
                }
            }

            $result = [
                'nickname' => $player_check['nickname'],
                'player_rank' => $player_check['player_rank'],
                'stats_by_game_type' => $stats_by_type
            ];

            Utils::apiResponse('success', '获取玩家游戏类型统计数据成功', $result);
        } else {
            Utils::apiResponse('error', '不支持的请求方法', null);
        }
        break;

    // 批量获取玩家统计数据
    case 'players_batch_stats':
        if ($method === 'POST') {
            $input = json_decode(file_get_contents('php://input'), true);

            if (!isset($input['nicknames']) || !is_array($input['nicknames'])) {
                Utils::apiResponse('error', '缺少玩家昵称列表参数', null);
            }

            $nicknames = array_map(function($name) {
                return Utils::sanitizeInput($name);
            }, $input['nicknames']);

            $game_type = isset($input['game_type']) ? Utils::sanitizeInput($input['game_type']) : null;

            // 验证游戏类型
            $valid_types = ['暗杀', '死斗', '盟主'];
            if ($game_type && !in_array($game_type, $valid_types)) {
                Utils::apiResponse('error', '无效的游戏类型', null);
            }

            $results = [];

            foreach ($nicknames as $nickname) {
                // 检查玩家是否存在
                $check_sql = "SELECT id, nickname, player_rank FROM players WHERE nickname = '{$nickname}'";
                $player_check = $db->getRow($check_sql);

                if (!$player_check) {
                    $results[$nickname] = [
                        'error' => '未找到指定玩家',
                        'nickname' => $nickname
                    ];
                    continue;
                }

                // 获取玩家数据
                if ($game_type) {
                    $sql = "SELECT
                              p.id as player_id,
                              p.nickname,
                              p.player_rank,
                              COALESCE(SUM(gr.kills), 0) as total_kills,
                              COALESCE(SUM(gr.deaths), 0) as total_deaths,
                              COALESCE(SUM(gr.wins), 0) as total_wins,
                              COALESCE(SUM(gr.losses), 0) as total_losses,
                              (CASE WHEN COALESCE(SUM(gr.deaths), 0) = 0 THEN COALESCE(SUM(gr.kills), 0) ELSE COALESCE(SUM(gr.kills), 0) / COALESCE(SUM(gr.deaths), 1) END) as kd,
                              MAX(gr.team) as team
                            FROM
                              players p
                            LEFT JOIN
                              game_records gr ON p.id = gr.player_id
                            LEFT JOIN
                              games gm ON gr.game_id = gm.id
                            WHERE
                              p.nickname = '{$nickname}' AND (gm.game_type = '{$game_type}' OR gm.game_type IS NULL)
                            GROUP BY
                              p.id, p.nickname, p.player_rank";
                } else {
                    $sql = "SELECT
                              p.id as player_id,
                              p.nickname,
                              p.player_rank,
                              COALESCE(SUM(gr.kills), 0) as total_kills,
                              COALESCE(SUM(gr.deaths), 0) as total_deaths,
                              COALESCE(SUM(gr.wins), 0) as total_wins,
                              COALESCE(SUM(gr.losses), 0) as total_losses,
                              (CASE WHEN COALESCE(SUM(gr.deaths), 0) = 0 THEN COALESCE(SUM(gr.kills), 0) ELSE COALESCE(SUM(gr.kills), 0) / COALESCE(SUM(gr.deaths), 1) END) as kd,
                              MAX(gr.team) as team
                            FROM
                              players p
                            LEFT JOIN
                              game_records gr ON p.id = gr.player_id
                            WHERE
                              p.nickname = '{$nickname}'
                            GROUP BY
                              p.id, p.nickname, p.player_rank";
                }

                $player = $db->getRow($sql);

                if ($player) {
                    // 计算统计数据
                    $player_kd = $player['kd'] ?? 0;
                    $total_games = $player['total_wins'] + $player['total_losses'];
                    $kpr = ($total_games > 0) ? $player['total_kills'] / $total_games : 0;
                    $dpr = ($total_games > 0) ? $player['total_deaths'] / $total_games : 0;
                    $win_rate = ($total_games > 0) ? ($player['total_wins'] / $total_games) * 100 : 0;

                    // 简化的Impact计算（不计算队伍平均KD，使用固定基准）
                    $base_kd = 1.0; // 基准KD值
                    $impact = ($base_kd > 0) ? $player_kd / $base_kd : $player_kd;

                    $results[$nickname] = [
                        'nickname' => $player['nickname'],
                        'player_rank' => $player['player_rank'],
                        'game_type' => $game_type,
                        'total_kills' => (int)$player['total_kills'],
                        'total_deaths' => (int)$player['total_deaths'],
                        'total_wins' => (int)$player['total_wins'],
                        'total_losses' => (int)$player['total_losses'],
                        'total_games' => $total_games,
                        'win_rate' => round($win_rate, 2),
                        'kd' => round($player_kd, 2),
                        'impact' => round($impact, 2),
                        'kpr' => round($kpr, 2),
                        'dpr' => round($dpr, 2),
                        'team' => $player['team']
                    ];
                } else {
                    $results[$nickname] = [
                        'error' => '查询玩家数据失败',
                        'nickname' => $nickname
                    ];
                }
            }

            Utils::apiResponse('success', '批量获取玩家统计数据成功', $results);
        } else {
            Utils::apiResponse('error', '不支持的请求方法，请使用POST', null);
        }
        break;

    // 默认：未找到路径
    default:
        Utils::apiResponse('error', '未找到请求的API路径', null);
        break;
}

// 记录API访问
// 关闭数据库连接
$db->close(); 




