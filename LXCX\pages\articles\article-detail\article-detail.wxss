/* 页面容器 */
.container {
  padding: 0;
  box-sizing: border-box;
  background-color: #fff;
  min-height: 100vh;
  position: relative;
  background: linear-gradient(135deg, #f5ecd7 0%, #e9d8b4 100%);
  font-family: 'ST<PERSON>aiti', '<PERSON><PERSON><PERSON>', 'FZ<PERSON>huTi', 'FZKai-Z03', '楷体', serif;
  color: #4a3b22;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 加载状态 */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 80rpx 0;
  color: #bfa76a;
  font-size: 28rpx;
  font-family: inherit;
}

/* 错误提示 */
.error {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 80rpx 0;
  color: #bfa76a;
  font-size: 28rpx;
  font-family: inherit;
}

/* 文章容器 */
.article-wrapper {
  padding: 30rpx;
  padding-right: 40rpx;
  background-color: #fff;
  box-sizing: border-box;
  width: 100%;
  background:  rgba(255, 251, 230, 0.97);
  border: 2rpx solid #bfa76a;
  border-radius: 22rpx;
  box-shadow: 0 12rpx 32rpx rgba(107, 58, 28, 0.13);
  padding: 48rpx 36rpx 36rpx 36rpx;
  margin: 0 12rpx 0 12rpx;
  position: relative;
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
}

/* 文章标题 */
.article-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #000;
  line-height: 1.6;
  margin-bottom: 28rpx;
  letter-spacing: 3rpx;
  text-shadow: 0 4rpx 12rpx #e9d8b4;
  font-family: inherit;
  border-left: 10rpx solid #bfa76a;
  padding-left: 18rpx;
  background: linear-gradient(90deg, #fffbe6 80%, #f7e7b4 100%);
  border-radius: 8rpx;
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
  overflow: hidden;
}

/* 文章元信息 */
.article-meta {
  display: flex;
  flex-wrap: wrap;
  font-size: 24rpx;
  color: #a08c5b;
  margin-bottom: 24rpx;
  font-family: inherit;
  border-bottom: 1rpx dashed #e9d8b4;
  padding-bottom: 10rpx;
}

.article-meta text {
  margin-right: 28rpx;
  color: #bfa76a;
}

/* 文章封面图 */
.article-cover {
  width: 100%;
  border-radius: 12rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(107, 58, 28, 0.10);
  border: 2rpx solid #e9d8b4;
  background: #f5ecd7;
}

/* 文章内容 */
.article-content {
  font-size: 34rpx;
  line-height: 2.2;
   color: #000;
  letter-spacing: 1.5rpx;
  background: none;
  font-family: inherit;
  padding: 0 2rpx 0 2rpx;
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
  overflow: hidden;
  padding-right: 10rpx;
  box-sizing: border-box;
  width: 100%;
}

.article-content rich-text {
  width: 100%;
  display: block;
  box-sizing: border-box;
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
}

/* 富文本内容样式 */
.article-content rich-text {
  display: block;
}

/* 底部安全区域 */
.safe-bottom-area {
  height: calc(100rpx + env(safe-area-inset-bottom));
}

/* 底部操作栏 */
.article-actions {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height: 100rpx;
  background: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
  transform: translateY(100%);
  transition: transform 0.3s ease-out;
  z-index: 100;
  padding-bottom: env(safe-area-inset-bottom);
}

.article-actions.visible {
  transform: translateY(0);
}

/* 操作按钮容器 */
.action-item {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100rpx; /* 确保高度与操作栏一致 */
  padding: 0 40rpx;
  box-sizing: border-box;
}

/* 图标容器 */
.action-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.action-icon.liked {
  color: #d46b08;
}

/* 图标文本 */
.icon-text {
  font-size: 90rpx;
  line-height: 1;
  font-family: serif;
  transform: scale(1.2);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 点赞数文本 */
.action-text {
  font-size: 32rpx;
  color: #666;
  margin-left: 16rpx;
  min-width: 40rpx;
  text-align: left;
}

/* 按钮点击效果 */
.button-hover {
  opacity: 0.7;
  background: rgba(0, 0, 0, 0.05);
}

/* 文章内图片适配 */
.article-content image {
  max-width: 100% !important;
  width: 100% !important;
  height: auto !important;
  border-radius: 8rpx;
  margin: 20rpx auto !important;
  display: block !important;
  box-shadow: 0 4rpx 16rpx rgba(107, 58, 28, 0.15);
}

/* 文章内容中的富文本图片 */
.article-content rich-text image {
  max-width: 100% !important;
  width: 100% !important;
  height: auto !important;
  display: block !important;
  margin: 20rpx auto !important;
}

/* 确保富文本中的图片正确显示 */
.article-content rich-text img {
  max-width: 100% !important;
  width: 100% !important;
  height: auto !important;
  display: block !important;
  margin: 20rpx auto !important;
  border-radius: 8rpx;
  box-shadow: 0 4rpx 16rpx rgba(107, 58, 28, 0.15);
}

/* 文章内容包装器 */
.article-wrapper {
  width: 100%;
}

/* 交互区域 */
.article-actions {
  display: flex;
  justify-content: space-around;
  padding: 20rpx 30rpx;
  background-color: #fff;
  border-top: 1rpx solid #eee;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 100;
  box-sizing: border-box;
  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.08);
  opacity: 0;
  transform: translateY(100%);
  transition: all 0.3s ease;
  border-top-left-radius: 20rpx;
  border-top-right-radius: 20rpx;
}

.article-actions.visible {
  opacity: 1;
  transform: translateY(0);
}

.button-hover {
  opacity: 0.8;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 10rpx;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10rpx 20rpx;
  flex: 1;
  position: relative;
}

.action-item:active {
  opacity: 0.7;
}

.action-icon {
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.2s;
}

.action-icon:active {
  transform: scale(1.1);
}

.icon-text {
  font-size: 48rpx;
  line-height: 1;
}

.action-text {
  font-size: 24rpx;
  color: #666;
  margin-top: 10rpx;
}

/* 点赞和收藏的激活状态 */
.action-icon.liked {
  color:rgb(212, 8, 8);
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

/* Markdown 样式 */
.article-content h1,
.article-content h2,
.article-content h3 {
  color: #6b3a1c;
  font-family: inherit;
  border-left: 6rpx solid #bfa76a;
  padding-left: 12rpx;
  background: linear-gradient(90deg, #fffbe6 80%, #f7e7b4 100%);
  border-radius: 6rpx;
}

.article-content ul, .article-content ol {
  color: #4a3b22;
  padding-left: 48rpx;
}

.article-content blockquote, .article-content .quote-block {
  background-color: #f7e7b4;
  border-left: 8rpx solid #bfa76a;
  color: #7a5a2a;
  font-style: italic;
  margin-left: 0;
  margin-right: 0;
}

.article-content code.inline-code {
  background-color: #f5ecd7;
  padding: 0 6rpx;
  font-family: monospace;
  border-radius: 4rpx;
  color: #a08c5b;
}

.article-content pre.code-block {
  background-color: #f5ecd7;
  padding: 20rpx;
  margin: 20rpx 0;
  border-radius: 8rpx;
  overflow-x: auto;
  color: #4a3b22;
}

.article-content pre.code-block code {
  font-family: monospace;
  white-space: pre;
}

.article-content hr {
  border: none;
  border-top: 2rpx dashed #bfa76a;
  margin: 32rpx 0;
}

/* 短代码样式 */
.article-content .shortcode {
  margin: 20rpx 0;
  padding: 20rpx;
  border-radius: 8rpx;
}

.article-content .warning-box {
  background-color: rgb(255, 230, 230);
  border-left: 8rpx solid rgb(255, 51, 24);
}

.article-content .info-box {
  background-color: #e6f4ff;
  border-left: 8rpx solid #1890ff;
}

.article-content .tip-box {
  background-color: #e6fff0;
  border-left: 8rpx solid #52c41a;
}

.article-content .download-box {
  background-color: #f5f5f5;
  padding: 20rpx;
  border-radius: 8rpx;
  margin: 20rpx 0;
}

.article-content .download-link {
  display: flex;
  align-items: center;
  color: #1890ff;
}

.article-content .download-icon {
  margin-right: 10rpx;
  font-size: 36rpx;
}

.article-content .download-filename {
  font-weight: bold;
  margin-right: 10rpx;
}

.article-content .file-size {
  color: #999;
  font-size: 26rpx;
}

.article-content .shortcode-button {
  display: inline-block;
  padding: 12rpx 30rpx;
  background-color: #1890ff;
  color: white;
  border-radius: 6rpx;
  margin: 10rpx 0;
  text-align: center;
}

.article-content .shortcode-table {
  width: 100%;
  border-collapse: collapse;
  margin: 20rpx 0;
}

.article-content .shortcode-table th,
.article-content .shortcode-table td {
  border: 1rpx solid #ddd;
  padding: 16rpx;
  text-align: left;
}

.article-content .shortcode-table th {
  background-color: #f5f5f5;
  font-weight: bold;
}

.article-content .table-responsive {
  overflow-x: auto;
  margin: 20rpx 0;
}

.article-content .markdown-image {
  display: block;
  margin: 20rpx 0;
  max-width: 100%;
  border-radius: 8rpx;
}

.article-content .quote-block {
  padding: 20rpx;
  margin: 20rpx 0;
  border-left: 6rpx solid #ddd;
  background-color: #f9f9f9;
  color: #666;
}

/* 卡片装饰角 */
.article-wrapper::after {
  content: '';
  position: absolute;
  right: 0; top: 0;
  width: 60rpx; height: 60rpx;
  background: url('https://img1.lxbl.online/102.jpg') no-repeat right top;
  opacity: 0.12;
  pointer-events: none;
}

/* 视频容器样式 */
.video-container {
  width: 100%;
  margin: 20rpx 0;
  background: #000;
  border-radius: 16rpx;
  overflow: hidden;
  position: relative;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.article-video {
  width: 100%;
  height: 422rpx; /* 16:9 比例 */
  display: block;
}

/* 视频加载占位图 */
.video-placeholder {
  width: 100%;
  height: 422rpx;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 视频错误提示 */
.video-error {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #999;
  font-size: 24rpx;
  text-align: center;
}

/* 视频控制按钮样式 */
.action-item .action-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  background: rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8rpx;
  transition: all 0.3s ease;
}

.action-item:active .action-icon {
  transform: scale(0.9);
  background: rgba(0, 0, 0, 0.1);
}

.action-text {
  font-size: 24rpx;
  color: #666;
}

/* 播放状态样式 */
.action-item.playing .action-icon {
  background: rgba(0, 122, 255, 0.1);
}

.action-item.playing .action-text {
  color: #007AFF;
}

/* 文章内容区域样式调整 */
.article-content {
  padding: 30rpx;
  line-height: 1.6;
  color: #333;
  font-size: 28rpx;
}

/* 底部交互区样式补充 */
.article-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 20rpx 30rpx;
  display: flex;
  justify-content: space-around;
  align-items: center;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.article-actions.visible {
  transform: translateY(0);
}

/* 安全区域适配 */
.safe-bottom-area {
  height: 120rpx; /* 底部操作栏的高度 */
}

/* 媒体列表样式 */
.media-list {
  margin: 20rpx 0;
  width: 100%;
}

.media-image {
  width: 100%;
  margin: 20rpx 0;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.media-video {
  width: 100%;
  margin: 20rpx 0;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

/* 文章详情页样式 */
.article-detail {
  padding: 20rpx;
  background: #fff;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

.article-container {
  padding-bottom: 120rpx;
}

.article-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
  line-height: 1.4;
}

/* 视频播放器样式 */
.video-container {
  position: relative;
  width: 100%;
  margin-bottom: 30rpx;
  border-radius: 12rpx;
  overflow: hidden;
  background: #000;
}

.video-container video {
  width: 100%;
  height: 420rpx;
}

.video-error {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #fff;
  text-align: center;
  background: rgba(0, 0, 0, 0.6);
  padding: 10rpx 20rpx;
  border-radius: 6rpx;
}

/* 媒体列表样式 */
.media-list {
  margin: 20rpx 0;
}

.media-item {
  margin-bottom: 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
  background: #f8f8f8;
}

/* 视频项样式 */
.video-item video {
  width: 100%;
  height: 360rpx;
}

/* 音频播放器样式 */
.audio-item {
  padding: 20rpx;
  background: #fff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  border-radius: 12rpx;
  margin: 20rpx 0;
}

.audio-item.playing {
  border: 2rpx solid #007AFF;
}

.audio-player {
  display: flex;
  align-items: center;
}

.audio-control {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #f0f0f0;
  border-radius: 50%;
  margin-right: 20rpx;
  transition: all 0.3s ease;
}

.control-icon {
  font-size: 40rpx;
  line-height: 1;
  color: #007AFF;
}

.audio-info {
  flex: 1;
}

.audio-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.audio-progress {
  width: 100%;
  margin: 10rpx 0;
}

.audio-time {
  font-size: 24rpx;
  color: #666;
}

.audio-error {
  color: #ff4d4f;
  font-size: 24rpx;
  margin-top: 10rpx;
  text-align: center;
}

/* 图片项样式 */
.image-item {
  text-align: center;
}

.media-image {
  width: 100%;
  height: auto;
  border-radius: 12rpx;
}

/* 文章内容样式 */
.article-content {
  margin-top: 30rpx;
  font-size: 30rpx;
  line-height: 1.6;
  color: #333;
}

/* 底部操作栏样式 */
.article-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background: #fff;
  display: flex;
  justify-content: space-around;
  align-items: center;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
  transform: translateY(100%);
  transition: transform 0.3s ease-out;
}

.article-actions.show {
  transform: translateY(0);
}

.action-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
}

.action-item image {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}

.action-item text {
  font-size: 28rpx;
  color: #666;
}

/* 内嵌媒体样式 */
.article-content .media-placeholder {
  margin: 20rpx 0;
  border-radius: 12rpx;
  overflow: hidden;
}

.article-content .audio-placeholder {
  background: #f8f9fa;
  border: 1rpx solid #e9ecef;
  padding: 20rpx;
}

.audio-player-inline {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.audio-control-inline {
  width: 60rpx;
  height: 60rpx;
  background: #007AFF;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.audio-control-inline:hover {
  background: #0056b3;
  transform: scale(1.05);
}

.audio-control-inline .control-icon {
  color: white;
  font-size: 24rpx;
  line-height: 1;
}

.audio-info-inline {
  flex: 1;
}

.audio-info-inline .audio-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  font-weight: 500;
}

.audio-progress-bar {
  width: 100%;
  height: 6rpx;
  background: #e9ecef;
  border-radius: 3rpx;
  overflow: hidden;
}

.progress-line {
  height: 100%;
  background: #007AFF;
  width: 0%;
  transition: width 0.3s ease;
}

/* 浮动音频控制面板 */
.audio-control-panel {
  position: fixed;
  bottom: 120rpx;
  left: 20rpx;
  right: 20rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);
  z-index: 1000;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.audio-player-floating {
  display: flex;
  align-items: center;
  padding: 20rpx;
  gap: 20rpx;
}

.audio-player-floating .audio-control {
  width: 60rpx;
  height: 60rpx;
  background: #007AFF;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.audio-player-floating .control-icon {
  color: white;
  font-size: 24rpx;
  line-height: 1;
}

.audio-player-floating .audio-info {
  flex: 1;
  min-width: 0;
}

.audio-player-floating .audio-name {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 8rpx;
  font-weight: 500;
}

.audio-player-floating .audio-progress {
  width: 100%;
  margin: 8rpx 0;
}

.audio-player-floating .audio-time {
  font-size: 22rpx;
  color: #666;
}

.audio-close {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  flex-shrink: 0;
}

.audio-close text {
  font-size: 24rpx;
  color: #666;
  line-height: 1;
}

/* 内嵌视频样式优化 */
.article-content video {
  width: 100% !important;
  height: auto !important;
  min-height: 300rpx;
  border-radius: 12rpx !important;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1) !important;
  margin: 20rpx 0 !important;
  display: block !important;
}

/* 内嵌图片样式优化 */
.article-content img {
  width: 100% !important;
  height: auto !important;
  border-radius: 12rpx !important;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1) !important;
  margin: 20rpx 0 !important;
  display: block !important;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.article-content img:hover {
  transform: scale(1.02);
}

/* 媒体控制区域样式 */
.media-controls {
  margin: 30rpx 0;
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 30rpx;
  border: 1rpx solid #e9ecef;
}

.media-controls-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  text-align: center;
  border-bottom: 1rpx solid #e9ecef;
  padding-bottom: 15rpx;
}

.media-control-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  margin: 15rpx 0;
  background: white;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.media-control-item.active {
  border: 2rpx solid #007AFF;
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.15);
}

.media-control-item:active {
  transform: scale(0.98);
}

.media-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
  width: 60rpx;
  text-align: center;
}

.media-info {
  flex: 1;
  min-width: 0;
}

.media-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.media-url {
  font-size: 22rpx;
  color: #666;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.media-action {
  padding: 12rpx 24rpx;
  background: #007AFF;
  color: white;
  border-radius: 20rpx;
  font-size: 26rpx;
  font-weight: 500;
  min-width: 80rpx;
  text-align: center;
  transition: all 0.3s ease;
}

.media-action:active {
  background: #0056b3;
  transform: scale(0.95);
}

/* 视频模态框样式 */
.video-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.video-modal-content {
  width: 90%;
  max-width: 800rpx;
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

.video-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background: #f8f9fa;
  border-bottom: 1rpx solid #e9ecef;
}

.video-modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.video-modal-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  font-size: 32rpx;
  color: #666;
  transition: all 0.3s ease;
}

.video-modal-close:active {
  background: rgba(0, 0, 0, 0.2);
  transform: scale(0.9);
}

.modal-video {
  width: 100%;
  height: 400rpx;
  background: #000;
}

/* 内联媒体播放器样式 */
.inline-media-section {
  margin: 30rpx 0;
  padding: 0 20rpx;
}

.inline-video-player {
  margin: 30rpx 0;
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  border: 2rpx solid #e9ecef;
}

.inline-audio-player {
  margin: 30rpx 0;
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  border: 2rpx solid #e9ecef;
}

.media-title {
  padding: 20rpx 30rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  font-size: 28rpx;
  font-weight: 600;
  color: #495057;
  border-bottom: 1rpx solid #dee2e6;
}

.inline-video {
  width: 100%;
  height: 400rpx;
  background: #000;
}

.audio-control-inline {
  display: flex;
  align-items: center;
  padding: 25rpx 30rpx;
  gap: 20rpx;
  transition: background-color 0.3s ease;
}

.audio-control-inline.playing {
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
}

.audio-control-btn {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #007AFF 0%, #5856d6 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.3);
  transition: all 0.3s ease;
}

.audio-control-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.4);
}

.audio-control-btn .control-icon {
  color: white;
  font-size: 32rpx;
  line-height: 1;
}

.audio-info-inline {
  flex: 1;
  min-width: 0;
}

.audio-info-inline .audio-name {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 15rpx;
  font-weight: 500;
}

.audio-progress-container {
  margin-top: 15rpx;
}

.audio-progress-container .audio-progress {
  width: 100%;
  margin-bottom: 10rpx;
}

.audio-progress-container .audio-time {
  font-size: 24rpx;
  color: #666;
  text-align: center;
}

/* 简单的内联播放器样式 */
.inline-video-container {
  margin: 20rpx 0;
}

.inline-video-container .inline-video {
  width: 100%;
  height: 400rpx;
  background: #000;
  border-radius: 8rpx;
}

.inline-audio-container {
  margin: 20rpx 0;
  background: #f8f9fa;
  border-radius: 8rpx;
  border: 1rpx solid #e9ecef;
}

.audio-player-simple {
  display: flex;
  align-items: center;
  padding: 20rpx;
  gap: 15rpx;
}

.audio-player-simple .audio-control-btn {
  width: 60rpx;
  height: 60rpx;
  background: #007AFF;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.audio-player-simple .control-icon {
  color: white;
  font-size: 24rpx;
  line-height: 1;
}

.audio-player-simple .audio-info {
  flex: 1;
  min-width: 0;
}

.audio-player-simple .audio-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.audio-player-simple .audio-progress-container {
  margin-top: 10rpx;
}

.audio-player-simple .audio-progress {
  width: 100%;
  margin-bottom: 8rpx;
}

.audio-player-simple .audio-time {
  font-size: 22rpx;
  color: #666;
  text-align: center;
}