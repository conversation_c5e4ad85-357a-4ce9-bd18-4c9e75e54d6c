<?php
/**
 * 格式化文本处理函数
 * 提供Markdown解析和短代码解析功能
 */

/**
 * 解析文本中的短代码
 * 
 * @param string $content 内容文本
 * @return string 解析后的文本
 */
function parse_shortcodes($content) {
    // 首先处理转义的短代码，临时替换它们
    $content = preg_replace('/\\\\\[(.*?)\\\\\]/', '{{ESCAPED_SHORTCODE_$1}}', $content);
    
    // 警告框短代码
    $content = preg_replace_callback('/\[warning\]((?:[^[]|\[(?!\/warning\])|(?R))*)\[\/warning\]/s', function($matches) {
        return '<div class="shortcode warning-box"><strong>警告：</strong>' . trim($matches[1]) . '</div>';
    }, $content);
    
    // 信息框短代码
    $content = preg_replace_callback('/\[info\]((?:[^[]|\[(?!\/info\])|(?R))*)\[\/info\]/s', function($matches) {
        return '<div class="shortcode info-box"><strong>信息：</strong>' . trim($matches[1]) . '</div>';
    }, $content);
    
    // 提示框短代码
    $content = preg_replace_callback('/\[tip\]((?:[^[]|\[(?!\/tip\])|(?R))*)\[\/tip\]/s', function($matches) {
        return '<div class="shortcode tip-box"><strong>提示：</strong>' . trim($matches[1]) . '</div>';
    }, $content);
    
    // 代码框短代码 - 支持语言高亮
    $content = preg_replace_callback('/\[code(?:\s+lang="([^"]+)")?\]((?:[^[]|\[(?!\/code\])|(?R))*)\[\/code\]/s', function($matches) {
        $lang = isset($matches[1]) ? ' class="language-' . htmlspecialchars($matches[1]) . '"' : '';
        return '<pre class="code-block"><code' . $lang . '>' . htmlspecialchars(trim($matches[2])) . '</code></pre>';
    }, $content);
    
    // 引用短代码
    $content = preg_replace_callback('/\[quote(?:\s+author="([^"]+)")?\]((?:[^[]|\[(?!\/quote\])|(?R))*)\[\/quote\]/s', function($matches) {
        $author = isset($matches[1]) ? '<cite>' . htmlspecialchars($matches[1]) . '</cite>' : '';
        return '<blockquote class="quote-block">' . trim($matches[2]) . $author . '</blockquote>';
    }, $content);
    
    // 带属性的按钮短代码
    $content = preg_replace_callback('/\[button\s+url="([^"]+)"(?:\s+target="([^"]+)")?\]((?:[^[]|\[(?!\/button\])|(?R))*)\[\/button\]/s', function($matches) {
        $url = htmlspecialchars($matches[1]);
        $target = isset($matches[2]) ? ' target="' . htmlspecialchars($matches[2]) . '"' : ' target="_blank"';
        return '<a href="' . $url . '"' . $target . ' class="shortcode-button">' . trim($matches[3]) . '</a>';
    }, $content);
    
    // 颜色短代码
    $content = preg_replace_callback('/\[color=([#a-zA-Z0-9]+)\](.*?)\[\/color\]/s', function($matches) {
        $color = htmlspecialchars($matches[1]);
        $text = trim($matches[2]);
        return '<span style="color:' . $color . ';">' . $text . '</span>';
    }, $content);
    
    // 下载短代码
    $content = preg_replace_callback('/\[download\s+url="([^"]+)"(?:\s+size="([^"]+)")?\]((?:[^[]|\[(?!\/download\])|(?R))*)\[\/download\]/s', function($matches) {
        $url = htmlspecialchars($matches[1]);
        $size = isset($matches[2]) ? ' <span class="file-size">(' . htmlspecialchars($matches[2]) . ')</span>' : '';
        $filename = htmlspecialchars(trim($matches[3]));
        
        return '<div class="download-box">
            <a href="' . $url . '" class="download-link" download>
                <span class="download-icon">📥</span>
                <span class="download-filename">' . $filename . '</span>' . $size . '
            </a>
        </div>';
    }, $content);
    
    // 表格短代码
    $content = preg_replace_callback('/\[table\]((?:[^[]|\[(?!\/table\])|(?R))*)\[\/table\]/s', function($matches) {
        $table_content = trim($matches[1]);
        $rows = explode("\n", $table_content);
        $output = '<div class="table-responsive"><table class="shortcode-table">';
        
        foreach ($rows as $i => $row) {
            $cells = explode('|', trim($row));
            $output .= '<tr>';
            foreach ($cells as $cell) {
                $tag = ($i === 0) ? 'th' : 'td';
                $output .= '<' . $tag . '>' . trim($cell) . '</' . $tag . '>';
            }
            $output .= '</tr>';
        }
        
        $output .= '</table></div>';
        return $output;
    }, $content);
    
    // 水平分隔线短代码
    $content = preg_replace('/\[hr\]/', '<hr class="shortcode-hr">', $content);
    
    // 目录短代码
    $content = preg_replace('/\[toc\]/', '<div id="article-toc" class="article-toc"><h4>目录</h4><div class="toc-content"></div></div>', $content);
    
    // 恢复转义的短代码
    $content = preg_replace('/{{ESCAPED_SHORTCODE_(.*?)}}/', '[$1]', $content);
    
    return $content;
}

/**
 * 简单的Markdown解析函数
 * 
 * @param string $content Markdown文本
 * @return string 解析后的HTML
 */
function format_markdown($content) {
    // 获取标题并添加ID属性以便目录链接
    $headingCounter = [1 => 0, 2 => 0, 3 => 0];
    
    // 首先解析分隔线，避免与其他元素冲突
    $content = preg_replace('/^[\s]*?---[\s]*?$/m', '<hr class="markdown-hr">', $content);
    
    // 解析标题
    $content = preg_replace_callback('/^### (.*?)$/m', function($matches) use (&$headingCounter) {
        $headingCounter[3]++;
        $id = "heading-3-" . $headingCounter[3];
        return '<h3 id="' . $id . '">' . $matches[1] . '</h3>';
    }, $content);
    
    $content = preg_replace_callback('/^## (.*?)$/m', function($matches) use (&$headingCounter) {
        $headingCounter[2]++;
        $id = "heading-2-" . $headingCounter[2];
        return '<h2 id="' . $id . '">' . $matches[1] . '</h2>';
    }, $content);
    
    $content = preg_replace_callback('/^# (.*?)$/m', function($matches) use (&$headingCounter) {
        $headingCounter[1]++;
        $id = "heading-1-" . $headingCounter[1];
        return '<h1 id="' . $id . '">' . $matches[1] . '</h1>';
    }, $content);
    
    // 解析粗体
    $content = preg_replace('/\*\*(.*?)\*\*/', '<strong>$1</strong>', $content);
    
    // 解析斜体
    $content = preg_replace('/\*(.*?)\*/', '<em>$1</em>', $content);
    
    // 解析链接 [链接文字](链接地址)
    $content = preg_replace_callback('/\[(.*?)\]\((.*?)\)/', function($matches) {
        return '<a href="' . htmlspecialchars($matches[2]) . '">' . $matches[1] . '</a>';
    }, $content);
    
    // 解析无序列表
    $content = preg_replace_callback('/(?:^|\n)- (.*?)(?:\n|$)/', function($matches) {
        return "\n<ul><li>" . $matches[1] . "</li></ul>\n";
    }, $content);
    
    // 合并连续的无序列表项
    $content = preg_replace('/<\/ul>\s*<ul>/', '', $content);
    
    // 解析有序列表
    $content = preg_replace_callback('/(?:^|\n)(\d+)\. (.*?)(?:\n|$)/', function($matches) {
        return "\n<ol start=\"" . $matches[1] . "\"><li>" . $matches[2] . "</li></ol>\n";
    }, $content);
    
    // 合并连续的有序列表项
    $content = preg_replace('/<\/ol>\s*<ol start="\d+">/', '', $content);
    
    // 解析图片 ![alt文字](图片地址)
    $content = preg_replace_callback('/!\[(.*?)\]\((.*?)\)/', function($matches) {
        return '<img src="' . htmlspecialchars($matches[2]) . '" alt="' . htmlspecialchars($matches[1]) . '" class="markdown-image">';
    }, $content);
    
    // 解析引用
    $content = preg_replace_callback('/(?:^|\n)> (.*?)(?:\n|$)/', function($matches) {
        return "\n<blockquote>" . $matches[1] . "</blockquote>\n";
    }, $content);
    
    // 合并连续的引用
    $content = preg_replace('/<\/blockquote>\s*<blockquote>/', '<br>', $content);
    
    // 解析代码块
    $content = preg_replace_callback('/```(.*?)```/s', function($matches) {
        return '<pre class="code-block"><code>' . htmlspecialchars($matches[1]) . '</code></pre>';
    }, $content);
    
    // 解析行内代码
    $content = preg_replace_callback('/`(.*?)`/', function($matches) {
        return '<code class="inline-code">' . htmlspecialchars($matches[1]) . '</code>';
    }, $content);
    
    // 将换行转换为<br>标签
    $content = nl2br($content);
    
    return $content;
}

/**
 * 生成文章目录
 *
 * @param string $content 已处理的HTML内容
 * @return string 包含目录的HTML内容
 */
function generate_toc($content) {
    // 提取所有标题
    preg_match_all('/<h([1-3])\s+id="(.*?)">(.*?)<\/h\1>/', $content, $matches, PREG_SET_ORDER);
    
    if (empty($matches)) {
        // 如果没有标题，则移除目录占位符
        return preg_replace('/<div id="article-toc".*?<\/div><\/div>/', '', $content);
    }
    
    $toc = '<ul class="toc-list">';
    $currentLevel = 0;
    $prevLevel = 0;
    
    foreach ($matches as $match) {
        $level = (int)$match[1];
        $id = $match[2];
        $title = strip_tags($match[3]);
        
        if ($level > $prevLevel) {
            // 增加嵌套级别
            $toc .= str_repeat('<ul class="toc-sub-list">', $level - $prevLevel);
            $currentLevel = $level;
        } elseif ($level < $prevLevel) {
            // 减少嵌套级别
            $toc .= str_repeat('</li></ul>', $prevLevel - $level);
            $currentLevel = $level;
        } elseif ($prevLevel > 0) {
            // 同级新项
            $toc .= '</li>';
        }
        
        $toc .= '<li class="toc-item toc-level-' . $level . '"><a href="#' . $id . '">' . $title . '</a>';
        $prevLevel = $level;
    }
    
    // 闭合所有剩余的标签
    $toc .= str_repeat('</li></ul>', $currentLevel);
    $toc .= '</ul>';
    
    // 替换目录占位符
    $content = preg_replace('/<div id="article-toc" class="article-toc"><h4>目录<\/h4><div class="toc-content"><\/div><\/div>/', '<div id="article-toc" class="article-toc"><h4>目录</h4><div class="toc-content">' . $toc . '</div></div>', $content);
    
    return $content;
}

/**
 * 完整处理内容文本，先解析短代码，再解析Markdown，最后生成目录
 * 
 * @param string $content 原始内容文本
 * @return string 处理后的HTML内容
 */
function format_content($content) {
    // 首先应用短代码
    $content = parse_shortcodes($content);
    
    // 然后应用Markdown解析
    $content = format_markdown($content);
    
    // 最后生成目录
    $content = generate_toc($content);
    
    return $content;
}