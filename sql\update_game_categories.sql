-- 创建游戏类型表
CREATE TABLE IF NOT EXISTS `game_categories` (
    `id` INT(11) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(50) NOT NULL,
    `description` VARCHAR(255),
    `create_time` DATETIME NOT NULL,
    `update_time` DATETIME NOT NULL,
    UNIQUE (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 添加默认游戏类型
INSERT INTO `game_categories` (`name`, `description`, `create_time`, `update_time`) VALUES
('暗杀', '暗杀类游戏模式', NOW(), NOW()),
('死斗', '死斗类游戏模式', NOW(), NOW()),
('盟主', '盟主类游戏模式', NOW(), NOW());

-- 添加新列game_type_id到games表
ALTER TABLE `games` ADD COLUMN `game_type_id` INT(11) UNSIGNED AFTER `image_file`;

-- 更新现有数据
UPDATE `games` g
JOIN `game_categories` gc ON g.game_type = gc.name
SET g.game_type_id = gc.id;

-- 添加外键约束
ALTER TABLE `games`
ADD CONSTRAINT `fk_game_type`
FOREIGN KEY (`game_type_id`) REFERENCES `game_categories` (`id`)
ON DELETE RESTRICT
ON UPDATE CASCADE;

-- 删除旧的game_type列
ALTER TABLE `games` DROP COLUMN `game_type`; 