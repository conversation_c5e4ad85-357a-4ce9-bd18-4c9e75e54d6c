/* 页面容器 - 现代渐变背景设计 */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

/* 背景装饰元素 */
.container::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  animation: float 20s ease-in-out infinite;
  pointer-events: none;
}

@keyframes float {
  0%, 100% { transform: translate(0, 0) rotate(0deg); }
  33% { transform: translate(30px, -30px) rotate(120deg); }
  66% { transform: translate(-20px, 20px) rotate(240deg); }
}

/* 增强的头部区域 */
.header {
  text-align: center;
  padding: 60px 30px 20px;
  position: relative;
  z-index: 2;
}

.header-decoration {
  width: 60px;
  height: 4px;
  background: linear-gradient(90deg, #ffd700, #ffed4e);
  margin: 0 auto 20px;
  border-radius: 2px;
  box-shadow: 0 2px 8px rgba(255, 215, 0, 0.3);
}

.title {
  font-size: 32px;
  font-weight: 700;
  color: #ffffff;
  display: block;
  margin-bottom: 12px;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
  letter-spacing: 1px;
}

.title-underline {
  width: 80px;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.5), transparent);
  margin: 0 auto;
}

/* 模式列表容器 */
.mode-list {
  padding: 0 20px 10px;
  position: relative;
  z-index: 2;
}

/* 模式卡片基础样式 */
.mode-item {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  margin-bottom: 16px;
  position: relative;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 不同模式的渐变色彩 */
.mode-item-0 { box-shadow: 0 8px 32px rgba(255, 107, 107, 0.2); }
.mode-item-1 { box-shadow: 0 8px 32px rgba(54, 207, 201, 0.2); }
.mode-item-2 { box-shadow: 0 8px 32px rgba(255, 159, 67, 0.2); }
.mode-item-3 { box-shadow: 0 8px 32px rgba(123, 104, 238, 0.2); }

/* 卡片内部容器 */
.mode-card-inner {
  display: flex;
  align-items: center;
  padding: 12px 10px !important;
  position: relative;
  z-index: 2;
}

/* 背景装饰 */
.mode-item-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.mode-item-0 .mode-item-bg { background: linear-gradient(135deg, rgba(255, 107, 107, 0.1), rgba(255, 142, 83, 0.1)); }
.mode-item-1 .mode-item-bg { background: linear-gradient(135deg, rgba(54, 207, 201, 0.1), rgba(56, 239, 125, 0.1)); }
.mode-item-2 .mode-item-bg { background: linear-gradient(135deg, rgba(255, 159, 67, 0.1), rgba(255, 206, 84, 0.1)); }
.mode-item-3 .mode-item-bg { background: linear-gradient(135deg, rgba(123, 104, 238, 0.1), rgba(161, 196, 253, 0.1)); }

/* 悬停和点击效果 */
.mode-item:active {
  transform: translateY(2px) scale(0.98);
}

.mode-item:active .mode-item-bg {
  opacity: 1;
}

.mode-item:active .mode-card-inner {
  transform: scale(0.98);
}

/* 图标容器 */
.mode-icon-container {
  position: relative;
  margin-right: 20px;
}

.mode-icon {
  width: 56px;
  height: 56px;
  border-radius: 16px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.6));
  padding: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.icon-glow {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 18px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.mode-item:active .icon-glow {
  opacity: 1;
}

/* 内容区域 */
.mode-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.mode-name {
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 4px;
  letter-spacing: 0.5px;
}

.mode-description {
  font-size: 14px;
  color: #7f8c8d;
  font-weight: 400;
  opacity: 0.8;
}

/* 箭头容器和样式 */
.mode-arrow-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.5);
  transition: all 0.3s ease;
}

.mode-arrow {
  width: 12px;
  height: 12px;
  border-top: 2px solid #667eea;
  border-right: 2px solid #667eea;
  transform: rotate(45deg);
  transition: all 0.3s ease;
}

.mode-item:active .mode-arrow-container {
  background: rgba(255, 255, 255, 0.8);
  transform: scale(1.1);
}

.mode-item:active .mode-arrow {
  border-color: #5a67d8;
  transform: rotate(45deg) scale(1.1);
}

/* 页脚区域 */
.footer {
  padding: 15px 20px 40px;
  text-align: center;
  position: relative;
  z-index: 2;
}

.footer-text {
  color: rgba(255, 255, 255, 0.9);
  font-size: 16px;
  font-weight: 400;
  letter-spacing: 0.5px;
  padding: 10px 15px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 20px;
  display: inline-block;
  max-width: 90%;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(5px);
  line-height: 1.5;
}

.footer-tip {
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
  margin-top: 8px;
  font-weight: 300;
}

/* 响应式设计 */
@media screen and (max-width: 480px) {
  .header {
    padding: 40px 20px 15px;
  }

  .title {
    font-size: 28px;
  }

  .mode-list {
    padding: 0 15px 15px;
  }

  .mode-card-inner {
    padding: 20px 16px;
  }

  .mode-icon {
    width: 48px;
    height: 48px;
    padding: 10px;
  }

  .mode-name {
    font-size: 18px;
  }

  .mode-description {
    font-size: 13px;
  }
}

/* 动画增强 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.mode-item {
  animation: slideInUp 0.6s ease forwards;
}

.mode-item:nth-child(1) { animation-delay: 0.1s; }
.mode-item:nth-child(2) { animation-delay: 0.2s; }
.mode-item:nth-child(3) { animation-delay: 0.3s; }
.mode-item:nth-child(4) { animation-delay: 0.4s; }

/* 分享按钮样式 */
.share-buttons {
  display: flex;
  justify-content: center;
  gap: 20rpx;
  margin-top: 30rpx;
  padding: 0 30rpx;
}

.share-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 40rpx;
  border-radius: 40rpx;
  background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
  border: none;
  color: #ffffff;
  font-size: 28rpx;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.2);
}

.share-btn::after {
  border: none;
}

.share-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}

.share-btn text {
  font-weight: 500;
}

.share-btn:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.2);
}