                </div><!-- end .container -->
            </div><!-- end .main-content -->
        </div><!-- end .content-wrapper -->
    </div><!-- end .page-wrapper -->

    <!-- 回到顶部按钮 -->
    <a href="#" id="back-to-top" class="back-to-top" style="display: none; position: fixed; bottom: 20px; right: 20px; width: 40px; height: 40px; line-height: 40px; text-align: center; background: var(--primary); color: white; border-radius: 50%; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2); z-index: 1000; opacity: 0; transition: all 0.3s ease;">
        <i class="fas fa-arrow-up"></i>
    </a>

    <!-- 加载jQuery -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    
    <script>
    $(document).ready(function() {
        // 确认删除功能
        window.confirmDelete = function(message, callback) {
            if (confirm(message)) {
                callback();
            }
        };
        
        // 表格排序功能
        $('.table th[data-sort]').css('cursor', 'pointer').click(function() {
            let sort = $(this).data('sort');
            let order = 'asc';
            
            // 检查当前排序状态
            if ($(this).hasClass('sort-asc')) {
                order = 'desc';
                $(this).removeClass('sort-asc').addClass('sort-desc');
            } else if ($(this).hasClass('sort-desc')) {
                $(this).removeClass('sort-desc');
                sort = '';
            } else {
                $(this).addClass('sort-asc');
            }
            
            // 重置其他列的排序状态
            $(this).siblings('[data-sort]').removeClass('sort-asc sort-desc');
            
            // 重新加载页面，带上排序参数
            let url = new URL(window.location.href);
            if (sort) {
                url.searchParams.set('sort', sort);
                url.searchParams.set('order', order);
            } else {
                url.searchParams.delete('sort');
                url.searchParams.delete('order');
            }
            window.location.href = url.toString();
        });
        
        // 高亮当前排序列
        let currentSort = new URLSearchParams(window.location.search).get('sort');
        let currentOrder = new URLSearchParams(window.location.search).get('order');
        if (currentSort) {
            $('.table th[data-sort="' + currentSort + '"]').addClass(currentOrder === 'desc' ? 'sort-desc' : 'sort-asc');
        }
        
        // 修复表格响应式问题
        $('.table-responsive').each(function() {
            if ($(this).width() > $(window).width()) {
                $(this).css('overflow-x', 'auto');
            }
        });
        
        // 侧边栏切换 - 桌面端
        $('#toggle-sidebar').click(function() {
            $('body').toggleClass('collapsed');

            // 本地保存侧边栏状态
            let sidebarState = $('body').hasClass('collapsed') ? 'collapsed' : 'expanded';
            localStorage.setItem('sidebar_state', sidebarState);
        });

        // 移动端侧边栏切换
        $('#mobile-toggle').click(function() {
            $('body').toggleClass('sidebar-visible');
        });

        // 侧边栏遮罩层点击关闭
        $('.sidebar-backdrop').click(function() {
            $('body').removeClass('sidebar-visible');
        });

        // 加载保存的侧边栏状态（仅桌面端）
        if ($(window).width() > 768) {
            let savedState = localStorage.getItem('sidebar_state');
            if (savedState === 'collapsed') {
                $('body').addClass('collapsed');
            }
        }

        // 窗口大小变化时处理侧边栏状态
        $(window).resize(function() {
            if ($(window).width() > 768) {
                // 桌面端：移除移动端类，恢复保存的状态
                $('body').removeClass('sidebar-visible');
                let savedState = localStorage.getItem('sidebar_state');
                if (savedState === 'collapsed') {
                    $('body').addClass('collapsed');
                } else {
                    $('body').removeClass('collapsed');
                }
            } else {
                // 移动端：移除桌面端类
                $('body').removeClass('collapsed');
            }
        });

        // ESC键关闭移动端侧边栏
        $(document).keyup(function(e) {
            if (e.keyCode === 27) { // ESC键
                $('body').removeClass('sidebar-visible');
            }
        });
        
        // 回到顶部按钮
        $(window).scroll(function() {
            if ($(this).scrollTop() > 300) {
                $('#back-to-top').css('opacity', '1').fadeIn();
            } else {
                $('#back-to-top').fadeOut();
            }
        });
        
        $('#back-to-top').click(function(e) {
            e.preventDefault();
            $('html, body').animate({scrollTop: 0}, 300);
        });
        
        // 图片预览放大功能
        $(document).on('click', '.image-link', function(e) {
            e.preventDefault();
            let imgSrc = $(this).attr('href');
            let overlay = $('<div>').addClass('image-overlay').css({
                position: 'fixed',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
                backgroundColor: 'rgba(0,0,0,0.8)',
                zIndex: 9999,
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                cursor: 'zoom-out'
            });
            
            let img = $('<img>').attr('src', imgSrc).css({
                maxWidth: '90%',
                maxHeight: '90%',
                boxShadow: '0 5px 15px rgba(0,0,0,0.5)',
                borderRadius: '8px'
            });
            
            overlay.append(img);
            $('body').append(overlay);
            
            overlay.click(function() {
                $(this).fadeOut(300, function() {
                    $(this).remove();
                });
            });
        });
        
        // 确保内容显示
        $('.main-content').css({
            'display': 'block',
            'visibility': 'visible',
            'opacity': '1'
        });
        
        $('.content-wrapper').css({
            'display': 'flex',
            'flex-direction': 'column'
        });
        
        // 添加卡片阴影效果
        $('.card').hover(
            function() { $(this).css('box-shadow', '0 8px 25px rgba(0, 0, 0, 0.1)'); },
            function() { $(this).css('box-shadow', 'var(--box-shadow)'); }
        );
        
        // 给按钮添加波纹效果
        $('.btn').click(function(e) {
            let x = e.pageX - $(this).offset().left;
            let y = e.pageY - $(this).offset().top;
            
            let ripple = $('<span>').addClass('ripple');
            ripple.css({
                left: x + 'px',
                top: y + 'px'
            });
            
            $(this).append(ripple);
            
            setTimeout(function() {
                ripple.remove();
            }, 600);
        });
    });
    </script>
</body>
</html> 