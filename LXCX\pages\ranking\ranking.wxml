<view class="ranking-container">
  <!-- 顶部统计信息 -->
  <view class="stats-header">
    <view class="stats-left">
      <view class="stats-title">已累计{{currentGameType}}</view>
      <view class="stats-counter">
        <view class="counter-box">{{dayCount[0]}}</view>
        <view class="counter-box">{{dayCount[1]}}</view>
        <view class="counter-box">{{dayCount[2]}}</view>
        <view class="counter-box">{{dayCount[3]}}</view>
        <view class="counter-unit">天</view>
      </view>
      <view class="stats-total">累计狙杀数量: {{total_records}}</view>
    </view>
    <view class="stats-right">
      <!-- 显示规则按钮 -->   
      <view class="rules-btn" bindtap="showRules">最新规则</view>
      <!-- 游戏类型选择器 -->
      <view class="game-type-selector">
        <view class="custom-select" bindtap="toggleDropdown">
          <view class="selected-value">{{currentGameType}}</view>
          <text class="select-arrow {{isDropdownOpen ? 'open' : ''}}">▼</text>
        </view>
        <view class="dropdown-menu {{isDropdownOpen ? 'show' : ''}}">
          <view 
            wx:for="{{gameTypes}}" 
            wx:key="*this"
            class="dropdown-item {{currentGameType === item ? 'active' : ''}}"
            data-type="{{item}}"
            bindtap="selectGameType"
          >
            {{item}}
          </view>
        </view>
      </view>
      
    </view>
  </view>

  <!-- 前三名展示 -->
  <view class="top-three">
    <!-- 第二名 -->
    <view class="top-player second">
      <view class="player-avatar-container">
        <image class="player-avatar" src="/images/default-avatar.png" mode="aspectFill"></image>
       </view>
      <view class="player-rank">NO.2</view>
      <view class="player-name">{{rankList[1].nickname}}</view>
      <view class="player-hall">{{rankList[1].hall}}</view>
      <view class="player-score">
      <text>{{rankList[1].total_kills}}</text>
      </view>
    </view>
    
    <!-- 第一名 -->
    <view class="top-player first">
      <view class="player-avatar-container">
        <image class="player-avatar" src="/images/default-avatar.png" mode="aspectFill"></image>
    
      </view>
      <view class="player-rank">NO.1</view>
      <view class="player-name">{{rankList[0].nickname}}</view>
      <view class="player-hall">{{rankList[0].hall}}</view>
      <view class="player-score">
        
        <text>{{rankList[0].snipeCount}}</text>
      </view>
    </view>
    
    <!-- 第三名 -->
    <view class="top-player third">
      <view class="player-avatar-container">
        <image class="player-avatar" src="/images/default-avatar.png" mode="aspectFill"></image>
        
      </view>
      <view class="player-rank">NO.3</view>
      <view class="player-name">{{rankList[2].nickname}}</view>
      <view class="player-hall">{{rankList[2].hall}}</view>
      <view class="player-score">
      
        <text>{{rankList[2].snipeCount}}</text>
      </view>
    </view>
  </view>

  <!-- 排行榜列表 -->
  <view class="ranking-list">
    <block wx:for="{{rankList}}" wx:key="index" wx:if="{{index >= 3 && index < 15}}">
      <view class="ranking-item">
        <view class="rank-number">NO.{{index + 1}}</view>
        <view class="player-info">
          <text class="player-name">{{item.nickname}}</text>
          <text class="player-hall">{{rankList[index].hall}}</text>
          <text class="player-summary">狙杀: {{item.total_kills || 0}}</text>
        </view>
        
      </view>
    </block>
  </view>
</view> 