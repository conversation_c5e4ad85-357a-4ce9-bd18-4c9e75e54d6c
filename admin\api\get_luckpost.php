<?php
header('Content-Type: application/json; charset=utf-8');

$jsonFile = __DIR__ . '/../luckpost.json';
$jsonData = file_get_contents($jsonFile);
$data = json_decode($jsonData, true);

if (!$data || !is_array($data)) {
    echo json_encode([
        'success' => false,
        'msg' => '数据读取失败',
        'xst' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

$randIndex = array_rand($data);
$luck = $data[$randIndex]['xst'];

echo json_encode([
    'success' => true,
    'xst' => $luck
], JSON_UNESCAPED_UNICODE);
