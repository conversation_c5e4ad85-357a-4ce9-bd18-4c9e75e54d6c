/**
 * 格式化时间
 * @param {Date} date - 日期对象
 * @returns {String} - 格式化后的时间字符串，形如 2023-05-16 15:30:45
 */
const formatTime = date => {
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  const hour = date.getHours()
  const minute = date.getMinutes()
  const second = date.getSeconds()

  return [year, month, day].map(formatNumber).join('-') + ' ' + [hour, minute, second].map(formatNumber).join(':')
}

/**
 * 解析日期字符串为Date对象（兼容iOS）
 * @param {String} dateString - 日期字符串
 * @returns {Date} - 日期对象
 */
const parseDate = dateString => {
  if (!dateString) return new Date();
  
  // iOS 只支持以下格式：
  // - "yyyy/MM/dd"
  // - "yyyy/MM/dd HH:mm:ss"
  // - "yyyy-MM-dd"
  // - "yyyy-MM-ddTHH:mm:ss"
  // - "yyyy-MM-ddTHH:mm:ss+HH:mm"
  
  // 检测 "yyyy-MM-dd HH:mm:ss" 格式并转换为 iOS 兼容格式
  if (typeof dateString === 'string' && dateString.match(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/)) {
    // 转换为 "yyyy-MM-ddTHH:mm:ss" 格式 (ISO 8601)
    dateString = dateString.replace(' ', 'T');
  }
  
  // 尝试直接解析
  try {
    const date = new Date(dateString);
    // 检查日期是否有效
    if (!isNaN(date.getTime())) {
      return date;
    }
  } catch (e) {
    console.error('日期直接解析失败:', e);
  }
  
  // 如果直接解析失败，尝试其他格式转换
  try {
    if (typeof dateString === 'string') {
      if (dateString.includes('-') && dateString.includes(' ') && dateString.includes(':')) {
        // 转换为 "yyyy/MM/dd HH:mm:ss" 格式
        const formattedWithSlash = dateString.replace(/-/g, '/');
        const dateWithSlash = new Date(formattedWithSlash);
        if (!isNaN(dateWithSlash.getTime())) {
          return dateWithSlash;
        }
      }
    }
  } catch (e) {
    console.error('日期格式转换失败:', e);
  }
  
  // 手动解析日期
  try {
    if (typeof dateString === 'string') {
      let parts = [];
      let year, month, day, hours = 0, minutes = 0, seconds = 0;
      
      // 处理 "yyyy-MM-dd HH:mm:ss" 格式
      if (dateString.includes('-') && dateString.includes(':')) {
        const [datePart, timePart] = dateString.split(' ');
        if (datePart && datePart.includes('-')) {
          [year, month, day] = datePart.split('-').map(Number);
        }
        
        if (timePart && timePart.includes(':')) {
          [hours, minutes, seconds] = timePart.split(':').map(Number);
        }
        
        // 月份要减1，因为JS的月份是从0开始的
        if (year && month && day) {
          return new Date(year, month - 1, day, hours, minutes, seconds);
        }
      }
    }
  } catch (e) {
    console.error('日期手动解析失败:', e);
  }
  
  // 所有方法都失败时，返回当前日期
  console.warn('无法解析日期，使用当前时间:', dateString);
  return new Date();
}

/**
 * 标准化日期格式，确保返回 iOS 兼容格式的字符串
 * @param {String|Date} date - 日期对象或日期字符串
 * @returns {String} - 格式化后的日期字符串，形如 "yyyy-MM-ddTHH:mm:ss"
 */
const standardizeDate = date => {
  // 如果是字符串，先尝试解析为Date对象
  if (typeof date === 'string') {
    date = parseDate(date);
  } else if (!(date instanceof Date)) {
    date = new Date();
  }
  
  // 确保是有效日期
  if (isNaN(date.getTime())) {
    date = new Date();
  }
  
  // 格式化为 ISO 8601 格式 (yyyy-MM-ddTHH:mm:ss)
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  const seconds = date.getSeconds().toString().padStart(2, '0');
  
  return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}`;
}

/**
 * 格式化数字
 * @param {Number} n - 数字
 * @returns {String} - 格式化后的字符串，小于10的数字前补0
 */
const formatNumber = n => {
  n = n.toString()
  return n[1] ? n : `0${n}`
}

/**
 * 显示提示信息
 * @param {String} title - 提示内容
 * @param {String} icon - 图标，可选值为 'success', 'loading', 'none'
 * @param {Number} duration - 提示持续时间，单位毫秒
 */
const showToast = (title, icon = 'none', duration = 1500) => {
  wx.showToast({
    title,
    icon,
    duration
  })
}

/**
 * 显示加载中
 * @param {String} title - 加载提示文字
 */
const showLoading = (title = '加载中...') => {
  wx.showLoading({
    title,
    mask: true
  })
}

/**
 * 隐藏加载中
 */
const hideLoading = () => {
  wx.hideLoading()
}

/**
 * 确认对话框
 * @param {String} title - 标题
 * @param {String} content - 内容
 * @returns {Promise} - 点击确认返回resolve，点击取消返回reject
 */
const showConfirm = (title, content) => {
  return new Promise((resolve, reject) => {
    wx.showModal({
      title,
      content,
      success(res) {
        if (res.confirm) {
          resolve()
        } else if (res.cancel) {
          reject()
        }
      }
    })
  })
}

/**
 * 去除字符串首尾空格
 * @param {String} str - 字符串
 * @returns {String} - 去除首尾空格后的字符串
 */
const trim = str => {
  return str.replace(/(^\s*)|(\s*$)/g, '')
}

/**
 * 解析文本中的短代码
 * 
 * @param {String} content 内容文本
 * @return {String} 解析后的文本
 */
const parseShortcodes = content => {
  // 警告框短代码
  content = content.replace(/\[warning\](.*?)\[\/warning\]/gs, 
    '<div class="shortcode warning-box"><strong>警告：</strong>$1</div>');
  
  // 信息框短代码
  content = content.replace(/\[info\](.*?)\[\/info\]/gs, 
    '<div class="shortcode info-box"><strong>信息：</strong>$1</div>');
  
  // 提示框短代码
  content = content.replace(/\[tip\](.*?)\[\/tip\]/gs, 
    '<div class="shortcode tip-box"><strong>提示：</strong>$1</div>');
  
  // 代码框短代码
  content = content.replace(/\[code\](.*?)\[\/code\]/gs, 
    '<pre class="code-block"><code>$1</code></pre>');
  
  // 引用短代码
  content = content.replace(/\[quote\](.*?)\[\/quote\]/gs, 
    '<blockquote class="quote-block">$1</blockquote>');
  
  // 带属性的按钮短代码 [button url="链接地址"]按钮文字[/button]
  content = content.replace(/\[button url="(.*?)"\](.*?)\[\/button\]/gs, 
    '<a href="$1" class="shortcode-button" target="_blank">$2</a>');
  
  // 颜色短代码 [color=#ff0000]文本[/color]
  content = content.replace(/\[color=(#[a-fA-F0-9]{3,6}|[a-zA-Z]+)\](.*?)\[\/color\]/gs, 
    '<span style="color:$1;">$2</span>');
  
  // 下载短代码 [download url="文件地址" size="文件大小"]文件名[/download]
  content = content.replace(/\[download url="(.*?)"(?: size="(.*?)")?\](.*?)\[\/download\]/gs, (match, url, size, filename) => {
    size = size ? ` <span class="file-size">(${size})</span>` : '';
    return `<div class="download-box">
      <a href="${url}" data-url="${url}" class="download-link" download>
        <span class="download-icon">📥</span>
        <span class="download-filename">${filename}</span>${size}
      </a>
    </div>`;
  });
  
  // 表格短代码 [table]行1列1|行1列2\n行2列1|行2列2[/table]
  content = content.replace(/\[table\](.*?)\[\/table\]/gs, (match, tableContent) => {
    const rows = tableContent.trim().split('\n');
    let output = '<div class="table-responsive"><table class="shortcode-table">';
    
    rows.forEach((row, i) => {
      const cells = row.trim().split('|');
      output += '<tr>';
      cells.forEach(cell => {
        // 第一行使用th，其他行使用td
        const tag = (i === 0) ? 'th' : 'td';
        output += `<${tag}>${cell.trim()}</${tag}>`;
      });
      output += '</tr>';
    });
    
    output += '</table></div>';
    return output;
  });
  
  // 水平分隔线短代码
  content = content.replace(/\[hr\]/g, 
    '<hr class="shortcode-hr" style="border: none; border-bottom: 1px solid #ccc; margin: 1em 0;">');
  
  return content;
}

/**
 * 简单的Markdown解析函数
 * 
 * @param {String} content Markdown文本
 * @return {String} 解析后的HTML
 */
const parseMarkdown = content => {
  // 解析标题
  content = content.replace(/^### (.*?)$/gm, '<h3>$1</h3>');
  content = content.replace(/^## (.*?)$/gm, '<h2>$1</h2>');
  content = content.replace(/^# (.*?)$/gm, '<h1>$1</h1>');
  
  // 解析粗体
  content = content.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
  
  // 解析斜体
  content = content.replace(/\*(.*?)\*/g, '<em>$1</em>');
  
  // 解析链接 [链接文字](链接地址)
  content = content.replace(/\[(.*?)\]\((.*?)\)/g, (match, text, url) => {
    // 确保链接可点击，添加data-url属性用于小程序内处理
    return `<a href="${url}" data-url="${url}" class="link">${text}</a>`;
  });
  
  // 解析无序列表
  content = content.replace(/(?:^|\n)- (.*?)(?:\n|$)/g, '\n<ul><li>$1</li></ul>\n');
  
  // 合并连续的无序列表项
  content = content.replace(/<\/ul>\s*<ul>/g, '');
  
  // 解析有序列表
  content = content.replace(/(?:^|\n)(\d+)\. (.*?)(?:\n|$)/g, '\n<ol start="$1"><li>$2</li></ol>\n');
  
  // 合并连续的有序列表项
  content = content.replace(/<\/ol>\s*<ol start="\d+">/g, '');
  
  // 解析图片 ![alt文字](图片地址)
  content = content.replace(/!\[(.*?)\]\((.*?)\)/g, (match, alt, src) => {
    // 添加data-src属性用于点击预览
    return `<img src="${src}" alt="${alt}" data-src="${src}" class="markdown-image">`;
  });
  
  // 解析引用
  content = content.replace(/(?:^|\n)> (.*?)(?:\n|$)/g, '\n<blockquote>$1</blockquote>\n');
  
  // 合并连续的引用
  content = content.replace(/<\/blockquote>\s*<blockquote>/g, '<br>');
  
  // 解析分隔线
  content = content.replace(/\n---\n/g, '\n<hr>\n');
  
  // 解析代码块
  content = content.replace(/```(.*?)```/gs, '<pre class="code-block"><code>$1</code></pre>');
  
  // 解析行内代码
  content = content.replace(/`(.*?)`/g, '<code class="inline-code">$1</code>');
  
  // 将换行转换为<br>标签
  content = content.replace(/\n/g, '<br>');
  
  return content;
}

/**
 * 完整处理内容文本，先解析短代码，再解析Markdown
 * 
 * @param {String} content 原始内容文本
 * @return {String} 处理后的HTML内容
 */
const formatContent = content => {
  if (!content) return '';
  
  // 首先应用短代码
  content = parseShortcodes(content);
  
  // 然后应用Markdown解析
  content = parseMarkdown(content);
  
  // 处理图片标签，确保它们在rich-text中正确显示
  // 修复后端插入的图片不显示问题
  content = content.replace(/<img[^>]*src=["']([^"']*)["'][^>]*>/gi, (match, src) => {
    // 处理图片URL，确保完整路径
    const fixedSrc = fixImageUrl(src);
    // 确保图片标签符合小程序rich-text的要求，并设置mode为widthFix以自动调整高度
    return `<img src="${fixedSrc}" data-src="${fixedSrc}" style="max-width:100%;width:100%;height:auto;display:block;margin:10px auto;" />`;
  });
  
  // 处理后端可能插入的其他格式的图片标签
  content = content.replace(/!\[([^\]]*)\]\(([^\)]*)\)/g, (match, alt, src) => {
    // 处理图片URL，确保完整路径
    const fixedSrc = fixImageUrl(src);
    return `<img src="${fixedSrc}" alt="${alt}" data-src="${fixedSrc}" style="max-width:100%;width:100%;height:auto;display:block;margin:10px auto;" />`;
  });
  
  // 确保所有图片标签都正确闭合
  content = content.replace(/<img([^>]*)(?!\/)>(?!\s*<\/img>)/gi, '<img$1 />');
  
  // 确保图片链接在小程序中可以正常显示
  content = content.replace(/<a[^>]*href=["']([^"']*)["'][^>]*>(.*?)<\/a>/gi, (match, href, innerContent) => {
    if (innerContent.includes('<img')) {
      // 如果链接包含图片，确保图片可以正常显示
      return innerContent.replace(/<img[^>]*src=["']([^"']*)["'][^>]*>/gi, (imgMatch, imgSrc) => {
        const fixedSrc = fixImageUrl(imgSrc);
        return `<img src="${fixedSrc}" data-src="${fixedSrc}" data-link-url="${href}" style="max-width:100%;width:100%;height:auto;display:block;margin:10px auto;" />`;
      });
    }
    return match;
  });
  
  return content;
}

/**
 * 根据图片路径获取优化版图片路径
 * 对于lxrwimg目录下的大图片，提供替代方案
 * 
 * @param {String} imagePath 原始图片路径
 * @return {String} 优化后的图片路径
 */
const getOptimizedImagePath = imagePath => {
  // 如果图片路径为空，直接返回默认头像
  if (!imagePath) return '/images/default-avatar.png';
  
  // 使用缓存避免重复处理同一图片
  if (!getOptimizedImagePath.cache) {
    getOptimizedImagePath.cache = {};
  }
  
  // 检查缓存中是否已存在处理结果
  if (getOptimizedImagePath.cache[imagePath]) {
    return getOptimizedImagePath.cache[imagePath];
  }
  
  let result = imagePath;
  
  // 检查是否是lxrwimg目录下的图片
  if (imagePath && imagePath.indexOf('lxrwimg') > -1) {
    // 大图片使用默认占位图替代
    if (imagePath.endsWith('流星超版：一方.jpg') || 
        imagePath.endsWith('凌云～竹子.jpg') || 
        imagePath.endsWith('༺ 孤 楓 ༻  （进群备注账号）.jpg')) {
      result = '/images/default-avatar.png'; // 使用默认图片替代
    }
  }
  
  // 缓存处理结果
  getOptimizedImagePath.cache[imagePath] = result;
  
  return result;
}

/**
 * 修复图片URL，确保完整路径
 * 
 * @param {String} url 原始图片URL
 * @return {String} 修复后的图片URL
 */
const fixImageUrl = url => {
  if (!url) return '';
  
  // 如果已经是完整URL，则直接返回
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url;
  }
  
  // 获取API基础URL（从app.js或配置中获取）
  const apiBaseUrl = getApp().globalData.apiBaseUrl || 'https://lxbl.online/BDLX';
  
  // 处理相对路径
  if (url.startsWith('/')) {
    // 保留开头的斜杠，因为某些API路径可能需要它
    return `${apiBaseUrl}${url}`;
  }
  
  // 处理uploads路径
  if (url.includes('uploads/')) {
    // 确保路径格式正确
    const uploadsIndex = url.indexOf('uploads/');
    url = url.substring(uploadsIndex);
    return `${apiBaseUrl}/${url}`;
  }
  
  // 其他情况，直接拼接基础URL
  return `${apiBaseUrl}/${url}`;
};

module.exports = {
  formatTime,
  formatNumber,
  showToast,
  showLoading,
  hideLoading,
  showConfirm,
  trim,
  parseShortcodes,
  parseMarkdown,
  formatContent,
  getOptimizedImagePath,
  parseDate,
  standardizeDate,
  fixImageUrl
} 