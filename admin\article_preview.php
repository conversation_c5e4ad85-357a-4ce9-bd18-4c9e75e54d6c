<?php
require_once 'config.php';
require_once 'includes/db.php';
require_once 'includes/utils.php';
require_once '../includes/formatting.php';

// 检查登录状态
Utils::checkLogin();

// 检查ID参数
if (!isset($_GET['id']) || empty($_GET['id'])) {
    die('缺少文章ID参数');
}

$article_id = (int)$_GET['id'];

// 获取文章数据
$sql = "SELECT * FROM articles WHERE id = {$article_id}";
$article = $db->getRow($sql);

if (!$article) {
    die('未找到指定文章');
}

// 先解析Markdown，再处理短代码
$content_html = Utils::parseMarkdown($article['content']);
$content_html = parse_shortcodes($content_html);

// 生成目录HTML
$toc_html = '';
if (preg_match_all('/<h([1-3])\s+id="(.*?)">(.*?)<\/h\1>/', $content_html, $matches, PREG_SET_ORDER)) {
    $toc_html = '<div class="article-toc"><h2>目录</h2><ul class="toc-list">';
    foreach ($matches as $match) {
        $level = $match[1];
        $id = $match[2];
        $title = strip_tags($match[3]);
        $indent = ($level - 1) * 20;
        $toc_html .= sprintf(
            '<li style="margin-left: %dpx;"><a href="#%s">%s</a></li>',
            $indent,
            $id,
            $title
        );
    }
    $toc_html .= '</ul></div>';
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $article['title']; ?> - 文章预览</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            position: relative;
        }
        
        .main-container {
            display: flex;
            max-width: 1200px;
            margin: 0 auto;
            position: relative;
            gap: 40px;
        }
        
        .article-container {
            flex: 1;
            max-width: 800px;
            padding: 20px;
        }
        
        .toc-container {
            width: 280px;
            position: sticky;
            top: 20px;
            height: calc(100vh - 40px);
            overflow-y: auto;
            padding: 20px;
            box-sizing: border-box;
            margin-top: 20px;
        }
        
        .article-header {
            border-bottom: 1px solid #e0e0e0;
            margin-bottom: 20px;
            padding-bottom: 10px;
        }
        
        .article-title {
            font-size: 24px;
            margin-bottom: 10px;
        }
        
        .article-summary {
            font-size: 16px;
            color: #555;
            margin-bottom: 15px;
            font-style: italic;
            border-left: 3px solid #3498db;
            padding-left: 10px;
        }
        
        .article-meta {
            color: #777;
            font-size: 14px;
            margin-bottom: 20px;
        }
        
        .article-content {
            line-height: 1.6;
        }
        
        /* 目录样式 */
        .article-toc {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
        }
        
        .article-toc h2 {
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 18px;
            color: #495057;
            padding-bottom: 10px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .toc-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .toc-list li {
            margin: 8px 0;
            line-height: 1.4;
            font-size: 14px;
        }
        
        .toc-list a {
            color: #495057;
            text-decoration: none;
            transition: color 0.2s;
            display: block;
            padding: 4px 0;
        }
        
        .toc-list a:hover {
            color: #007bff;
            text-decoration: none;
        }
        
        .article-content h1,
        .article-content h2,
        .article-content h3 {
            margin-top: 1.5em;
            margin-bottom: 0.8em;
            scroll-margin-top: 20px;
        }
        
        .article-content p {
            margin-bottom: 1.2em;
        }
        
        .article-content img {
            max-width: 100%;
            height: auto;
            display: block;
            margin: 20px auto;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .article-content hr,
        .article-content .markdown-hr {
            border: 0;
            height: 1px;
            background-color: #e0e0e0;
            margin: 2em 0;
            width: 100%;
        }
        
        .article-content a {
            color: #3498db;
            text-decoration: none;
        }
        
        .article-content a:hover {
            text-decoration: underline;
        }
        
        .preview-badge {
            display: inline-block;
            background-color: #e74c3c;
            color: white;
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 12px;
            margin-bottom: 20px;
        }
        
        .status-badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 12px;
            margin-left: 10px;
        }
        
        .status-published {
            background-color: #2ecc71;
            color: white;
        }
        
        .status-draft {
            background-color: #95a5a6;
            color: white;
        }
        
        /* 短代码样式 */
        .shortcode {
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
        }
        .warning-box {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
        }
        .info-box {
            background-color: #d1ecf1;
            border-left: 4px solid #17a2b8;
        }
        .tip-box {
            background-color: #d4edda;
            border-left: 4px solid #28a745;
        }
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #eee;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            font-family: monospace;
            overflow-x: auto;
        }
        .quote-block {
            border-left: 4px solid #6c757d;
            background-color: #f8f9fa;
            padding: 15px;
            margin: 15px 0;
            font-style: italic;
        }
        .shortcode-button {
            display: inline-block;
            padding: 8px 15px;
            margin: 5px 0;
            background-color: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            transition: background-color 0.3s;
        }
        .shortcode-button:hover {
            background-color: #2980b9;
            color: white;
            text-decoration: none;
        }
        .shortcode-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        .shortcode-table th,
        .shortcode-table td {
            border: 1px solid #dee2e6;
            padding: 8px 12px;
            text-align: left;
        }
        .shortcode-table th {
            background-color: #f8f9fa;
        }
        .table-responsive {
            overflow-x: auto;
        }
        .download-box {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px 15px;
            margin: 15px 0;
        }
        .download-link {
            display: flex;
            align-items: center;
            text-decoration: none;
            color: #3498db;
        }
        .download-link:hover {
            text-decoration: underline;
        }
        .download-icon {
            margin-right: 10px;
            font-size: 1.2em;
        }
        .download-filename {
            font-weight: bold;
            margin-right: 5px;
        }
        .file-size {
            color: #6c757d;
            font-size: 0.9em;
        }
        .inline-code {
            background-color: #f8f9fa;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 0.9em;
            border: 1px solid #eee;
        }
        .article-image {
            margin: 30px 0;
            text-align: center;
        }
        .image-caption {
            margin-top: 10px;
            color: #666;
            font-size: 0.9em;
            font-style: italic;
        }
        
        /* 返回顶部按钮样式 */
        .back-to-top {
            position: fixed;
            bottom: 100px;
            right: 30px;
            width: 40px;
            height: auto;
            padding: 15px 0;
            border-radius: 4px;
            background-color:rgb(53, 156, 225);
            color: white;
            text-align: center;
            cursor: pointer;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            z-index: 1000;
            font-size: 14px;
            writing-mode: vertical-lr;
            letter-spacing: 2px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .back-to-top span {
            transform: rotate(180deg);
            display: inline-block;
            margin-bottom: 5px;
        }
        
        .back-to-top:hover {
            background-color: #2980b9;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }
        
        .back-to-top.visible {
            opacity: 1;
            visibility: visible;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="article-container">
            <div class="preview-badge">预览模式</div>
            
            <div class="article-header">
                <?php if ($article['published']): ?>
                    <span class="status-badge status-published">已发布</span>
                <?php else: ?>
                    <span class="status-badge status-draft">草稿</span>
                <?php endif; ?>
                <h1 class="article-title"><?php echo $article['title']; ?></h1>
                <?php if (!empty($article['summary'])): ?>
                <div class="article-summary"><?php echo $article['summary']; ?></div>
                <?php endif; ?>
                <div class="article-meta">
                    <span>创建时间：<?php echo date('Y-m-d H:i:s', strtotime($article['create_time'])); ?></span>
                    <span>更新时间：<?php echo date('Y-m-d H:i:s', strtotime($article['update_time'])); ?></span>
                </div>
            </div>
            
            <div class="article-content">
                <?php echo $content_html; ?>
            </div>
            
            <?php if (!empty($article['file_path'])): ?>
                <div class="article-attachment">
                    <h3>附件</h3>
                    <p>
                        <a href="download.php?file=<?php echo $article['file_path']; ?>&type=article" target="_blank">
                            <?php echo $article['file_path']; ?>
                        </a>
                    </p>
                </div>
            <?php endif; ?>
            
            <div style="margin-top: 30px;">
                <a href="article_edit.php?id=<?php echo $article['id']; ?>" style="color: #3498db;">返回编辑</a>
            </div>
        </div>
        
        <div class="toc-container">
            <?php echo $toc_html; ?>
        </div>
    </div>
    
    <!-- 返回顶部按钮 -->
    <div class="back-to-top" title="返回顶部"><span>→</span>返回顶部</div>
    
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const backToTopButton = document.querySelector('.back-to-top');
        
        // 监听滚动事件
        window.addEventListener('scroll', function() {
            // 获取文档高度
            const docHeight = Math.max(
                document.body.scrollHeight,
                document.body.offsetHeight,
                document.documentElement.clientHeight,
                document.documentElement.scrollHeight,
                document.documentElement.offsetHeight
            );
            
            // 获取视窗高度
            const windowHeight = window.innerHeight;
            
            // 获取当前滚动位置
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            
            // 当滚动超过页面10%时显示按钮
            if (scrollTop > docHeight * 0.1) {
                backToTopButton.classList.add('visible');
            } else {
                backToTopButton.classList.remove('visible');
            }
        });
        
        // 点击返回顶部
        backToTopButton.addEventListener('click', function() {
            // 使用平滑滚动效果
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    });
    </script>
</body>
</html> 