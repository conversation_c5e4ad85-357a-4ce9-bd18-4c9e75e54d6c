<?php
require_once 'config.php';
require_once 'includes/db.php';
require_once 'includes/utils.php';

// 包含头部
include 'includes/header.php';
?>

<div class="main-content">
    <div class="container">
<?php

// 初始化变量
$error = '';
$success = '';
$category = [
    'id' => '',
    'name' => '',
    'description' => '',
    'is_default' => 0
];

// 处理默认分类设置
if (isset($_GET['set_default']) && !empty($_GET['set_default'])) {
    $default_id = (int)$_GET['set_default'];
    
    // 先将所有分类设为非默认
    $sql = "UPDATE article_categories SET is_default = 0";
    $db->query($sql);
    
    // 设置指定分类为默认
    $sql = "UPDATE article_categories SET is_default = 1 WHERE id = {$default_id}";
    if ($db->query($sql)) {
        $success = '默认分类设置成功';
    } else {
        $error = '默认分类设置失败';
    }
}

// 处理删除分类
if (isset($_GET['delete']) && !empty($_GET['delete'])) {
    $delete_id = (int)$_GET['delete'];
    
    // 检查是否为默认分类
    $sql = "SELECT is_default FROM article_categories WHERE id = {$delete_id}";
    $check = $db->getRow($sql);
    
    if ($check && $check['is_default']) {
        $error = '默认分类不能删除';
    } else {
        // 先将该分类下的文章移至默认分类
        $sql = "SELECT id FROM article_categories WHERE is_default = 1";
        $default = $db->getRow($sql);
        
        if ($default) {
            $default_id = $default['id'];
            
            // 更新文章分类
            $sql = "UPDATE articles SET category_id = {$default_id} WHERE category_id = {$delete_id}";
            $db->query($sql);
            
            // 删除分类
            $sql = "DELETE FROM article_categories WHERE id = {$delete_id}";
            if ($db->query($sql)) {
                $success = '分类删除成功，相关文章已移至默认分类';
            } else {
                $error = '分类删除失败';
            }
        } else {
            $error = '系统中没有默认分类，无法删除';
        }
    }
}

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // 获取表单数据
    $name = isset($_POST['name']) ? Utils::sanitizeInput($_POST['name']) : '';
    $description = isset($_POST['description']) ? Utils::sanitizeInput($_POST['description']) : '';
    $is_default = isset($_POST['is_default']) ? 1 : 0;
    
    if (empty($name)) {
        $error = '分类名称不能为空';
    } else {
        if (empty($error)) {
            $now = date('Y-m-d H:i:s');
            
            // 如果设为默认分类，先将其他分类设为非默认
            if ($is_default) {
                $sql = "UPDATE article_categories SET is_default = 0";
                $db->query($sql);
            }
            
            if (!empty($_POST['id'])) {
                // 更新现有分类
                $category_id = (int)$_POST['id'];
                $sql = "UPDATE article_categories SET 
                        name = '{$db->escape($name)}',
                        description = '{$db->escape($description)}',
                        is_default = {$is_default},
                        update_time = '{$now}'
                        WHERE id = {$category_id}";
                
                if ($db->query($sql)) {
                    $success = '分类更新成功';
                    
                    // 重置表单
                    $category = [
                        'id' => '',
                        'name' => '',
                        'description' => '',
                        'is_default' => 0
                    ];
                } else {
                    $error = '分类更新失败';
                }
            } else {
                // 创建新分类
                $category_id = $db->insert('article_categories', [
                    'name' => $name,
                    'description' => $description,
                    'is_default' => $is_default,
                    'create_time' => $now,
                    'update_time' => $now
                ]);
                
                if ($category_id) {
                    $success = '分类创建成功';
                    
                    // 重置表单
                    $category = [
                        'id' => '',
                        'name' => '',
                        'description' => '',
                        'is_default' => 0
                    ];
                } else {
                    $error = '分类创建失败';
                }
            }
        }
    }
}

// 检查是否是编辑现有分类
if (isset($_GET['edit']) && !empty($_GET['edit'])) {
    $category_id = (int)$_GET['edit'];
    
    // 获取分类数据
    $sql = "SELECT * FROM article_categories WHERE id = {$category_id}";
    $category_data = $db->getRow($sql);
    
    if ($category_data) {
        $category = $category_data;
    }
}

// 获取分类列表
$sql = "SELECT c.*, (SELECT COUNT(*) FROM articles WHERE category_id = c.id) as article_count 
        FROM article_categories c ORDER BY is_default DESC, name ASC";
$categories = $db->getRows($sql);
?>
<h1 class="page-title">文章分类管理</h1>

<?php if (!empty($error)): ?>
    <div class="alert alert-danger"><?php echo $error; ?></div>
<?php endif; ?>

<?php if (!empty($success)): ?>
    <div class="alert alert-success"><?php echo $success; ?></div>
<?php endif; ?>

<div class="row">
    <div class="col-md-4">
        <div class="card">
            <div class="card-title"><?php echo empty($category['id']) ? '新增分类' : '编辑分类'; ?></div>
            <form method="POST" action="article_categories.php">
                <input type="hidden" name="id" value="<?php echo $category['id']; ?>">
                
                <div class="form-group">
                    <label for="name" class="form-label">分类名称</label>
                    <input type="text" id="name" name="name" class="form-control" value="<?php echo $category['name']; ?>" required>
                </div>
                
                <div class="form-group">
                    <label for="description" class="form-label">分类描述</label>
                    <textarea id="description" name="description" class="form-control" rows="3"><?php echo $category['description']; ?></textarea>
                </div>
                
                <div class="form-group">
                    <div class="form-check">
                        <input type="checkbox" id="is_default" name="is_default" class="form-check-input" <?php echo $category['is_default'] ? 'checked' : ''; ?>>
                        <label for="is_default" class="form-check-label">设为默认分类</label>
                    </div>
                    <div class="form-text">默认分类会作为新文章的初始分类，且不能被删除</div>
                </div>
                
                <div class="form-group">
                    <button type="submit" class="btn btn-primary"><?php echo empty($category['id']) ? '创建分类' : '更新分类'; ?></button>
                    <?php if (!empty($category['id'])): ?>
                    <a href="article_categories.php" class="btn btn-secondary">取消编辑</a>
                    <?php endif; ?>
                </div>
            </form>
        </div>
    </div>
    
    <div class="col-md-8">
        <div class="card">
            <div class="card-title">分类列表</div>
            
            <?php if (empty($categories)): ?>
                <div class="alert alert-info">暂无分类</div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table data-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>名称</th>
                                <th>描述</th>
                                <th>文章数量</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($categories as $cat): ?>
                                <tr>
                                    <td><?php echo $cat['id']; ?></td>
                                    <td><?php echo $cat['name']; ?></td>
                                    <td><?php echo $cat['description']; ?></td>
                                    <td><?php echo $cat['article_count']; ?></td>
                                    <td>
                                        <?php if ($cat['is_default']): ?>
                                            <span class="badge badge-success">默认</span>
                                        <?php else: ?>
                                            <a href="article_categories.php?set_default=<?php echo $cat['id']; ?>" class="badge badge-secondary">设为默认</a>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <a href="article_categories.php?edit=<?php echo $cat['id']; ?>" class="btn btn-primary btn-sm">编辑</a>
                                        <?php if (!$cat['is_default']): ?>
                                            <a href="javascript:void(0);" onclick="confirmDelete('确定要删除此分类吗？相关文章将移至默认分类。', function() { window.location.href='article_categories.php?delete=<?php echo $cat['id']; ?>'; })" class="btn btn-danger btn-sm">删除</a>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<div class="card mt-3">
    <div class="card-title">分类管理说明</div>
    <p>文章分类用于对文章进行归类，便于管理和展示。您可以通过以下功能管理分类：</p>
    <ul>
        <li><strong>新增分类</strong>：创建新的文章分类</li>
        <li><strong>编辑分类</strong>：修改分类的名称和描述</li>
        <li><strong>设为默认</strong>：将某个分类设为默认分类，新建文章时会自动选择此分类</li>
        <li><strong>删除分类</strong>：删除不需要的分类，该分类下的文章会自动移至默认分类</li>
    </ul>
    <p>注意：默认分类不能被删除，请确保系统中始终有一个默认分类。</p>
</div>

<?php
// 包含底部
    </div><!-- 结束 .container -->
</div><!-- 结束 .main-content -->

<?php
include 'includes/footer.php';
?> 