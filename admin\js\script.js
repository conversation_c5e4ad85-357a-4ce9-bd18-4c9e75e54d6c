/**
 * BDLX后台管理系统JavaScript
 */

// 文档加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    initAlerts();
    initDataTable();
    initMarkdownEditor();
    initExcelEditor();
    initImagePreview();
});

// 提示框自动关闭
function initAlerts() {
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(function(alert) {
        setTimeout(function() {
            alert.style.opacity = '0';
            setTimeout(function() {
                alert.style.display = 'none';
            }, 500);
        }, 5000);
    });
}

// 数据表格初始化
function initDataTable() {
    const tables = document.querySelectorAll('.data-table');
    tables.forEach(function(table) {
        if (table.classList.contains('initialized')) {
            return;
        }
        
        // 添加排序功能
        const headers = table.querySelectorAll('th[data-sort]');
        headers.forEach(function(header) {
            header.addEventListener('click', function() {
                const sortKey = this.getAttribute('data-sort');
                const sortDirection = this.getAttribute('data-direction') || 'asc';
                
                // 清除所有表头的排序方向
                headers.forEach(h => h.removeAttribute('data-direction'));
                
                // 设置当前表头的排序方向
                this.setAttribute('data-direction', sortDirection === 'asc' ? 'desc' : 'asc');
                
                // 排序表格
                sortTable(table, sortKey, sortDirection);
            });
        });
        
        // 添加搜索功能
        const searchInput = document.querySelector('.table-search');
        if (searchInput) {
            searchInput.addEventListener('keyup', function() {
                const searchText = this.value.toLowerCase();
                const tableRows = table.querySelectorAll('tbody tr');
                
                tableRows.forEach(function(row) {
                    const cells = row.querySelectorAll('td');
                    let found = false;
                    
                    cells.forEach(function(cell) {
                        if (cell.textContent.toLowerCase().indexOf(searchText) > -1) {
                            found = true;
                        }
                    });
                    
                    row.style.display = found ? '' : 'none';
                });
            });
        }
        
        table.classList.add('initialized');
    });
}

// 排序表格
function sortTable(table, key, direction) {
    const rows = Array.from(table.querySelectorAll('tbody tr'));
    const index = Array.from(table.querySelectorAll('th')).findIndex(th => th.getAttribute('data-sort') === key);
    
    rows.sort(function(a, b) {
        const aValue = a.cells[index].textContent.trim();
        const bValue = b.cells[index].textContent.trim();
        
        // 检查是否为数字
        const aNum = parseFloat(aValue);
        const bNum = parseFloat(bValue);
        
        if (!isNaN(aNum) && !isNaN(bNum)) {
            return direction === 'asc' ? aNum - bNum : bNum - aNum;
        }
        
        // 字符串比较
        if (direction === 'asc') {
            return aValue.localeCompare(bValue, 'zh-CN');
        } else {
            return bValue.localeCompare(aValue, 'zh-CN');
        }
    });
    
    // 重新添加排序后的行
    const tbody = table.querySelector('tbody');
    rows.forEach(function(row) {
        tbody.appendChild(row);
    });
}

// Markdown编辑器
function initMarkdownEditor() {
    const markdownInput = document.querySelector('.markdown-editor-input');
    const markdownPreview = document.querySelector('.markdown-editor-preview');
    
    if (markdownInput && markdownPreview) {
        markdownInput.addEventListener('input', function() {
            const markdown = this.value;
            const html = parseMarkdown(markdown);
            markdownPreview.innerHTML = html;
        });
        
        // 初始渲染
        if (markdownInput.value) {
            markdownPreview.innerHTML = parseMarkdown(markdownInput.value);
        }
    }
}

// 简单的Markdown解析
function parseMarkdown(markdown) {
    if (!markdown) return '';
    
    // 标题
    markdown = markdown.replace(/^# (.*?)$/gm, '<h1>$1</h1>');
    markdown = markdown.replace(/^## (.*?)$/gm, '<h2>$1</h2>');
    markdown = markdown.replace(/^### (.*?)$/gm, '<h3>$1</h3>');
    
    // 粗体和斜体
    markdown = markdown.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
    markdown = markdown.replace(/\*(.*?)\*/g, '<em>$1</em>');
    
    // 列表
    markdown = markdown.replace(/^- (.*?)$/gm, '<li>$1</li>');
    markdown = markdown.replace(/<li>(.*?)<\/li>(\n<li>|$)/g, '<ul><li>$1</li>$2</ul>');
    
    // 链接
    markdown = markdown.replace(/\[(.*?)\]\((.*?)\)/g, '<a href="$2" target="_blank">$1</a>');
    
    // 段落
    markdown = markdown.replace(/^(?!<h|<ul|<\/ul|<li|<\/li|<a)(.*?)$/gm, '<p>$1</p>');
    
    // 空行
    markdown = markdown.replace(/(<\/[^>]+>)\n\n/g, '$1<br><br>');
    
    return markdown;
}

// Excel表格编辑
function initExcelEditor() {
    const excelTable = document.querySelector('.edit-table');
    
    if (excelTable) {
        // 添加行编辑功能
        const cells = excelTable.querySelectorAll('td[data-editable="true"]');
        cells.forEach(function(cell) {
            cell.addEventListener('click', function() {
                // 如果已经是编辑状态，不做处理
                if (this.querySelector('input')) {
                    return;
                }
                
                const value = this.textContent.trim();
                const input = document.createElement('input');
                
                // 设置输入框类型和值
                if (this.getAttribute('data-type') === 'number') {
                    input.type = 'number';
                    input.min = '0';
                } else {
                    input.type = 'text';
                }
                
                input.value = value;
                
                // 清空单元格并添加输入框
                this.textContent = '';
                this.appendChild(input);
                input.focus();
                
                // 失去焦点时保存
                input.addEventListener('blur', function() {
                    cell.textContent = this.value;
                    // 触发保存回调
                    const saveCallback = window[cell.getAttribute('data-save-callback')];
                    if (typeof saveCallback === 'function') {
                        saveCallback(cell.parentNode.getAttribute('data-id'), cell.getAttribute('data-field'), this.value);
                    }
                });
                
                // 按下回车键保存
                input.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        this.blur();
                    }
                });
            });
        });
    }
}

// 图片预览
function initImagePreview() {
    const imageInputs = document.querySelectorAll('input[type="file"][data-preview]');
    
    imageInputs.forEach(function(input) {
        input.addEventListener('change', function() {
            const previewId = this.getAttribute('data-preview');
            const preview = document.getElementById(previewId);
            
            if (preview && this.files && this.files[0]) {
                const reader = new FileReader();
                
                reader.onload = function(e) {
                    if (preview.tagName === 'IMG') {
                        preview.src = e.target.result;
                    } else {
                        const img = document.createElement('img');
                        img.src = e.target.result;
                        img.style.maxWidth = '100%';
                        preview.innerHTML = '';
                        preview.appendChild(img);
                    }
                };
                
                reader.readAsDataURL(this.files[0]);
            }
        });
    });
}

// 确认删除
function confirmDelete(message, callback) {
    if (confirm(message || '确定要删除此项吗？')) {
        if (typeof callback === 'function') {
            callback();
        }
    }
}

// 异步提交表单
function submitFormAsync(formId, callback) {
    const form = document.getElementById(formId);
    
    if (!form) return;
    
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(form);
        
        fetch(form.action, {
            method: form.method,
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (typeof callback === 'function') {
                callback(data);
            }
        })
        .catch(error => {
            console.error('表单提交错误:', error);
            alert('提交失败，请重试');
        });
    });
}

// 导出数据
function exportData(format) {
    window.location.href = 'export_data.php?format=' + format;
}

// 保存Excel编辑
function saveExcelEdit(gameId, rowId, field, value) {
    fetch('ajax_handler.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `action=save_excel_edit&game_id=${gameId}&row_id=${rowId}&field=${field}&value=${value}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            // 成功保存，可以显示提示
            showToast('保存成功', 'success');
        } else {
            showToast('保存失败：' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('保存数据错误:', error);
        showToast('保存失败，请重试', 'error');
    });
}

// 显示提示信息
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;
    
    document.body.appendChild(toast);
    
    // 显示提示
    setTimeout(() => {
        toast.classList.add('show');
    }, 10);
    
    // 3秒后隐藏
    setTimeout(() => {
        toast.classList.remove('show');
        // 动画结束后移除元素
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 500);
    }, 3000);
} 