// 引入API请求
const api = require('../../../utils/api')
const util = require('../../../utils/util')

// 支持的媒体格式
const MEDIA_TYPES = {
  // 视频格式
  VIDEO: {
    mp4: 'video/mp4',
    mov: 'video/quicktime',
    m4v: 'video/x-m4v',
    '3gp': 'video/3gpp',
    avi: 'video/x-msvideo',
    webm: 'video/webm'
  },
  // 音频格式
  AUDIO: {
    mp3: 'audio/mpeg',
    wav: 'audio/wav',
    ogg: 'audio/ogg',
    m4a: 'audio/x-m4a',
    aac: 'audio/aac'
  }
};

Page({
  data: {
    id: null,
    article: null,
    loading: true,
    isLiked: false,
    likeCount: 0,
    imageLoading: true,
    imageError: false,
    articleImages: [],
    // 视频相关状态
    isPlaying: false,
    videoError: false,
    audioError: false,
    pageReady: false,
    mediaList: [], // 存储媒体列表
    inlineMediaList: [], // 存储内联媒体列表
    contentSegments: [], // 存储内容分段
    currentAudio: null, // 当前播放的音频
    isAudioPlaying: false, // 音频播放状态
    audioDuration: 0, // 音频总时长
    audioCurrentTime: 0, // 音频当前播放时间
    audioProgress: 0, // 音频播放进度（0-100）
    formattedCurrentTime: '00:00', // 格式化后的当前时间
    formattedDuration: '00:00' // 格式化后的总时长
  },

  // 视频播放器实例
  videoContext: null,
  audioContext: null,

  onLoad: function (options) {
    if (options.id) {
      const id = options.id
      this.setData({ id })
      this.loadArticleDetail(id)
      this.checkLikeStatus(id)
    } else {
      util.showToast('参数错误')
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
    
    // 初始化视频播放器
    this.videoContext = wx.createVideoContext('articleVideo')
    // 初始化音频播放器
    this.audioContext = wx.createInnerAudioContext()
    
    // 设置音频事件监听
    this.setupAudioListeners()
  },
  
  // 页面显示时
  onShow: function() {
    // 确保底部操作栏正确显示
    setTimeout(() => {
      this.setData({
        pageReady: true
      })
    }, 300) // 延迟显示底部栏，确保内容先加载完成
    
    // 添加图片点击预览功能
    this.setupImagePreview();
  },
  
  // 页面滚动事件
  onPageScroll: function(e) {
    // 确保滚动时内容不被底部栏遮挡
    if (e.scrollTop > 100 && !this.data.pageReady) {
      this.setData({
        pageReady: true
      })
    }
  },
  
  // 加载文章详情完成后
  onReady: function() {
    // 确保内容区域正确显示
    setTimeout(() => {
      wx.createSelectorQuery()
        .select('.article-content')
        .boundingClientRect(rect => {
          if (rect && rect.height > 0) {
            // 内容加载完成，确保可见
            this.setData({
              contentLoaded: true
            })
          }
        })
        .exec()
    }, 500)
  },
  
  // 提取媒体链接并处理内容
  extractMediaLinks: function(content) {
    if (!content) return { mediaList: [], processedContent: '', inlineMediaList: [] };

    const mediaList = [];
    const inlineMediaList = [];
    const lines = content.split('\n');
    const processedLines = [];

    // 媒体文件扩展名正则表达式 - 更宽泛的匹配
    const mediaRegex = /\.(mp4|mp3|wav|ogg|m4a|aac|jpg|jpeg|png|gif|webp)(\?.*)?$/i;
    // URL编码的文件名正则表达式
    const encodedMediaRegex = /%20.*\.(mp3|mp4|wav|ogg|m4a|aac)(\?.*)?$/i;
    // 检查是否包含媒体文件名的正则表达式
    const containsMediaRegex = /\.(mp4|mp3|wav|ogg|m4a|aac|jpg|jpeg|png|gif|webp)/i;

    lines.forEach((line, index) => {
      const trimmedLine = line.trim();

      // 检查是否是媒体链接
      if (mediaRegex.test(trimmedLine) || encodedMediaRegex.test(trimmedLine) || containsMediaRegex.test(trimmedLine)) {
        // 提取文件扩展名，处理URL参数
        let ext = '';
        const urlParts = trimmedLine.split('?')[0]; // 移除URL参数
        const pathParts = urlParts.split('.');
        if (pathParts.length > 1) {
          ext = pathParts[pathParts.length - 1].toLowerCase();
        }

        // 如果无法从URL提取扩展名，尝试从内容中推断
        if (!ext && trimmedLine.toLowerCase().includes('mp4')) ext = 'mp4';
        else if (!ext && trimmedLine.toLowerCase().includes('mp3')) ext = 'mp3';
        else if (!ext && trimmedLine.toLowerCase().includes('wav')) ext = 'wav';
        else if (!ext && trimmedLine.toLowerCase().includes('ogg')) ext = 'ogg';
        else if (!ext && trimmedLine.toLowerCase().includes('m4a')) ext = 'm4a';
        else if (!ext && trimmedLine.toLowerCase().includes('aac')) ext = 'aac';

        // 如果仍然无法确定扩展名，作为普通文本处理
        if (!ext) {
          processedLines.push(trimmedLine);
        } else {

        const mediaId = `media_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

        // 视频文件
        if (['mp4', 'webm', 'mov', 'm4v', '3gp', 'avi'].includes(ext)) {
          const mediaItem = {
            id: mediaId,
            type: 'video',
            url: trimmedLine,
            format: ext,
            mimeType: MEDIA_TYPES.VIDEO[ext],
            position: index
          };
          mediaList.push(mediaItem);
          inlineMediaList.push(mediaItem);

          // 在内容中插入视频占位符，使用特殊标记
          processedLines.push(`[MEDIA:VIDEO:${mediaId}:${trimmedLine}:${ext.toUpperCase()}]`);
        }
        // 音频文件
        else if (['mp3', 'wav', 'ogg', 'm4a', 'aac'].includes(ext)) {
          const mediaItem = {
            id: mediaId,
            type: 'audio',
            url: trimmedLine,
            format: ext,
            mimeType: MEDIA_TYPES.AUDIO[ext],
            position: index
          };
          mediaList.push(mediaItem);
          inlineMediaList.push(mediaItem);

          // 在内容中插入音频占位符
          processedLines.push(`[MEDIA:AUDIO:${mediaId}:${trimmedLine}:${ext.toUpperCase()}]`);
        }
        // 图片文件
        else if (['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(ext)) {
          // 检查是否是重复的图片URL
          if (!mediaList.some(item => item.url === trimmedLine)) {
            const mediaItem = {
              id: mediaId,
              type: 'image',
              url: trimmedLine,
              format: ext, // 为图片也添加format属性
              position: index
            };
            mediaList.push(mediaItem);
            inlineMediaList.push(mediaItem);

            // 在内容中插入图片占位符
            processedLines.push(`[MEDIA:IMAGE:${mediaId}:${trimmedLine}:${ext.toUpperCase()}]`);
          }
        }
        }
      } else if (trimmedLine) {
        // 如果不是媒体链接且不是空行，则保留该行
        processedLines.push(trimmedLine);
      } else {
        // 保留空行
        processedLines.push('');
      }
    });

    return {
      mediaList,
      inlineMediaList,
      processedContent: processedLines.join('\n')
    };
  },

  // 创建内容分段，将媒体播放器插入到正确位置
  createContentSegments: function(content, mediaList) {
    if (!content) return [{ type: 'text', content: '' }];
    if (!mediaList.length) return [{ type: 'text', content: content }];

    const segments = [];
    let currentContent = content;

    // 按位置排序媒体列表
    const sortedMedia = [...mediaList].sort((a, b) => a.position - b.position);

    sortedMedia.forEach((media) => {
      const placeholder = `[MEDIA:${media.type.toUpperCase()}:${media.id}:${media.url}:${media.format ? media.format.toUpperCase() : ''}]`;

      const placeholderIndex = currentContent.indexOf(placeholder);
      if (placeholderIndex !== -1) {
        // 添加占位符前的文本内容
        const beforeText = currentContent.substring(0, placeholderIndex);
        if (beforeText.trim()) {
          segments.push({
            type: 'text',
            content: beforeText
          });
        }

        // 添加媒体段
        if (media.type === 'video') {
          segments.push({
            type: 'video',
            url: media.url,
            format: media.format || 'mp4',
            id: media.id
          });
        } else if (media.type === 'audio') {
          segments.push({
            type: 'audio',
            url: media.url,
            format: media.format || 'mp3',
            id: media.id
          });
        } else if (media.type === 'image') {
          // 图片仍然在rich-text中处理，不需要单独的段落
          // 将图片作为HTML内容添加到文本段中
          const imageHtml = `<img src="${media.url}" style="width: 100%; border-radius: 8px; margin: 10px 0; box-shadow: 0 2px 8px rgba(0,0,0,0.1); display: block;" />`;
          segments.push({
            type: 'text',
            content: imageHtml
          });
        }

        // 更新当前内容为占位符后的部分
        currentContent = currentContent.substring(placeholderIndex + placeholder.length);
      }
    });

    // 添加剩余的文本内容
    if (currentContent.trim()) {
      segments.push({
        type: 'text',
        content: currentContent
      });
    }

    return segments;
  },
  
  // 加载文章详情
  loadArticleDetail: function(id) {
    util.showLoading();
    api.getArticle(id)
      .then(article => {
        if (article && article.content) {
          // 先提取媒体链接
          const { mediaList, inlineMediaList, processedContent } = this.extractMediaLinks(article.content);

          // 处理Markdown和短代码
          let formattedContent = util.formatContent(processedContent);

          // 创建内容分段，将媒体播放器插入到正确位置
          const contentSegments = this.createContentSegments(formattedContent, inlineMediaList);

          // 不再设置主视频，因为视频已经嵌入在内容中
          // 清除video_url以避免重复显示
          article.video_url = null;

          this.setData({
            article,
            mediaList,
            inlineMediaList,
            contentSegments,
            loading: false,
            likeCount: article.like_count || 0
          });

          // 处理文章内的图片预览
          this.setupImagePreview();
        }
        
        wx.setNavigationBarTitle({
          title: article.title || '文章详情'
        });
        
        util.hideLoading();
      })
      .catch(err => {
        console.error('加载文章详情失败:', err);
        util.hideLoading();
        util.showToast('获取文章详情失败');
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      });
  },
  
  // 检查点赞状态
  checkLikeStatus: function(id) {
    const likedArticles = wx.getStorageSync('likedArticles') || []
    const isLiked = likedArticles.includes(id.toString())
    this.setData({ isLiked })
  },
  
  // 处理点赞操作
  handleLike: function() {
    const { id, isLiked, likeCount } = this.data
    if (!id) return
    
    if (isLiked) {
      // 取消点赞
      this.setData({
        isLiked: false,
        likeCount: Math.max(0, likeCount - 1)
      })
      
      // 本地存储更新
      let likedArticles = wx.getStorageSync('likedArticles') || []
      likedArticles = likedArticles.filter(item => item !== id.toString())
      wx.setStorageSync('likedArticles', likedArticles)
      
      // 发送请求
      api.unlikeArticle(id)
        .catch(err => {
          // 请求失败恢复状态
          this.setData({
            isLiked: true,
            likeCount: likeCount
          })
          util.showToast('操作失败，请重试')
        })
    } else {
      // 添加点赞
      this.setData({
        isLiked: true,
        likeCount: likeCount + 1
      })
      
      // 本地存储更新
      const likedArticles = wx.getStorageSync('likedArticles') || []
      if (!likedArticles.includes(id.toString())) {
        likedArticles.push(id.toString())
        wx.setStorageSync('likedArticles', likedArticles)
      }
      
      // 发送请求
      api.likeArticle(id)
        .catch(err => {
          // 请求失败恢复状态
          this.setData({
            isLiked: false,
            likeCount: Math.max(0, likeCount)
          })
          util.showToast('操作失败，请重试')
        })
    }
  },

  handleImageError(e) {
    console.error('图片加载失败:', e)
    this.setData({
      imageError: true,
      imageLoading: false
    })
  },

  handleImageLoad() {
    this.setData({
      imageLoading: false,
      imageError: false
    })
  },

  // 设置图片预览功能
  setupImagePreview: function() {
    // 延迟执行，确保内容已渲染
    setTimeout(() => {
      const query = wx.createSelectorQuery();
      query.selectAll('.article-content rich-text img, .media-list .media-image').boundingClientRect(res => {
        if (res && res.length) {
          const imageUrls = res.map(item => item.dataset.src || item.src).filter(Boolean);
          this.setData({ imageUrls });
        }
      }).exec();
    }, 1000);
  },
  
  // 处理图片点击预览
  previewImage: function(e) {
    const url = e.currentTarget.dataset.url;
    const urls = this.data.imageUrls || [url];
    wx.previewImage({
      current: url,
      urls: urls
    });
  },

  // 视频播放事件处理
  onVideoPlay: function() {
    this.setData({ isPlaying: true });
  },

  onVideoPause: function() {
    this.setData({ isPlaying: false });
  },

  onVideoError: function(e) {
    console.error('视频播放错误:', e);
    this.setData({ 
      videoError: true,
      isPlaying: false
    });
    wx.showToast({
      title: '视频加载失败',
      icon: 'none'
    });
  },

  onVideoEnd: function() {
    this.setData({ isPlaying: false });
  },

  // 格式化时间函数
  formatTime: function(seconds) {
    if (isNaN(seconds) || seconds === null) return '00:00';
    
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    
    const formattedMinutes = String(minutes).padStart(2, '0');
    const formattedSeconds = String(remainingSeconds).padStart(2, '0');
    
    return `${formattedMinutes}:${formattedSeconds}`;
  },

  // 设置音频事件监听
  setupAudioListeners: function() {
    const audio = this.audioContext;

    audio.onPlay(() => {
      console.log('音频开始播放');
      this.setData({
        isAudioPlaying: true,
        audioError: false
      });
    });

    audio.onPause(() => {
      console.log('音频暂停播放');
      this.setData({ isAudioPlaying: false });
    });

    audio.onTimeUpdate(() => {
      if (audio.duration > 0) {
        const progress = (audio.currentTime / audio.duration) * 100;
        this.setData({
          audioCurrentTime: audio.currentTime,
          audioDuration: audio.duration,
          audioProgress: progress || 0,
          formattedCurrentTime: this.formatTime(audio.currentTime),
          formattedDuration: this.formatTime(audio.duration)
        });
      }
    });

    audio.onEnded(() => {
      console.log('音频播放结束');
      this.setData({
        isAudioPlaying: false,
        audioProgress: 0,
        audioCurrentTime: 0,
        formattedCurrentTime: '00:00'
      });
    });

    audio.onError((err) => {
      console.error('音频播放错误:', err);
      this.setData({
        audioError: true,
        isAudioPlaying: false,
        currentAudio: null
      });
      wx.showToast({
        title: '音频播放失败，请检查网络或文件格式',
        icon: 'none',
        duration: 3000
      });
    });

    audio.onCanplay(() => {
      console.log('音频可以播放');
    });

    audio.onWaiting(() => {
      console.log('音频缓冲中...');
    });
  },

  // 处理音频播放控制
  handleAudioControl: function(e) {
    const audioUrl = e.currentTarget.dataset.url;
    
    // 如果点击的是当前播放的音频
    if (this.data.currentAudio === audioUrl) {
      if (this.data.isAudioPlaying) {
        this.audioContext.pause();
      } else {
        this.audioContext.play();
      }
      return;
    }
    
    // 如果是新的音频
    this.setData({ currentAudio: audioUrl });
    this.audioContext.stop();
    this.audioContext.src = audioUrl;
    this.audioContext.play();
  },

  // 处理音频进度条点击
  handleAudioSeek: function(e) {
    const { value } = e.detail;
    const seekTime = (value / 100) * this.data.audioDuration;
    this.audioContext.seek(seekTime);
  },

  // 处理文章内容点击事件（用于媒体交互）
  handleContentTap: function(e) {
    // 现在媒体播放器直接嵌入在内容中，不需要特殊处理
  },

  // 停止音频播放
  stopAudio: function() {
    if (this.audioContext) {
      this.audioContext.stop();
      this.setData({
        currentAudio: null,
        isAudioPlaying: false,
        audioProgress: 0,
        audioCurrentTime: 0,
        formattedCurrentTime: '00:00'
      });
    }
  },

  // 全局音频播放方法（供HTML中调用）
  playAudioGlobal: function(url) {
    this.handleAudioControl({ currentTarget: { dataset: { url: url } } });
  },

  // 全局图片预览方法（供HTML中调用）
  previewImageGlobal: function(url) {
    wx.previewImage({
      current: url,
      urls: [url]
    });
  },



  // 页面隐藏时暂停播放
  onHide: function() {
    if (this.audioContext && this.data.isAudioPlaying) {
      this.audioContext.pause();
    }
  },

  // 页面卸载时释放资源
  onUnload: function() {
    if (this.audioContext) {
      this.audioContext.destroy();
    }
  },

  // 分享功能
  onShareAppMessage: function () {
    return {
      title: this.data.article.title,
      path: '/pages/articles/article-detail/article-detail?id=' + this.data.id
    }
  },
  // 分享到朋友圈
  onShareTimeline: function () {
    return {
      title: this.data.article.title,
      path: '/pages/articles/article-detail/article-detail?id=' + this.data.id
    }
  },



}) 