<?php
require_once 'config.php';
require_once 'includes/db.php';
require_once 'includes/utils.php';

// 检查登录状态
Utils::checkLogin();

// 检查参数
if (!isset($_GET['id']) || !isset($_GET['type'])) {
    $_SESSION['message'] = '缺少必要参数';
    $_SESSION['message_type'] = 'danger';
    header('Location: index.php');
    exit;
}

$id = (int)$_GET['id'];
$type = Utils::sanitizeInput($_GET['type']);

switch ($type) {
    // 删除游戏数据
    case 'game':
        // 获取游戏数据，用于删除文件
        $sql = "SELECT excel_file, image_file FROM games WHERE id = {$id}";
        $game = $db->getRow($sql);
        
        if (!$game) {
            $_SESSION['message'] = '游戏数据不存在';
            $_SESSION['message_type'] = 'danger';
        } else {
            // 删除游戏记录（级联删除会自动删除相关记录）
            $sql = "DELETE FROM games WHERE id = {$id}";
            if ($db->query($sql)) {
                // 删除文件
                Utils::deleteFile(EXCEL_DIR . $game['excel_file']);
                Utils::deleteFile(IMAGES_DIR . $game['image_file']);
                
                $_SESSION['message'] = '删除成功';
                $_SESSION['message_type'] = 'success';
                
                // 记录活动
                Utils::logActivity('删除数据', "删除游戏数据：ID {$id}");
            } else {
                $_SESSION['message'] = '删除失败';
                $_SESSION['message_type'] = 'danger';
            }
        }
        
        header('Location: index.php');
        break;
    
    // 删除文章
    case 'article':
        // 获取文章信息，用于删除文件
        $sql = "SELECT file_path FROM articles WHERE id = {$id}";
        $article = $db->getRow($sql);
        
        $sql = "DELETE FROM articles WHERE id = {$id}";
        if ($db->query($sql)) {
            // 如果有关联文件，删除文件
            if ($article && !empty($article['file_path'])) {
                Utils::deleteFile(ARTICLES_DIR . $article['file_path']);
            }
            
            $_SESSION['message'] = '文章删除成功';
            $_SESSION['message_type'] = 'success';
            
            // 记录活动
            Utils::logActivity('删除数据', "删除文章：ID {$id}");
        } else {
            $_SESSION['message'] = '文章删除失败';
            $_SESSION['message_type'] = 'danger';
        }
        
        header('Location: articles.php');
        break;
    
    // 删除公告
    case 'announcement':
        // 获取公告信息，用于删除文件
        $sql = "SELECT file_path FROM announcements WHERE id = {$id}";
        $announcement = $db->getRow($sql);
        
        $sql = "DELETE FROM announcements WHERE id = {$id}";
        if ($db->query($sql)) {
            // 如果有关联文件，删除文件
            if ($announcement && !empty($announcement['file_path'])) {
                Utils::deleteFile(ANNOUNCEMENTS_DIR . $announcement['file_path']);
            }
            
            $_SESSION['message'] = '公告删除成功';
            $_SESSION['message_type'] = 'success';
            
            // 记录活动
            Utils::logActivity('删除数据', "删除公告：ID {$id}");
        } else {
            $_SESSION['message'] = '公告删除失败';
            $_SESSION['message_type'] = 'danger';
        }
        
        header('Location: announcements.php');
        break;
    
    // 默认：未知类型
    default:
        $_SESSION['message'] = '未知删除类型';
        $_SESSION['message_type'] = 'danger';
        header('Location: index.php');
        break;
}

// 关闭数据库连接
$db->close(); 