// API请求封装
const app = getApp()
const util = require('./util.js')
const config = require('./config')

/**
 * 处理API返回的数据，统一处理日期格式
 * @param {Object|Array} data - API返回的数据
 * @returns {Object|Array} - 处理后的数据
 */
const processApiData = (data) => {
  if (!data) return data;
  
  // 处理数组
  if (Array.isArray(data)) {
    return data.map(item => processApiData(item));
  }
  
  // 处理对象
  if (typeof data === 'object') {
    const result = {...data};
    
    // 处理对象中可能的日期字段
    const dateFields = ['created_at', 'updated_at', 'create_time', 'update_time', 
                       'publish_time', 'publish_date', 'date', 'time', 'start_time', 'end_time'];
    
    dateFields.forEach(field => {
      if (result[field] && typeof result[field] === 'string') {
        // 将日期字符串转换为标准格式
        try {
          // 如果是日期格式的字符串，转换为标准的ISO格式
          if (result[field].match(/^\d{4}[-\/]\d{1,2}[-\/]\d{1,2}/) || 
              result[field].match(/^\d{4}[-\/]\d{1,2}[-\/]\d{1,2}\s\d{1,2}:\d{1,2}(:\d{1,2})?/)) {
            result[field] = util.standardizeDate(result[field]);
          }
        } catch (e) {
          console.error(`处理日期字段[${field}]出错:`, e);
        }
      }
    });
    
    // 递归处理嵌套对象
    Object.keys(result).forEach(key => {
      if (result[key] && typeof result[key] === 'object') {
        result[key] = processApiData(result[key]);
      }
    });
    
    return result;
  }
  
  return data;
};

/**
 * 封装请求API的函数
 * @param {String} path - API路径，例如 'players'
 * @param {Object} params - 请求参数
 * @param {String} method - 请求方法，默认GET
 * @returns {Promise} - 返回Promise对象
 */
const request = (path, params = {}, method = 'GET') => {
  const { apiBaseUrl, apiKey } = app.globalData
  
  // 合并参数
  const requestParams = {
    ...params,
    api_key: apiKey,
    path: path
  }
  
  // 构建URL，GET请求拼接参数
  let url = apiBaseUrl
  if (method === 'GET') {
    const paramsArray = []
    Object.keys(requestParams).forEach(key => {
      paramsArray.push(`${key}=${encodeURIComponent(requestParams[key])}`)
    })
    url = `${url}?${paramsArray.join('&')}`
  }
  
  // 设置最大重试次数
  const maxRetries = 3
  let retryCount = 0
  
  // 重试函数
  const executeRequest = () => {
    return new Promise((resolve, reject) => {
      wx.request({
        url,
        method,
        data: method !== 'GET' ? requestParams : {},
        header: {
          'content-type': 'application/json'
        },
        success: res => {
          if (res.statusCode === 200) {
            if (res.data.status === 'success') {
              // 处理返回数据中的日期格式，确保iOS兼容性
              const processedData = processApiData(res.data.data);
              resolve(processedData)
            } else {
              const errorMsg = res.data.message || '请求失败'
              console.error(`API错误: ${errorMsg}`, res.data)
              reject(errorMsg)
            }
          } else {
            console.error(`HTTP错误: ${res.statusCode}`, res)
            reject(`请求失败: ${res.statusCode}`)
          }
        },
        fail: err => {
          console.error('请求失败:', err)
          if (retryCount < maxRetries) {
            retryCount++
            console.log(`重试请求(${retryCount}/${maxRetries}): ${path}`)
            // 使用 Promise 替代 setTimeout
            return new Promise(r => setTimeout(r, Math.pow(2, retryCount) * 1000))
              .then(() => executeRequest())
              .then(resolve)
              .catch(reject)
          } else {
            const error = new Error('网络错误，请检查网络连接')
            error.originalError = err
            reject(error)
          }
        }
      })
    })
  }
  
  return executeRequest()
}

// 获取玩家游戏场次列表
function getPlayerGames(nickname, params = {}) {
  return request('player_games', { nickname, ...params })
}

// 获取玩家封禁状态
function getPlayerBanStatus(nickname) {
  return request('player_ban_status', { nickname });
}

// API方法
module.exports = {
  // 获取所有玩家列表
  getPlayers: (params = {}) => {
    return request('players', {
      offset: params.offset || 0,
      limit: params.limit || 20,
      ...params
    })
  },
  
  // 获取特定玩家详情
  getPlayer: (nickname) => {
    return request('player', { nickname })
  },
  
  // 获取特定玩家的游戏场次数量
  getPlayerGameCount: (nickname) => {
    return request('player_game_count', { nickname })
  },
  
  // 获取玩家详细数据（Impact影响力、KPR、DPR）
  getPlayerDetails: (nickname, params = {}) => {
    return request('player_details', { nickname, ...params })
  },
  
  // 获取游戏列表
  getGames: () => {
    return request('games')
  },
  
  // 获取特定游戏详情
  getGame: (id) => {
    return request('game', { id })
  },
  
  // 获取军衔统计
  getRanks: () => {
    return request('ranks')
  },
  
  // 获取公告列表
  getAnnouncements: () => {
    return request('announcements')
  },
  
  // 获取特定公告详情
  getAnnouncement: (id) => {
    return request('announcement', { id })
  },

  // 获取文章列表
  getArticles: (params = {}) => {
    return request('articles', params)
  },
  
  // 获取特定文章详情
  getArticle: (id) => {
    return request('article', { id })
  },
  
  // 获取文章分类列表
  getArticleCategories: () => {
    return request('article_categories')
  },
  
  // 获取指定分类的文章列表
  getArticlesByCategory: (categoryId, params = {}) => {
    return request('articles_by_category', { 
      category_id: categoryId,
      ...params 
    })
  },
  
  // 点赞文章
  likeArticle: (id) => {
    return request('like_article', { id }, 'POST')
  },
  
  // 取消点赞文章
  unlikeArticle: (id) => {
    return request('unlike_article', { id }, 'POST')
  },
  
  

  // 获取玩家游戏场次列表
  getPlayerGames: getPlayerGames,
  
  // 搜索玩家（模糊匹配）
  searchPlayers: (keyword, params = {}) => {
    return request('search_players', { 
      keyword,
      offset: params.offset || 0,
      limit: params.limit || 20,
      ...params
    })
  },
  
  // 获取玩家统计数据（胜率和KD值）
  getPlayerStats: (nickname) => {
    return request('player_stats', { nickname })
  },

  // 批量获取玩家统计数据（胜率和KD值）
  getPlayersStatsBatch: (nicknames) => {
    if (!Array.isArray(nicknames) || nicknames.length === 0) {
      return Promise.resolve({});
    }
    
    // 限制每批次最多50个玩家
    const MAX_BATCH_SIZE = 50;
    
    // 如果玩家数量不超过最大限制，直接请求
    if (nicknames.length <= MAX_BATCH_SIZE) {
      return requestPlayerStatsBatch(nicknames);
    }
    
    // 玩家数量超过限制，分批请求
    console.log(`玩家数量(${nicknames.length})超过批量API限制(${MAX_BATCH_SIZE})，分批请求`);
    
    // 将玩家列表分组
    const batches = [];
    for (let i = 0; i < nicknames.length; i += MAX_BATCH_SIZE) {
      batches.push(nicknames.slice(i, i + MAX_BATCH_SIZE));
    }
    
    // 依次处理每一批
    return new Promise((resolve) => {
      // 合并结果的对象
      let mergedResults = {};
      
      // 递归处理每一批
      const processBatch = (batchIndex) => {
        if (batchIndex >= batches.length) {
          // 所有批次处理完成，返回合并的结果
          return resolve(mergedResults);
        }
        
        // 处理当前批次
        requestPlayerStatsBatch(batches[batchIndex])
          .then(result => {
            // 合并结果
            mergedResults = { ...mergedResults, ...result };
            
            // 处理下一批
            processBatch(batchIndex + 1);
          })
          .catch(err => {
            console.error(`处理第${batchIndex + 1}批玩家数据失败:`, err);
            // 继续处理下一批，不中断整个流程
            processBatch(batchIndex + 1);
          });
      };
      
      // 开始处理第一批
      processBatch(0);
    });
  },

  // 获取玩家封禁状态
  getPlayerBanStatus: getPlayerBanStatus,

  // 获取玩家游戏场次图片
  getPlayerGameImages: (nickname) => {
    return request('player_game_images', { nickname })
  },

  // 直接通过游戏ID获取单个游戏图片
  getGameImage: (gameId) => {
    return request('game_image', { game_id: gameId })
  },

  // 获取特定游戏类型的玩家数据
  getGameTypeStatus: (nickname, gameType) => {
    // 转换游戏类型参数
    const type = gameType === 'assassination' ? 1 : 
                gameType === 'deathmatch' ? 2 : 
                gameType === 'boss' ? 3: 0;
                
    return request('player_type_stats', { 
      nickname,
      type 
    });
  },

  // 获取玩家在特定游戏类型下的详细数据
  getGameTypePlayerDetails: (nickname, gameType) => {
    return request('game_type_player_details', { 
      nickname: nickname,
      type: gameType 
    })
  },
  
  // 获取随机luckpost（直接请求php接口）
  getLuckPost: () => {
    return request('luckpost');
  }
}

// 在文件末尾添加辅助函数
// 请求单批次玩家统计数据的辅助函数
function requestPlayerStatsBatch(nicknames) {
  return request('player_stats_batch', { nicknames: nicknames.join(',') })
    .then(result => {
      // 处理返回的结果，将found_players数组转换为以nickname为key的对象
      if (result && Array.isArray(result.found_players)) {
        const statsMap = {};
        result.found_players.forEach(player => {
          if (player && player.nickname) {
            statsMap[player.nickname] = {
              kd: player.kd,
              win_rate: player.win_rate,
              total_kills: player.total_kills,
              total_deaths: player.total_deaths,
              total_wins: player.total_wins,
              total_losses: player.total_losses,
              player_rank: player.player_rank
            };
          }
        });
        return statsMap;
      }
      return result || {};
    })
    .catch(err => {
      console.error('批量获取玩家统计数据失败:', err);
      return {}; // 失败时返回空对象
    });
}