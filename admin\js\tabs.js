document.addEventListener('DOMContentLoaded', function() {
    // 初始化标签切换
    function initTabs() {
        const tabLinks = document.querySelectorAll('.tab-item');
        const tabContents = document.querySelectorAll('.tab-content');
        
        // 检查URL中的active_tab参数
        const urlParams = new URLSearchParams(window.location.search);
        const activeTabId = urlParams.get('active_tab') || tabLinks[0]?.getAttribute('href')?.substring(1);
        
        // 激活默认标签
        if (activeTabId) {
            const activeTab = document.getElementById(activeTabId);
            if (activeTab) {
                tabContents.forEach(content => content.classList.remove('active'));
                tabLinks.forEach(link => link.classList.remove('active'));
                
                activeTab.classList.add('active');
                document.querySelector(`[href="#${activeTabId}"]`)?.classList.add('active');
            }
        }
        
        // 添加点击事件处理
        tabLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                
                const targetId = this.getAttribute('href').substring(1);
                const targetTab = document.getElementById(targetId);
                
                if (targetTab) {
                    // 移除所有活动状态
                    tabContents.forEach(content => content.classList.remove('active'));
                    tabLinks.forEach(link => link.classList.remove('active'));
                    
                    // 激活目标标签
                    targetTab.classList.add('active');
                    this.classList.add('active');
                    
                    // 更新URL参数
                    const url = new URL(window.location.href);
                    url.searchParams.set('active_tab', targetId);
                    window.history.pushState({}, '', url);
                }
            });
        });
    }
    
    // 初始化标签
    initTabs();
});