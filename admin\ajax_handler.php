<?php
require_once 'config.php';
require_once 'includes/db.php';
require_once 'includes/utils.php';

// 检查用户是否已登录且为管理员
Utils::checkLogin();

// 设置响应头
header('Content-Type: application/json');

// 获取请求的操作
$action = isset($_POST['action']) ? $_POST['action'] : (isset($_GET['action']) ? $_GET['action'] : '');
$response = ['status' => 'error', 'message' => '未知操作'];

// 根据不同操作执行不同的处理
switch ($action) {
    // 获取文章列表
    case 'get_article_list':
        $articles = $db->getRows("SELECT id, title FROM articles ORDER BY create_time DESC");
        $response = [
            'status' => 'success',
            'items' => $articles
        ];
        break;
        
    // 获取公告列表
    case 'get_announcement_list':
        $announcements = $db->getRows("SELECT id, title FROM announcements ORDER BY priority DESC, created_at DESC");
        $response = [
            'status' => 'success',
            'items' => $announcements
        ];
        break;
        
    // 生成新的API密钥
    case 'generate_api_key':
        // 确保用户有权限执行此操作
        if (!Utils::isAdmin()) {
            $response = [
                'status' => 'error',
                'message' => '您没有权限执行此操作'
            ];
            break;
        }

        try {
            // 生成新的API密钥 (32位随机字符串)
            $new_api_key = bin2hex(random_bytes(16));
            
            // 将旧的API密钥设置为非活动
            $db->query("UPDATE api_settings SET is_active = 0 WHERE is_active = 1");
            
            // 插入新的API密钥
            $insertData = [
                'api_key' => $new_api_key,
                'created_at' => date('Y-m-d H:i:s'),
                'is_active' => 1
            ];
            
            $result = $db->insert('api_settings', $insertData);
            
            if ($result) {
                // 记录操作日志
                Utils::logActivity("生成了新的API密钥");
                
                $response = [
                    'status' => 'success',
                    'message' => 'API密钥已成功重置',
                    'api_key' => $new_api_key
                ];
            } else {
                $response = [
                    'status' => 'error',
                    'message' => '插入新的API密钥时发生错误'
                ];
            }
        } catch (Exception $e) {
            $response = [
                'status' => 'error',
                'message' => '生成API密钥时发生错误: ' . $e->getMessage()
            ];
        }
        break;
        
    // 其他AJAX操作可以在这里添加
    
    default:
        $response = [
            'status' => 'error',
            'message' => '不支持的操作'
        ];
}

// 输出JSON响应
echo json_encode($response);
exit; 