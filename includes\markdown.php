<?php
/**
 * Markdown处理文件
 * 提供将Markdown格式文本转换为HTML的功能
 */

/**
 * 将Markdown内容转换为HTML
 *
 * @param string $content Markdown格式的内容
 * @return string 转换后的HTML内容
 */
function parse_markdown($content) {
    // 使用JavaScript的marked.js渲染，此函数只是一个占位符
    // 在PHP端，我们可以使用Parsedown库来处理，但此处仅返回原始内容
    // 实际解析在前端使用marked.js完成
    return $content;
}

/**
 * 完整处理内容：先处理短代码，再处理Markdown
 *
 * @param string $content 原始内容
 * @return string 处理后的内容
 */
function process_content($content) {
    // 直接调用formatting.php中的format_content函数
    if (function_exists('format_content')) {
        return format_content($content);
    }
    
    // 如果format_content不存在，尝试使用do_shortcode
    if (function_exists('do_shortcode')) {
        $content = do_shortcode($content);
    }
    
    // 返回处理后的内容，交给前端的marked.js进行Markdown解析
    return $content;
}

/**
 * 生成Markdown解析器的JavaScript代码
 * 
 * @param string $element_id 要渲染内容的DOM元素ID
 * @param string $content Markdown内容
 * @return string JavaScript代码
 */
function get_markdown_script($element_id, $content) {
    $js = <<<EOT
<script>
document.addEventListener('DOMContentLoaded', function() {
    if (typeof marked !== 'undefined') {
        // 配置Marked选项
        marked.setOptions({
            breaks: true,      // 识别回车为<br>
            gfm: true,         // 支持Github风格的Markdown
            headerIds: true,   // 为标题生成ID
            mangle: false,     // 不转义内联HTML
            sanitize: false,   // 不净化输出
            smartLists: true,  // 使用更智能的列表行为
            smartypants: true, // 使用更智能的标点符号
            xhtml: false       // 不使用XHTML关闭标签
        });
        
        // 渲染Markdown内容
        const content = {$content};
        document.getElementById('{$element_id}').innerHTML = marked.parse(content);
        
        // 处理代码块语法高亮
        if (typeof Prism !== 'undefined') {
            Prism.highlightAllUnder(document.getElementById('{$element_id}'));
        }
    }
});
</script>
EOT;
    
    return $js;
}

/**
 * 生成完整的Markdown渲染HTML结构
 * 
 * @param string $content Markdown内容
 * @param string $element_id 要渲染内容的DOM元素ID (可选，默认自动生成)
 * @return string 完整的HTML结构
 */
function render_markdown($content, $element_id = null) {
    // 处理短代码
    $processed_content = process_content($content);
    
    // 如果没有提供ID，生成一个随机ID
    if ($element_id === null) {
        $element_id = 'markdown-' . mt_rand(1000, 9999);
    }
    
    // 生成JavaScript代码
    $js = get_markdown_script($element_id, json_encode($processed_content));
    
    // 生成HTML结构
    $html = <<<EOT
<div id="{$element_id}" class="markdown-content"></div>
{$js}
EOT;
    
    return $html;
} 