<?php
/**
 * 常用函数库
 */

if (!function_exists('redirect')) {
    /**
     * 页面重定向
     * 
     * @param string $url 重定向URL
     * @return void
     */
    function redirect($url) {
        header("Location: $url");
        exit;
    }
}

if (!function_exists('check_login')) {
    /**
     * 检查用户是否已登录
     * 
     * @return bool 是否已登录
     */
    function check_login() {
        return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
    }
}

if (!function_exists('require_login')) {
    /**
     * 要求用户登录才能访问页面
     * 
     * @return void
     */
    function require_login() {
        if (!check_login()) {
            $_SESSION['redirect_after_login'] = $_SERVER['REQUEST_URI'];
            redirect(SITE_URL . '/login.php');
        }
    }
}

if (!function_exists('escape_html')) {
    /**
     * 转义HTML特殊字符
     * 
     * @param string $string 需要转义的字符串
     * @return string 转义后的字符串
     */
    function escape_html($string) {
        return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
    }
}

if (!function_exists('format_date')) {
    /**
     * 格式化日期
     * 
     * @param string $date 日期字符串
     * @param string $format 格式化模式
     * @return string 格式化后的日期
     */
    function format_date($date, $format = 'Y-m-d H:i:s') {
        return date($format, strtotime($date));
    }
}

if (!function_exists('get_user_by_id')) {
    /**
     * 根据ID获取用户信息
     * 
     * @param int $user_id 用户ID
     * @return array|null 用户信息
     */
    function get_user_by_id($user_id) {
        global $pdo;
        
        $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
        $stmt->execute([$user_id]);
        return $stmt->fetch();
    }
}

if (!function_exists('get_current_user')) {
    /**
     * 获取当前登录用户信息
     * 
     * @return array|null 用户信息
     */
    function get_current_user() {
        if (!check_login()) {
            return null;
        }
        
        return get_user_by_id($_SESSION['user_id']);
    }
}

if (!function_exists('generate_csrf_token')) {
    /**
     * 生成CSRF令牌
     * 
     * @return string CSRF令牌
     */
    function generate_csrf_token() {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        
        return $_SESSION['csrf_token'];
    }
}

if (!function_exists('verify_csrf_token')) {
    /**
     * 验证CSRF令牌
     * 
     * @param string $token 待验证的令牌
     * @return bool 验证结果
     */
    function verify_csrf_token($token) {
        return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
    }
}

if (!function_exists('log_activity')) {
    /**
     * 记录用户活动
     * 
     * @param string $action 活动类型
     * @param string $details 详细信息
     * @param int|null $user_id 用户ID，默认为当前用户
     * @return bool 是否成功
     */
    function log_activity($action, $details = '', $user_id = null) {
        global $pdo;
        
        if ($user_id === null && check_login()) {
            $user_id = $_SESSION['user_id'];
        }
        
        $stmt = $pdo->prepare("INSERT INTO activity_logs (user_id, action, details, ip_address) VALUES (?, ?, ?, ?)");
        return $stmt->execute([
            $user_id,
            $action,
            $details,
            $_SERVER['REMOTE_ADDR']
        ]);
    }
} 