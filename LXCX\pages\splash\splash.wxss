.splash-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  background-color: #000;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.cyber-frame {
  position: relative;
  width: 80%;
  max-width: 500rpx;
  padding: 20rpx;
  border: 2px solid #0ff;
  box-shadow: 0 0 20rpx #0ff;
}

.logo {
  width: 100%;
  height: 200rpx;
}

.cyber-lines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.line {
  position: absolute;
  background: linear-gradient(90deg, transparent, #0ff, transparent);
  height: 1px;
  width: 100%;
  animation: scanLine 2s linear infinite;
}

.line:nth-child(1) { top: 0; }
.line:nth-child(2) { top: 50%; }
.line:nth-child(3) { bottom: 0; }

.loading-container {
  margin-top: 40rpx;
  width: 80%;
  max-width: 500rpx;
}

.connecting-text {
  color: #0ff;
  font-size: 28rpx;
  text-align: center;
  margin-bottom: 20rpx;
  text-shadow: 0 0 10rpx #0ff;
}

.progress-bar {
  width: 100%;
  height: 4rpx;
  background: rgba(0, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.progress {
  position: absolute;
  left: -100%;
  width: 100%;
  height: 100%;
  background: #0ff;
  box-shadow: 0 0 10rpx #0ff;
}

.progress.loading {
  animation: loading 1.5s ease-in-out forwards;
}

.scan-line {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom,
    transparent 0%,
    rgba(0, 255, 255, 0.1) 50%,
    transparent 100%);
  animation: scanAnimation 4s linear infinite;
}

.cyber-particles {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.particle {
  position: absolute;
  width: 4rpx;
  height: 4rpx;
  background: #0ff;
  border-radius: 50%;
  animation: float 3s linear infinite;
}

.glitch {
  animation: glitch 0.2s linear;
}

@keyframes scanAnimation {
  0% { transform: translateY(-100%); }
  100% { transform: translateY(100%); }
}

@keyframes loading {
  0% { left: -100%; }
  100% { left: 0; }
}

@keyframes float {
  0% {
    transform: translate(0, 0);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translate(var(--tx, 100rpx), var(--ty, 100rpx));
    opacity: 0;
  }
}

@keyframes glitch {
  0% { transform: translate(0); }
  20% { transform: translate(-5px, 5px); }
  40% { transform: translate(-5px, -5px); }
  60% { transform: translate(5px, 5px); }
  80% { transform: translate(5px, -5px); }
  100% { transform: translate(0); }
} 