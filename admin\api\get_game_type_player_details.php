<?php
require_once '../config.php';
require_once '../includes/db.php';
require_once '../includes/utils.php';

// 检查是否是AJAX请求或API请求
Utils::checkLoginAjax();

// 设置响应头
header('Content-Type: application/json');

// 获取参数
$player_id = isset($_GET['player_id']) ? (int)$_GET['player_id'] : null;
$nickname = isset($_GET['nickname']) ? Utils::sanitizeInput($_GET['nickname']) : null;
$game_type = isset($_GET['type']) ? Utils::sanitizeInput($_GET['type']) : null;

// 验证参数
if (!$player_id && !$nickname) {
    Utils::apiResponse('error', '缺少玩家ID或昵称参数', null);
    exit;
}

// 构建查询条件
$where = '';
if ($player_id) {
    $where = "WHERE p.id = {$player_id}";
} else if ($nickname) {
    $where = "WHERE p.nickname = '{$nickname}'";
}

// 获取玩家基本信息
$player_sql = "SELECT p.id, p.nickname, p.player_rank, p.is_banned, p.virtual_ip FROM players p {$where}";
$player_info = $db->getRow($player_sql);

if (!$player_info) {
    Utils::apiResponse('error', '未找到该玩家', null);
    exit;
}

// 更新where条件，使用找到的player_id
$player_id = $player_info['id'];
$where = "WHERE p.id = {$player_id}";

// 添加游戏类型筛选
$game_type_where = '';
if ($game_type) {
    $game_type_where = " AND gm.game_type = '{$game_type}'";
}

// 获取玩家在各游戏类型下的战绩
$stats_sql = "SELECT 
                gm.game_type,
                SUM(gr.kills) as total_kills,
                SUM(gr.deaths) as total_deaths,
                SUM(gr.wins) as total_wins,
                SUM(gr.losses) as total_losses,
                (CASE WHEN SUM(gr.deaths) = 0 THEN SUM(gr.kills) ELSE SUM(gr.kills) / SUM(gr.deaths) END) as kd,
                (CASE WHEN (SUM(gr.wins) + SUM(gr.losses)) = 0 THEN 0 ELSE (SUM(gr.wins) / (SUM(gr.wins) + SUM(gr.losses))) * 100 END) as win_rate,
                COUNT(DISTINCT gr.game_id) as game_count
            FROM 
                players p
            LEFT JOIN 
                game_records gr ON p.id = gr.player_id
            LEFT JOIN
                games gm ON gr.game_id = gm.id
            {$where} {$game_type_where}
            GROUP BY 
                gm.game_type";

$stats = $db->getRows($stats_sql);

// 格式化数据
foreach ($stats as &$stat) {
    $stat['kd'] = number_format($stat['kd'], 2);
    $stat['win_rate'] = number_format($stat['win_rate'], 2) . '%';
    
    // 计算KPR和DPR
    $total_games = $stat['game_count'];
    $stat['kpr'] = ($total_games > 0) ? number_format($stat['total_kills'] / $total_games, 2) : '0.00';
    $stat['dpr'] = ($total_games > 0) ? number_format($stat['total_deaths'] / $total_games, 2) : '0.00';
}

// 获取玩家最近10场比赛记录
$recent_games_sql = "SELECT 
                        g.id as game_id,
                        g.game_type,
                        g.upload_time,
                        gr.kills,
                        gr.deaths,
                        gr.wins,
                        gr.losses,
                        gr.team
                    FROM 
                        game_records gr
                    JOIN 
                        games g ON gr.game_id = g.id
                    WHERE 
                        gr.player_id = {$player_id}
                    " . ($game_type ? "AND g.game_type = '{$game_type}'" : "") . "
                    ORDER BY 
                        g.upload_time DESC
                    LIMIT 10";

$recent_games = $db->getRows($recent_games_sql);

// 获取玩家总体数据汇总
$summary_sql = "SELECT 
                    SUM(gr.kills) as total_kills,
                    SUM(gr.deaths) as total_deaths,
                    SUM(gr.wins) as total_wins,
                    SUM(gr.losses) as total_losses,
                    COUNT(DISTINCT gr.game_id) as total_games,
                    (CASE WHEN SUM(gr.deaths) = 0 THEN SUM(gr.kills) ELSE SUM(gr.kills) / SUM(gr.deaths) END) as overall_kd,
                    (CASE WHEN (SUM(gr.wins) + SUM(gr.losses)) = 0 THEN 0 ELSE (SUM(gr.wins) / (SUM(gr.wins) + SUM(gr.losses))) * 100 END) as overall_win_rate
                FROM 
                    game_records gr
                JOIN
                    games g ON gr.game_id = g.id
                WHERE 
                    gr.player_id = {$player_id}
                " . ($game_type ? "AND g.game_type = '{$game_type}'" : "");

$summary = $db->getRow($summary_sql);

if ($summary) {
    $summary['overall_kd'] = number_format($summary['overall_kd'], 2);
    $summary['overall_win_rate'] = number_format($summary['overall_win_rate'], 2) . '%';
    
    // 计算总体KPR和DPR
    $total_games = $summary['total_games'];
    $summary['overall_kpr'] = ($total_games > 0) ? number_format($summary['total_kills'] / $total_games, 2) : '0.00';
    $summary['overall_dpr'] = ($total_games > 0) ? number_format($summary['total_deaths'] / $total_games, 2) : '0.00';
}

// 构建响应
$response = [
    'status' => 'success',
    'message' => '获取玩家详细数据成功',
    'data' => [
        'player_info' => $player_info,
        'game_stats' => $stats,
        'recent_games' => $recent_games,
        'summary' => $summary
    ]
];

echo json_encode($response, JSON_UNESCAPED_UNICODE);