    </main>
    
    <footer>
        <div class="container">
            <div class="row">
                <div class="col-md-12 text-center">
                    <p class="mb-0">&copy; <?php echo date('Y'); ?> 流星数据查询系统- 版本 1.2.1</p>
                </div>
            </div>
        </div>
    </footer>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Marked.js 用于Markdown解析 -->
    <script src="https://cdn.jsdelivr.net/npm/marked@4.3.0/marked.min.js"></script>
    <!-- Prism.js 用于代码高亮 -->
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/prism.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-php.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-javascript.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-css.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-sql.min.js"></script>
    
    <!-- 文章目录相关JavaScript -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // 检查页面上是否存在目录元素
        const tocElement = document.getElementById('article-toc');
        if (tocElement) {
            // 获取所有目录链接
            const tocLinks = tocElement.querySelectorAll('a');
            
            // 为每个链接添加平滑滚动效果
            tocLinks.forEach(function(link) {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    // 获取目标元素id
                    const targetId = this.getAttribute('href').substring(1);
                    const targetElement = document.getElementById(targetId);
                    
                    if (targetElement) {
                        // 平滑滚动到目标位置
                        window.scrollTo({
                            top: targetElement.offsetTop - 60, // 减去顶部导航栏的高度
                            behavior: 'smooth'
                        });
                        
                        // 更新URL，但不重新加载页面
                        history.pushState(null, null, '#' + targetId);
                        
                        // 移除所有激活状态
                        tocLinks.forEach(function(l) {
                            l.classList.remove('active');
                        });
                        
                        // 添加激活状态到当前链接
                        this.classList.add('active');
                    }
                });
            });
            
            // 页面滚动时更新激活的目录项
            window.addEventListener('scroll', function() {
                // 获取所有带ID的标题元素
                const headings = document.querySelectorAll('h1[id], h2[id], h3[id]');
                
                // 如果没有标题，退出
                if (headings.length === 0) return;
                
                // 找到当前滚动位置最接近的标题
                let currentHeadingId = null;
                
                for (let i = 0; i < headings.length; i++) {
                    const heading = headings[i];
                    // 检查标题是否在视口上方或附近
                    if (heading.offsetTop - 100 <= window.scrollY) {
                        currentHeadingId = heading.id;
                    } else {
                        break;
                    }
                }
                
                // 更新活动的目录链接
                if (currentHeadingId) {
                    // 移除所有激活状态
                    tocLinks.forEach(function(link) {
                        link.classList.remove('active');
                    });
                    
                    // 找到对应的目录链接并添加激活状态
                    const activeLink = tocElement.querySelector('a[href="#' + currentHeadingId + '"]');
                    if (activeLink) {
                        activeLink.classList.add('active');
                    }
                }
            });
        }
    });
    </script>
</body>
</html> 