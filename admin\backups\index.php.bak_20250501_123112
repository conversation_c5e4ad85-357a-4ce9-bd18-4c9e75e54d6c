<?php
require_once 'config.php';
require_once 'includes/db.php';
require_once 'includes/utils.php';

// 包含头部
include 'includes/header.php';
?>

<div class="main-content">
    <div class="container">
<?php
// 获取分页参数
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$page = max(1, $page);
$per_page = 10;
$offset = ($page - 1) * $per_page;

// 获取筛选参数
$game_type = isset($_GET['game_type']) ? Utils::sanitizeInput($_GET['game_type']) : '';

// 构建查询条件
$where = '';
if (!empty($game_type)) {
    $where = "WHERE game_type = '{$game_type}'";
}

// 获取总记录数
$count_sql = "SELECT COUNT(*) as total FROM games {$where}";
$result = $db->getRow($count_sql);
$total_records = $result['total'];
$total_pages = ceil($total_records / $per_page);

// 获取数据列表
$sql = "SELECT g.*, 
               (SELECT COUNT(*) FROM game_records WHERE game_id = g.id) as record_count
        FROM games g 
        {$where}
        ORDER BY g.upload_time DESC 
        LIMIT {$offset}, {$per_page}";

$games = $db->getRows($sql);
?>
<h1 class="page-title">数据列表</h1>

<div class="card filter-card">
   
    <form method="GET" action="index.php" class="filter-form">
        <div class="form-group" style="display: flex; gap: 15px; align-items: center;">
            <label for="game_type" class="form-label" style="margin-bottom: 0;">游戏类型：</label>
            <select id="game_type" name="game_type" class="form-select" style="width: auto;">
                <option value="">全部</option>
                <option value="暗杀" <?php echo $game_type == '暗杀' ? 'selected' : ''; ?>>暗杀</option>
                <option value="死斗" <?php echo $game_type == '死斗' ? 'selected' : ''; ?>>死斗</option>
                <option value="盟主" <?php echo $game_type == '盟主' ? 'selected' : ''; ?>>盟主</option>
            </select>
            <button type="submit" class="btn btn-primary">筛选</button>
            <?php if (!empty($game_type)): ?>
                <a href="index.php" class="btn btn-secondary">重置</a>
            <?php endif; ?>
        </div>
    </form>
</div>

<div class="card">
    <div class="card-title">游戏数据列表</div>
    <?php if (empty($games)): ?>
        <div class="alert alert-info">暂无数据</div>
    <?php else: ?>
        <div class="table-responsive">
            <table class="table data-table">
                <thead>
                    <tr>
                        <th data-sort="id">序号</th>
                        <th data-sort="unique_id">唯一ID</th>
                        <th>图片</th>
                        <th>Excel文件</th>
                        <th data-sort="game_type">游戏类型</th>
                        <th data-sort="upload_time">上传时间</th>
                        <th>玩家数量</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($games as $index => $game): ?>
                        <tr>
                            <td><?php echo $offset + $index + 1; ?></td>
                            <td><?php echo $game['unique_id']; ?></td>
                            <td>
                                <a href="<?php echo IMAGES_DIR . $game['image_file']; ?>" target="_blank" class="image-link">
                                    <img src="<?php echo IMAGES_DIR . $game['image_file']; ?>" alt="游戏图片" style="max-width: 50px; max-height: 50px;">
                                </a>
                            </td>
                            <td><?php echo $game['excel_file']; ?></td>
                            <td>
                                <span class="game-type game-type-<?php echo $game['game_type'] == '暗杀' ? 'assassination' : ($game['game_type'] == '死斗' ? 'deathmatch' : 'alliance'); ?>">
                                    <?php echo $game['game_type']; ?>
                                </span>
                            </td>
                            <td><?php echo date('Y-m-d H:i:s', strtotime($game['upload_time'])); ?></td>
                            <td><?php echo $game['record_count']; ?></td>
                            <td>
                                <a href="create.php?id=<?php echo $game['id']; ?>" class="btn btn-primary btn-sm">查看</a>
                                <a href="javascript:void(0);" onclick="confirmDelete('确定要删除此项吗？', function() { window.location.href='delete.php?id=<?php echo $game['id']; ?>&type=game'; })" class="btn btn-danger btn-sm">删除</a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        
        <?php if ($total_pages > 1): ?>
            <div class="text-center mt-4">
                <div style="display: flex; justify-content: center; margin-bottom: 10px;">
                    <a href="index.php?page=1<?php echo !empty($game_type) ? '&game_type='.$game_type : ''; ?>" style="text-decoration: none; color: #6c757d; margin: 0 10px;">首页</a>
                    <a href="index.php?page=<?php echo max(1, $page - 1); ?><?php echo !empty($game_type) ? '&game_type='.$game_type : ''; ?>" style="text-decoration: none; color: #6c757d; margin: 0 10px;">上一页</a>
                    <a href="index.php?page=<?php echo min($total_pages, $page + 1); ?><?php echo !empty($game_type) ? '&game_type='.$game_type : ''; ?>" style="text-decoration: none; color: #6c757d; margin: 0 10px;">下一页</a>
                    <a href="index.php?page=<?php echo $total_pages; ?><?php echo !empty($game_type) ? '&game_type='.$game_type : ''; ?>" style="text-decoration: none; color: #6c757d; margin: 0 10px;">末页</a>
                </div>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</div>
    </div><!-- 结束 .container -->
</div><!-- 结束 .main-content -->

<?php
// 包含底部
include 'includes/footer.php';
?> 