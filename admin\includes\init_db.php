<?php
require_once __DIR__ . '/../../admin/config.php';
require_once 'db.php';

// 创建数据库表结构
function initializeDatabase() {
    global $db;
    
    // 创建游戏数据表
    $sql = "CREATE TABLE IF NOT EXISTS games (
        id INT(11) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        unique_id VARCHAR(50) NOT NULL UNIQUE,
        excel_file VARCHAR(255) NOT NULL,
        image_file VARCHAR(255) NOT NULL,
        game_type ENUM('暗杀', '死斗', '盟主') NOT NULL,
        upload_time DATETIME NOT NULL,
        INDEX (game_type)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
    
    if (!$db->query($sql)) {
        die("创建游戏数据表失败");
    }
    
    // 创建玩家表
    $sql = "CREATE TABLE IF NOT EXISTS players (
        id INT(11) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        nickname VARCHAR(50) NOT NULL,
        virtual_ip VARCHAR(50),
        player_rank VARCHAR(50),
        first_seen DATETIME NOT NULL,
        last_seen DATETIME NOT NULL,
        is_banned TINYINT(1) DEFAULT 0,
        UNIQUE (nickname)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
    
    if (!$db->query($sql)) {
        die("创建玩家表失败");
    }
    
    // 创建游戏记录表
    $sql = "CREATE TABLE IF NOT EXISTS game_records (
        id INT(11) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        game_id INT(11) UNSIGNED NOT NULL,
        player_id INT(11) UNSIGNED NOT NULL,
        kills INT(11) UNSIGNED DEFAULT 0,
        deaths INT(11) UNSIGNED DEFAULT 0,
        team VARCHAR(50),
        wins INT(11) UNSIGNED DEFAULT 0,
        losses INT(11) UNSIGNED DEFAULT 0,
        FOREIGN KEY (game_id) REFERENCES games(id) ON DELETE CASCADE,
        FOREIGN KEY (player_id) REFERENCES players(id) ON DELETE CASCADE,
        INDEX (player_id),
        INDEX (game_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
    
    if (!$db->query($sql)) {
        die("创建游戏记录表失败");
    }
    
    // 创建文章表
    $sql = "CREATE TABLE IF NOT EXISTS articles (
        id INT(11) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        content LONGTEXT,
        file_path VARCHAR(255),
        category_id INT(11) UNSIGNED DEFAULT 1,
        published TINYINT(1) DEFAULT 0,
        create_time DATETIME NOT NULL,
        update_time DATETIME NOT NULL,
        INDEX (published),
        INDEX (category_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
    
    if (!$db->query($sql)) {
        die("创建文章表失败");
    }
    
    // 创建文章分类表
    $sql = "CREATE TABLE IF NOT EXISTS article_categories (
        id INT(11) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(50) NOT NULL,
        description VARCHAR(255),
        is_default TINYINT(1) DEFAULT 0,
        create_time DATETIME NOT NULL,
        update_time DATETIME NOT NULL,
        INDEX (is_default)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
    
    if (!$db->query($sql)) {
        die("创建文章分类表失败");
    }
    
    // 检查是否有默认分类，如果没有则创建
    $sql = "SELECT COUNT(*) as count FROM article_categories";
    $result = $db->getRow($sql);
    
    if ($result && $result['count'] == 0) {
        // 创建默认分类
        $sql = "INSERT INTO article_categories (name, description, is_default, create_time, update_time) 
                VALUES ('默认分类', '系统默认分类', 1, NOW(), NOW())";
        
        if (!$db->query($sql)) {
            die("创建默认文章分类失败");
        }
    }
    
    // 创建公告表
    $sql = "CREATE TABLE IF NOT EXISTS announcements (
        id INT(11) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        content TEXT,
        status TINYINT(1) DEFAULT 0,
        priority TINYINT(1) DEFAULT 0,
        valid_from DATE NOT NULL,
        valid_to DATE NOT NULL,
        created_at DATETIME NOT NULL,
        updated_at DATETIME NOT NULL,
        INDEX (status),
        INDEX (priority),
        INDEX (valid_from),
        INDEX (valid_to)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
    
    if (!$db->query($sql)) {
        die("创建公告表失败");
    }
    
    // 创建API配置表
    $sql = "CREATE TABLE IF NOT EXISTS api_settings (
        id INT(11) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        api_key VARCHAR(255) NOT NULL,
        created_at DATETIME NOT NULL,
        last_used DATETIME,
        is_active TINYINT(1) DEFAULT 1
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
    
    if (!$db->query($sql)) {
        die("创建API配置表失败");
    }
    
    // 插入初始API密钥
    $sql = "INSERT INTO api_settings (api_key, created_at, is_active) 
            VALUES ('" . API_KEY . "', NOW(), 1)
            ON DUPLICATE KEY UPDATE last_used = NOW();";
    
    if (!$db->query($sql)) {
        die("插入初始API密钥失败");
    }
    
    // 创建管理员表
    $sql = "CREATE TABLE IF NOT EXISTS administrators (
        id INT(11) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        email VARCHAR(100),
        last_login DATETIME,
        created_at DATETIME NOT NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
    
    if (!$db->query($sql)) {
        die("创建管理员表失败");
    }
    
    // 检查是否有管理员账户，如果没有则创建默认账户
    $sql = "SELECT COUNT(*) as count FROM administrators";
    $result = $db->getRow($sql);
    
    if ($result && $result['count'] == 0) {
        // 创建默认管理员账户 (用户名: admin, 密码: admin123)
        $username = 'admin';
        $password = password_hash('admin123', PASSWORD_DEFAULT);
        $sql = "INSERT INTO administrators (username, password, created_at) 
                VALUES ('{$username}', '{$password}', NOW())";
        
        if (!$db->query($sql)) {
            die("创建默认管理员账户失败");
        }
        
        echo "已创建默认管理员账户，用户名: admin, 密码: admin123，请登录后立即修改密码！\n";
    }
    
    // 创建游戏分类表
    $sql = "CREATE TABLE IF NOT EXISTS game_categories (
        id INT(11) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(50) NOT NULL UNIQUE,
        description TEXT,
        created_at DATETIME NOT NULL,
        updated_at DATETIME NOT NULL,
        INDEX (name)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
    
    if (!$db->query($sql)) {
        die("创建游戏分类表失败");
    }
    
    // 插入默认分类
    $default_categories = [
        ['name' => '暗杀', 'description' => '暗杀模式游戏数据'],
        ['name' => '死斗', 'description' => '死斗模式游戏数据'],
        ['name' => '盟主', 'description' => '盟主模式游戏数据']
    ];
    
    foreach ($default_categories as $category) {
        $sql = "INSERT IGNORE INTO game_categories (name, description, created_at, updated_at) 
                VALUES ('{$category['name']}', '{$category['description']}', NOW(), NOW())";
        $db->query($sql);
    }

    // 修改游戏数据表，添加分类ID字段
    $sql = "ALTER TABLE games 
            ADD COLUMN category_id INT(11) UNSIGNED AFTER id,
            ADD FOREIGN KEY (category_id) REFERENCES game_categories(id),
            ADD INDEX (category_id)";
    
    try {
        $db->query($sql);
    } catch (Exception $e) {
        // 如果字段已存在，忽略错误
    }
    
    echo "数据库初始化完成\n";
}

// 运行初始化
initializeDatabase(); 