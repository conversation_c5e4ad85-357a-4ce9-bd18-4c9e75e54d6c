<?php
require_once '../includes/utils.php';
require_once '../includes/db.php';

Utils::checkLoginAjax();

$player_id = isset($_GET['player_id']) ? (int)$_GET['player_id'] : 0;
if (!$player_id) {
    Utils::apiResponse('error', '缺少player_id参数');
}

$player = $db->getRow("SELECT id, nickname, is_banned, ban_type FROM players WHERE id = {$player_id}");
if (!$player) {
    Utils::apiResponse('error', '玩家不存在');
}

Utils::apiResponse('success', '获取成功', [
    'player_id' => $player['id'],
    'nickname' => $player['nickname'],
    'is_banned' => (bool)$player['is_banned'],
    'ban_type' => $player['ban_type']
]); 