<?php
require_once '../config.php';
require_once '../includes/db.php';
require_once '../includes/utils.php';

// 验证API密钥
if (!Utils::validateApiKey()) {
    Utils::apiResponse('error', '无效的API密钥', null);
}

// 检查必要参数
if (!isset($_GET['nickname'])) {
    Utils::apiResponse('error', '缺少玩家昵称参数', null);
}

$nickname = Utils::sanitizeInput($_GET['nickname']);

// 获取玩家ID
$player_sql = "SELECT id FROM players WHERE nickname = '{$nickname}'";
$player = $db->getRow($player_sql);

if (!$player) {
    Utils::apiResponse('error', '未找到该玩家', null);
}

$player_id = $player['id'];

// 获取该玩家参与的所有游戏场次及其图片
$sql = "SELECT DISTINCT 
            g.id as game_id,
            g.game_type,
            g.image_file,
            g.upload_time as game_time,
            gr.team,
            gr.kills,
            gr.deaths,
            gr.wins,
            gr.losses
        FROM 
            game_records gr
        JOIN 
            games g ON gr.game_id = g.id
        WHERE 
            gr.player_id = {$player_id}
            AND g.image_file IS NOT NULL 
            AND g.image_file != ''
        ORDER BY 
            g.upload_time DESC";

$games = $db->getRows($sql);

if (!$games) {
    Utils::apiResponse('success', '该玩家暂无游戏图片记录', []);
}

// 处理图片路径，添加完整URL
$base_url = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://{$_SERVER['HTTP_HOST']}";
foreach ($games as &$game) {
    if ($game['image_file']) {
        $game['image_url'] = $base_url . '/BDLX/uploads/images/' . $game['image_file'];
    } else {
        $game['image_url'] = null;
    }
}

Utils::apiResponse('success', '获取玩家游戏图片记录成功', [
    'player_nickname' => $nickname,
    'total_images' => count($games),
    'games' => $games
]); 