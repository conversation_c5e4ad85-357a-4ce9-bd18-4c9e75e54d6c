<?php
require_once 'config.php';
require_once 'includes/db.php';
require_once 'includes/utils.php';

// 检查登录状态
Utils::checkLogin();

// 获取一些测试数据
$sql = "SELECT a.*, c.name as category_name 
        FROM articles a 
        LEFT JOIN article_categories c ON a.category_id = c.id 
        ORDER BY create_time DESC LIMIT 5";
$articles = $db->getRows($sql);

// 获取用户代理信息
$userAgent = $_SERVER['HTTP_USER_AGENT'];
$isMobile = preg_match('/Mobile|Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i', $userAgent);
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>移动端调试 - BDLX后台管理</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/layout-fix.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <style>
        .debug-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 12px;
        }
        
        .debug-section {
            margin-bottom: 20px;
            padding: 15px;
            background: white;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .debug-title {
            font-weight: bold;
            color: #007bff;
            margin-bottom: 10px;
            font-size: 16px;
        }
        
        .test-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        
        .test-table th,
        .test-table td {
            border: 1px solid #dee2e6;
            padding: 8px;
            text-align: left;
        }
        
        .test-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        
        .status-ok { color: #28a745; }
        .status-warning { color: #ffc107; }
        .status-error { color: #dc3545; }
        
        /* 复制文章页面的样式进行测试 */
        .article-table {
            margin-bottom: 0;
            min-width: 100%;
            table-layout: fixed;
        }
        
        .table-responsive {
            -webkit-overflow-scrolling: touch;
            overflow-x: auto;
            overflow-y: hidden;
        }
        
        .article-table th,
        .article-table td {
            padding: 12px 8px;
            border-bottom: 1px solid #e9ecef;
            text-align: left;
            vertical-align: middle;
        }
        
        .article-table th {
            background-color: #f1f3f5;
            font-weight: 600;
            font-size: 13px;
        }
        
        .article-title {
            font-weight: 500;
            color: #333;
            word-break: break-word;
        }
        
        @media (max-width: 768px) {
            .article-table {
                font-size: 13px;
            }
            
            .article-table th,
            .article-table td {
                padding: 8px 4px;
            }
        }
        
        @media (max-width: 576px) {
            .article-table th:nth-child(4),
            .article-table td:nth-child(4),
            .article-table th:nth-child(6),
            .article-table td:nth-child(6) {
                display: none;
            }
            
            .article-table th:nth-child(3),
            .article-table td:nth-child(3) {
                width: 40% !important;
                max-width: 150px;
            }
            
            .article-title {
                font-size: 14px !important;
                line-height: 1.4 !important;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                max-height: 2.8em;
            }
        }
    </style>
</head>
<body>
    <div class="container" style="max-width: 100%; padding: 20px;">
        <h1>移动端调试信息</h1>
        
        <div class="debug-section">
            <div class="debug-title">设备信息</div>
            <div class="debug-info">
                <strong>屏幕宽度:</strong> <span id="screen-width"></span>px<br>
                <strong>屏幕高度:</strong> <span id="screen-height"></span>px<br>
                <strong>视口宽度:</strong> <span id="viewport-width"></span>px<br>
                <strong>视口高度:</strong> <span id="viewport-height"></span>px<br>
                <strong>设备像素比:</strong> <span id="device-pixel-ratio"></span><br>
                <strong>用户代理:</strong> <?php echo htmlspecialchars($userAgent); ?><br>
                <strong>检测为移动设备:</strong> <span class="<?php echo $isMobile ? 'status-ok' : 'status-warning'; ?>">
                    <?php echo $isMobile ? '是' : '否'; ?>
                </span>
            </div>
        </div>
        
        <div class="debug-section">
            <div class="debug-title">CSS媒体查询测试</div>
            <div class="debug-info">
                <div id="media-query-info"></div>
            </div>
        </div>
        
        <div class="debug-section">
            <div class="debug-title">数据库连接测试</div>
            <div class="debug-info">
                <strong>数据库连接:</strong> 
                <span class="status-ok">正常</span><br>
                <strong>文章数量:</strong> <?php echo count($articles); ?> 条<br>
                <strong>查询状态:</strong> 
                <span class="<?php echo !empty($articles) ? 'status-ok' : 'status-warning'; ?>">
                    <?php echo !empty($articles) ? '成功' : '无数据'; ?>
                </span>
            </div>
        </div>
        
        <div class="debug-section">
            <div class="debug-title">文章标题显示测试</div>
            <?php if (!empty($articles)): ?>
                <div class="table-responsive">
                    <table class="article-table">
                        <thead>
                            <tr>
                                <th width="3%">
                                    <input type="checkbox">
                                </th>
                                <th width="5%">ID</th>
                                <th width="20%">标题</th>
                                <th width="22%">摘要</th>
                                <th width="10%">分类</th>
                                <th width="8%">状态</th>
                                <th width="12%">更新时间</th>
                                <th width="20%">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($articles as $article): ?>
                                <tr>
                                    <td><input type="checkbox"></td>
                                    <td><?php echo $article['id']; ?></td>
                                    <td class="article-title"><?php echo htmlspecialchars($article['title']); ?></td>
                                    <td><?php echo !empty($article['summary']) ? htmlspecialchars($article['summary']) : '<span class="text-muted">无摘要</span>'; ?></td>
                                    <td><span class="badge badge-info"><?php echo $article['category_name'] ?? '默认分类'; ?></span></td>
                                    <td>
                                        <?php if ($article['published']): ?>
                                            <span class="badge badge-success">已发布</span>
                                        <?php else: ?>
                                            <span class="badge badge-secondary">草稿</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo date('Y-m-d H:i', strtotime($article['update_time'])); ?></td>
                                    <td>
                                        <button class="btn btn-info btn-sm"><i class="fas fa-eye"></i></button>
                                        <button class="btn btn-primary btn-sm"><i class="fas fa-edit"></i></button>
                                        <button class="btn btn-danger btn-sm"><i class="fas fa-trash"></i></button>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="alert alert-warning">没有找到文章数据</div>
            <?php endif; ?>
        </div>
        
        <div class="debug-section">
            <div class="debug-title">CSS样式检测</div>
            <div class="debug-info">
                <div id="css-detection"></div>
            </div>
        </div>
        
        <div class="debug-section">
            <div class="debug-title">JavaScript功能测试</div>
            <div class="debug-info">
                <div id="js-test-results"></div>
            </div>
        </div>
    </div>

    <script>
        function updateDebugInfo() {
            // 设备信息
            document.getElementById('screen-width').textContent = screen.width;
            document.getElementById('screen-height').textContent = screen.height;
            document.getElementById('viewport-width').textContent = window.innerWidth;
            document.getElementById('viewport-height').textContent = window.innerHeight;
            document.getElementById('device-pixel-ratio').textContent = window.devicePixelRatio || 1;
            
            // 媒体查询测试
            const mediaQueries = [
                { query: '(max-width: 320px)', name: '极小屏幕 (≤320px)' },
                { query: '(max-width: 576px)', name: '小屏幕 (≤576px)' },
                { query: '(max-width: 768px)', name: '中等屏幕 (≤768px)' },
                { query: '(max-width: 992px)', name: '大屏幕 (≤992px)' }
            ];
            
            let mediaQueryInfo = '';
            mediaQueries.forEach(mq => {
                const matches = window.matchMedia(mq.query).matches;
                mediaQueryInfo += `<strong>${mq.name}:</strong> <span class="${matches ? 'status-ok' : 'status-warning'}">${matches ? '匹配' : '不匹配'}</span><br>`;
            });
            document.getElementById('media-query-info').innerHTML = mediaQueryInfo;
            
            // CSS样式检测
            const titleElement = document.querySelector('.article-title');
            if (titleElement) {
                const computedStyle = window.getComputedStyle(titleElement);
                const cssInfo = `
                    <strong>标题元素样式:</strong><br>
                    display: ${computedStyle.display}<br>
                    visibility: ${computedStyle.visibility}<br>
                    opacity: ${computedStyle.opacity}<br>
                    font-size: ${computedStyle.fontSize}<br>
                    color: ${computedStyle.color}<br>
                    width: ${computedStyle.width}<br>
                    height: ${computedStyle.height}<br>
                    overflow: ${computedStyle.overflow}<br>
                `;
                document.getElementById('css-detection').innerHTML = cssInfo;
            } else {
                document.getElementById('css-detection').innerHTML = '<span class="status-error">未找到标题元素</span>';
            }
            
            // JavaScript功能测试
            let jsResults = '';
            
            // 测试DOM查询
            const titleElements = document.querySelectorAll('.article-title');
            jsResults += `<strong>标题元素数量:</strong> ${titleElements.length}<br>`;
            
            // 测试事件监听
            try {
                const testElement = document.createElement('div');
                testElement.addEventListener('click', function() {});
                jsResults += '<strong>事件监听:</strong> <span class="status-ok">正常</span><br>';
            } catch (e) {
                jsResults += '<strong>事件监听:</strong> <span class="status-error">异常</span><br>';
            }
            
            // 测试CSS选择器
            try {
                const testQuery = document.querySelector('.article-table th:nth-child(3)');
                jsResults += `<strong>CSS选择器测试:</strong> <span class="${testQuery ? 'status-ok' : 'status-warning'}">${testQuery ? '正常' : '未找到元素'}</span><br>`;
            } catch (e) {
                jsResults += '<strong>CSS选择器测试:</strong> <span class="status-error">异常</span><br>';
            }
            
            document.getElementById('js-test-results').innerHTML = jsResults;
        }
        
        // 初始化
        updateDebugInfo();
        
        // 监听窗口大小变化
        window.addEventListener('resize', updateDebugInfo);
        window.addEventListener('orientationchange', function() {
            setTimeout(updateDebugInfo, 100);
        });
        
        // 每5秒更新一次信息
        setInterval(updateDebugInfo, 5000);
    </script>
</body>
</html>
