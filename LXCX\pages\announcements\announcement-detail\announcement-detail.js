// 引入API请求
const api = require('../../../utils/api')
const util = require('../../../utils/util')

Page({
  data: {
    id: null,
    announcement: null,
    loading: true
  },

  onLoad: function (options) {
    if (options.id) {
      const id = options.id
      this.setData({ id })
      this.loadAnnouncementDetail(id)
    } else {
      util.showToast('参数错误')
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
  },
  
  // 加载公告详情
  loadAnnouncementDetail: function (id) {
    util.showLoading()
    api.getAnnouncement(id)
      .then(announcement => {
        // 格式化公告内容，支持短代码和Markdown
        if (announcement && announcement.content) {
          // 检查util是否有formatContent方法
          if (typeof util.formatContent === 'function') {
            announcement.content = util.formatContent(announcement.content)
          } else {
            // 如果util没有formatContent方法，使用简单的格式化
            announcement.content = this.formatContent(announcement.content)
          }
        }
        
        this.setData({ announcement, loading: false })
        wx.setNavigationBarTitle({
          title: announcement.title || '公告详情'
        })
        util.hideLoading()
      })
      .catch(err => {
        util.hideLoading()
        util.showToast('获取公告详情失败')
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      })
  },
  
  // 基本格式化内容的方法
  formatContent: function(content) {
    if (!content) return '';
    
    // 处理可能的简单HTML标签
    // 此处仅做基本处理，更复杂的处理请使用专业解析库
    return content
      .replace(/<br\s*\/?>/gi, '\n')
      .replace(/<p>(.*?)<\/p>/gi, '<p>$1</p>\n')
      .replace(/<div>(.*?)<\/div>/gi, '<div>$1</div>\n');
  },
  
  // 处理富文本链接点击
  onRichTextTap: function(e) {
    // 获取点击事件的详细信息
    const detail = e.detail;
    
    // 检查是否点击的是链接
    if (detail.node && detail.node.name === 'a') {
      // 提取链接地址
      const url = detail.node.attrs.href;
      if (url) {
        // 检查链接类型并做相应处理
        if (url.startsWith('http://') || url.startsWith('https://')) {
          // 外部链接，复制到剪贴板
          wx.setClipboardData({
            data: url,
            success: () => {
              wx.showToast({
                title: '链接已复制，请在浏览器中打开',
                icon: 'none'
              });
            }
          });
        } else if (url.startsWith('/pages/')) {
          // 小程序内部页面跳转
          wx.navigateTo({
            url: url
          });
        }
      }
    }
    
    // 检查是否点击的是图片
    if (detail.node && detail.node.name === 'img') {
      const src = detail.node.attrs.src;
      if (src) {
        // 预览图片
        wx.previewImage({
          current: src,
          urls: [src]
        });
      }
    }
  },
  
  // 分享
  onShareAppMessage: function () {
    const { id, announcement } = this.data;
    let title = '重要公告';
    
    if (announcement) {
      // 如果标题太长，截取前30个字符
      title = announcement.title.length > 30 ? 
        announcement.title.substring(0, 30) + '...' : 
        announcement.title;
    }
    
    return {
      title: title,
      path: `/pages/announcements/announcement-detail/announcement-detail?id=${id}`
    }
  },
  // 分享到朋友圈
  onShareTimeline: function () {
    const { id, announcement } = this.data;
    let title = '重要公告';
    
    if (announcement) {
      // 如果标题太长，截取前30个字符
      title = announcement.title.length > 30 ? 
        announcement.title.substring(0, 30) + '...' : 
        announcement.title;
    }
    
    return {
      title: title, 
      path: `/pages/announcements/announcement-detail/announcement-detail?id=${id}`,
      query: `id=${id}`,
      success: function(res) {
        wx.showToast({
          title: '分享成功',
          icon: 'success',
          duration: 2000
        });
      },
      fail: function(res) {
        wx.showToast({
          title: '分享失败',
          icon: 'none',
          duration: 2000
        });
      }
    }
  }
}) 