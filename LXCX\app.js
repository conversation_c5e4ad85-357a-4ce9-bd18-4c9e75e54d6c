// 引入工具函数
const util = require('./utils/util.js')

App({
  globalData: {
        apiBaseUrl: 'https://lxbl.online/BDLX/admin/api.php',
  //  apiBaseUrl: 'http://localhost/BDLX/admin/api.php',
    
       apiKey: '64132a39191aeb959dba5bfa536f7a71',
  // apiKey: 'b26d312ecc908ac379c88568e96faa0a',

    dateFormatSupport: null // 记录设备对日期格式的支持情况
  },


  
  onLaunch() {
    // 小程序启动时执行的逻辑
    console.log('小程序启动');
    
    // 测试设备对日期格式的支持情况
    this.testDateFormatSupport();
  },
  
  /**
   * 测试设备对不同日期格式的支持情况
   * 用于检测当前设备是否需要特殊处理日期格式
   */
  testDateFormatSupport() {
    const formats = {
      iso: 'yyyy-MM-ddTHH:mm:ss',
      dash: 'yyyy-MM-dd HH:mm:ss',
      slash: 'yyyy/MM/dd HH:mm:ss',
      dashDate: 'yyyy-MM-dd',
      slashDate: 'yyyy/MM/dd'
    };
    
    const support = {};
    const now = new Date();
    
    // 测试 ISO 8601 格式 (yyyy-MM-ddTHH:mm:ss)
    try {
      const isoString = now.toISOString().substring(0, 19);
      const date = new Date(isoString);
      support.iso = !isNaN(date.getTime());
    } catch (e) {
      support.iso = false;
    }
    
    // 测试 yyyy-MM-dd HH:mm:ss 格式
    try {
      const year = now.getFullYear();
      const month = (now.getMonth() + 1).toString().padStart(2, '0');
      const day = now.getDate().toString().padStart(2, '0');
      const hours = now.getHours().toString().padStart(2, '0');
      const minutes = now.getMinutes().toString().padStart(2, '0');
      const seconds = now.getSeconds().toString().padStart(2, '0');
      
      const dashFormat = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      const date = new Date(dashFormat);
      support.dash = !isNaN(date.getTime());
    } catch (e) {
      support.dash = false;
    }
    
    // 测试 yyyy/MM/dd HH:mm:ss 格式
    try {
      const year = now.getFullYear();
      const month = (now.getMonth() + 1).toString().padStart(2, '0');
      const day = now.getDate().toString().padStart(2, '0');
      const hours = now.getHours().toString().padStart(2, '0');
      const minutes = now.getMinutes().toString().padStart(2, '0');
      const seconds = now.getSeconds().toString().padStart(2, '0');
      
      const slashFormat = `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`;
      const date = new Date(slashFormat);
      support.slash = !isNaN(date.getTime());
    } catch (e) {
      support.slash = false;
    }
    
    // 记录支持情况到全局数据
    this.globalData.dateFormatSupport = support;
    
    // 根据支持情况确定最佳格式
    if (support.iso) {
      this.globalData.preferredDateFormat = formats.iso;
    } else if (support.slash) {
      this.globalData.preferredDateFormat = formats.slash;
    } else if (support.dash) {
      this.globalData.preferredDateFormat = formats.dash;
    } else {
      // 退回到最安全的格式
      this.globalData.preferredDateFormat = formats.slashDate;
    }
    
    console.log('设备日期格式支持情况:', support);
    console.log('推荐使用的日期格式:', this.globalData.preferredDateFormat);
  }
})