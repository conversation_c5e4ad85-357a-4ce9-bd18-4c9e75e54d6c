CREATE TABLE IF NOT EXISTS `rules` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `title` varchar(255) NOT NULL COMMENT '规则标题',
    `content` text NOT NULL COMMENT '规则内容',
    `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0=草稿，1=已发布',
    `created_at` datetime NOT NULL COMMENT '创建时间',
    `updated_at` datetime NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='规则通知表'; 