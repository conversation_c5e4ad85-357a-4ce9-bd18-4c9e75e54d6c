{"description": "项目配置文件", "packOptions": {"ignore": [{"value": "images/lxrwimg", "type": "folder"}], "include": []}, "miniprogramRoot": "", "compileType": "miniprogram", "projectname": "流星笔记", "setting": {"useCompilerPlugins": ["typescript"], "babelSetting": {"ignore": [], "disablePlugins": ["transform-runtime"], "outputPath": ""}, "es6": false, "enhance": true, "postcss": false, "compileWorklet": false, "minified": true, "uglifyFileName": false, "uploadWithSourceMap": true, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "minifyWXML": true, "localPlugins": false, "disableUseStrict": false, "condition": false, "swc": false, "disableSWC": true}, "simulatorType": "wechat", "simulatorPluginLibVersion": {}, "condition": {}, "srcMiniprogramRoot": "", "appid": "wx09cc9a8451c372c1", "libVersion": "2.30.3", "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}}