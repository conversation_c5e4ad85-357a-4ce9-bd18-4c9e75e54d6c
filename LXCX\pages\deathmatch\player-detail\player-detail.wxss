/* 加载状态 */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
  color: #999;
}

/* 玩家头部信息 */
.player-header {
  position: relative;
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: #fff;
}

.player-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-right: 30rpx;
}

.player-basic {
  flex: 1;
}

.player-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.player-id, .player-rank {
  font-size: 30rpx;
  color: #666;
  margin-bottom: 5rpx;
  
  display: flex;
  align-items: center;
}

/* VIP标签样式 */
.vip-tag {
  color: #DAA520; /* 金黄色文字 */
  background-color: #FFFACD; /* 浅黄色背景 */
  padding: 2rpx 10rpx;
  margin-left:10rpx;
  border-radius: 6rpx;
  font-size: 26rpx;
  display: inline-block;
}

/* 标签页 */
.tabs {
  display: flex;
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  margin-top: 10rpx;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.tab-item.active {
  color: #4A90E2;
  font-weight: 500;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40%;
  height: 6rpx;
  background-color: #4A90E2;
  border-radius: 3rpx;
}

/* 内容区域 */
.content-box {
  width: 100%;
}

.overview-card, .hero-card, .history-card {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

/* 统计数据 */
.stat-row {
  display: flex;
  justify-content: space-around;
  text-align: center;
}

.stat-value {
  font-size: 36rpx;
  font-weight: 600;
  color: #4A90E2;
  margin-bottom: 10rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #999;
}

/* 数据面板 */
.data-panel {
  margin-bottom: 20rpx;
}

.data-item {
  margin-bottom: 20rpx;
}

.data-label {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8rpx;
}

.data-value {
  font-weight: bold;
  color: #4A90E2;
}

.data-bar-container {
  position: relative;
  height: 20rpx;
  background-color: #f5f5f5;
  border-radius: 10rpx;
  overflow: hidden;
}

.data-bar-baseline {
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 2rpx;
  background-color: #999;
}

.data-bar {
  height: 100%;
  border-radius: 10rpx;
  transition: width 0.3s ease;
}

/* 数据指标说明按钮 */
.show-tip {
  text-align: center;
  padding: 20rpx 0;
  color: #4A90E2;
  font-size: 28rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
}

/* 指标说明内容 */
.tip-box {
  background-color: #f9f9f9;
  padding: 20rpx;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.tip-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 15rpx;
}

.tip-content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

.tip-item {
  margin: 10rpx 0;
}

.tip-highlight {
  color: #4A90E2;
  font-weight: 500;
  margin-top: 15rpx;
}

/* 游戏场次表格样式 */
.game-history-table {
  margin-top: 15rpx;
  width: 100%;
  overflow-x: auto;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  border-radius: 8rpx;
  white-space: nowrap;
}

.table-header {
  display: flex;
  background-color: #f0f2f5;
  font-weight: bold;
  border-top-left-radius: 8rpx;
  border-top-right-radius: 8rpx;
}

.th {
  flex: 1;
  padding: 20rpx 10rpx;
  text-align: center;
  font-size: 26rpx;
  color: #333;
  white-space: nowrap;
}

.table-row {
  display: flex;
  border-bottom: 1rpx solid #eee;
  background-color: #fff;
}

.table-row:nth-child(odd) {
  background-color: #f9f9f9;
}

.table-row:last-child {
  border-bottom-left-radius: 8rpx;
  border-bottom-right-radius: 8rpx;
}

.td {
  flex: 1;
  padding: 20rpx 5rpx;
  text-align: center;
  font-size: 26rpx;
  color: #555;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-width: 60rpx;
}

.win {
  color: #4CAF50;
  font-weight: bold;
}

.lose {
  color: #FF5252;
  font-weight: bold;
}

.no-data {
  padding: 60rpx 0;
  text-align: center;
  color: #999;
  font-size: 28rpx;
  background-color: #fff;
}

/* 调整卡片内部边距 */
.hero-card {
  padding: 20rpx;
  margin-bottom: 20rpx;
}

/* 调整时间列宽度 */
.table-header .th:first-child,
.table-row .td:first-child {
  flex: 2;
  min-width: 180rpx;
}

.container {
  min-height: 100vh;
  background-image: url('https://img1.lxbl.online/102.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  padding: 30rpx 20rpx;
  position: relative;
}

.container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1;
}

/* 确保所有内容在遮罩层上方 */
.player-header, .tabs, .content-box {
  position: relative;
  z-index: 2;
}

/* 玩家头部信息 */
.player-header {
  background: rgba(255, 248, 240, 0.95);
  border: none;
  padding: 40rpx;
  margin: 20rpx;
  position: relative;
  box-shadow: 0 4rpx 20rpx rgba(139, 69, 19, 0.15);
}

/* 添加卷轴装饰 */
.player-header::before,
.player-header::after {
  content: '';
  position: absolute;
  width: 30rpx;
  height: 100%;
  top: 0;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="15" height="100%" viewBox="0 0 15 100"><path d="M0,0 Q7.5,50 0,100" fill="none" stroke="%238B4513" stroke-width="2"/></svg>');
  background-repeat: repeat-y;
}

.player-header::before {
  left: -15rpx;
}

.player-header::after {
  right: -15rpx;
  transform: scaleX(-1);
}

.player-avatar {
  border: 4rpx solid #8B4513;
  box-shadow: 0 0 15rpx rgba(139, 69, 19, 0.2);
}

.player-name {
  font-family: "楷体", "STKaiti";
  color: #4A321F;
  font-size: 40rpx;
  letter-spacing: 4rpx;
  text-shadow: 2rpx 2rpx 4rpx rgba(139, 69, 19, 0.1);
}

/* VIP标签古风样式 */
.vip-tag {
  background: linear-gradient(45deg, #DAA520, #FFD700);
  color: #8B4513;
  border: 1rpx solid #8B4513;
  padding: 4rpx 16rpx;
  font-family: "楷体", "STKaiti";
  text-shadow: 0 0 2rpx rgba(139, 69, 19, 0.3);
  border-radius: 35rpx; /* 圆角卷轴感 */
}

/* 标签页古风样式 */
.tabs {
  background: rgba(255, 248, 240, 0.95);
  border: 1rpx solid #8B4513;
  padding: 10rpx;
  margin: 20rpx;
}

.tab-item {
  font-family: "楷体", "STKaiti";
  color: #8B4513;
  font-size: 32rpx;
  letter-spacing: 2rpx;
}

.tab-item.active {
  color: #DAA520;
  font-weight: bold;
}

.tab-item.active::after {
  background: linear-gradient(to right, transparent, #DAA520, transparent);
  height: 4rpx;
}

/* 卡片古风样式 */
.overview-card, .hero-card {
  background: rgba(255, 248, 240, 0.95);
  border: none;
  margin: 30rpx 20rpx;
  padding: 30rpx;
  position: relative;
  box-shadow: 0 4rpx 20rpx rgba(139, 69, 19, 0.15);
}

.card-title {
  font-family: "楷体", "STKaiti";
  color: #4A321F;
  font-size: 36rpx;
  text-align: center;
  letter-spacing: 4rpx;
  
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid rgba(139, 69, 19, 0.2);
  position: relative;
}

.card-title::before,
.card-title::after {
  content: '※';
  color: #DAA520;
  margin: 0 20rpx;
}

/* 统计数据古风样式 */
.stat-value {
  font-family: "楷体", "STKaiti";
  color: #8B4513;
  font-size: 40rpx;
  text-shadow: 2rpx 2rpx 4rpx rgba(139, 69, 19, 0.1);
}

.stat-label {
  font-family: "楷体", "STKaiti";
  color: #4A321F;
  font-size: 28rpx;
  margin-top: 8rpx;
}

/* 数据面板古风样式 */
.data-panel {
  background: rgba(255, 248, 240, 0.9);
  padding: 20rpx;
  border-radius: 8rpx;
  border: 1rpx solid rgba(139, 69, 19, 0.2);
}

.data-label {
  font-family: "楷体", "STKaiti";
  color: #4A321F;
}

.data-bar-container {
  background: rgba(139, 69, 19, 0.1);
  border: 1rpx solid rgba(139, 69, 19, 0.2);
}

/* 表格古风样式 */
.game-history-table {
  border: 1rpx solid rgba(139, 69, 19, 0.3);
  background: rgba(255, 248, 240, 0.9);
}

.table-header {
  background: rgba(139, 69, 19, 0.1);
  border-bottom: 2rpx solid rgba(139, 69, 19, 0.3);
}

.th {
  font-family: "楷体", "STKaiti";
  color: #4A321F;
  font-weight: bold;
  padding: 20rpx 10rpx;
  text-align: center;
}

.td {
  font-family: "楷体", "STKaiti";
  color: #4A321F;
  padding: 16rpx 10rpx;
  text-align: center;
  border-bottom: 1rpx solid rgba(139, 69, 19, 0.1);
}

.table-row:hover {
  background: rgba(139, 69, 19, 0.05);
}

/* 提示框古风样式 */
.tip-box {
  background: rgba(255, 248, 240, 0.95);
  border: 1rpx solid #8B4513;
  padding: 20rpx;
  margin-top: 20rpx;
}

.tip-title {
  font-family: "楷体", "STKaiti";
  color: #8B4513;
  font-size: 32rpx;
  text-align: center;
  margin-bottom: 16rpx;
}

.tip-content {
  font-family: "楷体", "STKaiti";
  color: #4A321F;
  font-size: 28rpx;
  line-height: 1.6;
}

.tip-highlight {
  color: #DAA520;
  margin-top: 10rpx;
  font-weight: bold;
}

/* 作弊封禁样式 */
.cheat-ban {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 82, 82, 0.15); /* 默认红色作弊封禁 */
  z-index: 10;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.cheat-ban.violation {
  background: rgba(116, 255, 120, 0.15); /* 浅绿色违规封禁 */
}

.ban-icon {
  width: 450rpx;
  height: 450rpx;
  margin-bottom: 20rpx;
}

.ban-text {
  font-size: 42rpx;
  color: #f00;
  font-weight: bold;
  text-shadow: 0 2rpx 8rpx rgba(0,0,0,0.18);
  letter-spacing: 20rpx;
  animation: fireText 2s infinite;
}

.cheat-ban.violation .ban-text {
  color: #388e3c;
  text-shadow: none;
}

@keyframes fireText {
  0% { transform: scale(1); opacity: 0.7; }
  50% { transform: scale(1.1); opacity: 0.9; }
  100% { transform: scale(1); opacity: 0.7; }
}

/* 图片预览弹窗 */
.image-preview-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 999;
  display: flex;
  justify-content: center;
  align-items: center;
}

.preview-content {
  width: 90%;
  max-height: 90%;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 0 20rpx rgba(0, 0, 0, 0.5);
  animation: modalFadeIn 0.3s ease;
}

@keyframes modalFadeIn {
  from { opacity: 0; transform: scale(0.9); }
  to { opacity: 1; transform: scale(1); }
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #eee;
  background-color: #f9f9f9;
}

.preview-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.close-icon {
  font-size: 40rpx;
  color: #999;
  padding: 0 20rpx;
  cursor: pointer;
}

.preview-image {
  width: 100%;
  height: 500rpx;
  background-color: #f9f9f9;
}

.game-info {
  padding: 20rpx 30rpx;
  background-color: #fff;
}

.info-row {
  display: flex;
  margin-bottom: 10rpx;
  font-size: 28rpx;
}

.info-label {
  color: #666;
  width: 160rpx;
}

.info-value {
  color: #333;
  flex: 1;
}

.preview-footer {
  display: flex;
  justify-content: flex-end;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #eee;
  background-color: #f9f9f9;
}

.action-btn {
  padding: 10rpx 30rpx;
  margin-left: 20rpx;
  background-color: #4A90E2;
  color: #fff;
  border-radius: 6rpx;
  font-size: 28rpx;
}

.action-btn:active {
  opacity: 0.8;
} 