/* 页面头部 */
.header {
  padding: 20rpx 0;
  margin-bottom: 20rpx;
}

.banner-container {
  width: 100%;
  padding: 0;
  margin: 0;
}

.banner-wrapper {
  position: relative;
  width: 100%;
  height: 200rpx;
  overflow: hidden;
  border-radius: 12rpx;
}

.banner-image {
  width: 100%;
  height: 100%;
  display: block;
  object-fit: cover;
}

.title-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.3);
}

.title {
  font-family: "楷体", "STKaiti";
  color: #F5DEB3;
  font-size: 44rpx;
  letter-spacing: 8rpx;
  text-shadow: 2rpx 2rpx 4rpx rgba(0, 0, 0, 0.5);
  white-space: nowrap;
}

.title::before,
.title::after {
  content: '※';
  color: #DAA520;
  margin: 0 20rpx;
  font-size: 32rpx;
}

/* 公告列表 */
.announcements-list {
  width: 95%;
}

.loading, .empty {
  text-align: center;
  padding: 40rpx 0;
  color: #999;
  font-size: 28rpx;
}

/* 错误提示样式 */
.error-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}

.error-msg {
  color: #ff4d4f;
  font-size: 28rpx;
  margin: 20rpx 0 30rpx;
  text-align: center;
}

.retry-btn {
  background-color: #4A90E2;
  color: #fff;
  font-size: 28rpx;
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
  margin-top: 20rpx;
}

.announcement-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 20rpx;
  background-color: rgba(255, 255, 255, 0.493);
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

/* 置顶公告样式 */
.top-item {
  background-color: #f8f8ff;
  border-left: 6rpx solid #4A90E2;
}

.announcement-info {
  flex: 1;
  min-width: 0;  /* 防止flex子项溢出 */
  width: 100%;
  
}

.announcement-title {
  display: flex;
  align-items: flex-start;
  flex-wrap: wrap;
  font-size: 30rpx;
  color: #333;
  margin-bottom: 10rpx;
  width: 100%;
  word-break: break-all;
}

.announcement-title text {
  word-break: break-all;
  white-space: pre-wrap;
}

.new-badge {
  display: inline-block;
  font-size: 20rpx;
  color: #fff;
  background-color: #ff3b30;
  padding: 2rpx 10rpx;
  border-radius: 10rpx;
  margin-left: 10rpx;
}

/* 置顶标识样式 */
.top-badge {
  display: inline-block;
  font-size: 20rpx;
  color: #fff;
  background-color: #4A90E2;
  padding: 2rpx 10rpx;
  border-radius: 10rpx;
  margin-right: 10rpx;
}

.announcement-time {
  font-size: 24rpx;
  color: #999;
}

.arrow image {
  width: 32rpx;
  height: 32rpx;
}

.container {
  min-height: 100vh;
  background-image: url('https://img1.lxbl.online/102.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  padding: 30rpx 20rpx;
  position: relative;
}

.container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1;
}

/* 确保所有内容在遮罩层上方 */
.header, .announcements-list {
  position: relative;
  z-index: 2;
}

/* 头部样式 */
.header {
  text-align: center;
  padding: 30rpx 0;
  margin-bottom: 30rpx;
  position: relative;
}

.header::before,
.header::after {
  content: '';
  position: absolute;
  height: 2rpx;
  width: 30%;
  background: linear-gradient(to right, transparent, #DAA520, transparent);
  bottom: 0;
}

.header::before {
  left: 0;
}

.header::after {
  right: 0;
}

/* 公告列表样式 */
.announcements-list {
  padding: 0 20rpx;
}

.announcement-item {
  background: rgba(255, 248, 240, 0.95);
  margin-bottom: 30rpx;
  padding: 30rpx;
  border-radius: 8rpx;
  position: relative;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 20rpx rgba(139, 69, 19, 0.15);
}

/* 添加卷轴效果 */
.announcement-item::before,
.announcement-item::after {
  content: '';
  position: absolute;
  width: 30rpx;
  height: 100%;
  top: 0;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="15" height="100%" viewBox="0 0 15 100"><path d="M0,0 Q7.5,50 0,100" fill="none" stroke="%238B4513" stroke-width="2"/></svg>');
  background-repeat: repeat-y;
}

.announcement-item::before {
  left: -15rpx;
}

.announcement-item::after {
  right: -15rpx;
  transform: scaleX(-1);
}

.announcement-item:active {
  transform: scale(0.98);
}

/* 置顶公告样式 */
.top-item {
  background: rgba(255, 248, 240, 0.98);
  border: 2rpx solid rgba(218, 165, 32, 0.3);
}

.announcement-info {
  flex: 1;
}

.announcement-title {
  font-family: "楷体", "STKaiti";
  font-size: 32rpx;
  color: #4A321F;
  margin-bottom: 12rpx;
  display: flex;
  align-items: center;
  letter-spacing: 2rpx;
}

.top-badge {
  background: linear-gradient(45deg, #DAA520, #FFD700);
  color: #8B4513;
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
  margin-right: 16rpx;
  font-family: "楷体", "STKaiti";
  border: 1rpx solid #8B4513;
}

.new-badge {
  background: #FF4D4F;
  color: #fff;
  font-size: 24rpx;
  padding: 2rpx 10rpx;
  border-radius: 6rpx;
  margin-left: 16rpx;
}

.announcement-time {
  font-family: "楷体", "STKaiti";
  font-size: 26rpx;
  color: #8B4513;
  opacity: 0.8;
}

/* 加载状态 */
.loading {
  text-align: center;
  padding: 40rpx;
  color: #F5DEB3;
  font-family: "楷体", "STKaiti";
}

/* 错误状态 */
.error-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 0;
}

.error-msg {
  color: #FF4D4F;
  font-family: "楷体", "STKaiti";
  margin: 20rpx 0;
}

.retry-btn {
  background: rgba(218, 165, 32, 0.2);
  color: #DAA520;
  border: 1rpx solid #DAA520;
  font-family: "楷体", "STKaiti";
  padding: 15rpx 40rpx;
  font-size: 28rpx;
  border-radius: 0;
}

/* 空状态 */
.empty {
  text-align: center;
  padding: 60rpx 0;
  color: #F5DEB3;
  font-family: "楷体", "STKaiti";
  font-size: 32rpx;
  text-shadow: 2rpx 2rpx 4rpx rgba(0, 0, 0, 0.3);
} 