<?php
// 数据库配置
define('DB_HOST', 'localhost');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_NAME', 'bdlx');



// 应用路径配置
define('ROOT_PATH', dirname(__DIR__) . '/');
define('BASE_URL', '/BDLX/admin/');
define('SITE_URL', '/BDLX/');
define('UPLOAD_DIR', ROOT_PATH . 'uploads/');
define('EXCEL_DIR', UPLOAD_DIR . 'excel/');
define('IMAGES_DIR', UPLOAD_DIR . 'images/');
define('THUMBS_DIR', UPLOAD_DIR . 'images/Thumbsimgs/');
define('ARTICLES_DIR', UPLOAD_DIR . 'articles/');
define('ANNOUNCEMENTS_DIR', UPLOAD_DIR . 'announcements/');

// API配置
define('API_KEY', '64132a39191aeb959dba5bfa536f7a71'); // 固定API密钥，与数据库中的一致

// 时区设置
date_default_timezone_set('Asia/Shanghai');

// 开启错误报告（开发环境）
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', dirname(__FILE__) . '/error.log');

// 创建必要的目录
$directories = [
    UPLOAD_DIR,
    EXCEL_DIR,
    IMAGES_DIR,
    THUMBS_DIR,
    ARTICLES_DIR,
    ANNOUNCEMENTS_DIR
];

foreach ($directories as $dir) {
    if (!file_exists($dir)) {
        if (!mkdir($dir, 0755, true)) {
            error_log("无法创建目录: " . $dir);
        }
    } else if (!is_writable($dir)) {
        if (!chmod($dir, 0755)) {
            error_log("无法修改目录权限: " . $dir);
        }
    }
}

// 设置文件上传相关的PHP配置
ini_set('upload_max_filesize', '10M');
ini_set('post_max_size', '10M');
ini_set('max_execution_time', 300);
ini_set('memory_limit', '256M');
?>