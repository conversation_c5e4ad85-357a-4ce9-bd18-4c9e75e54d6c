<?php
require_once 'config.php';
require_once 'includes/db.php';
require_once 'includes/utils.php';

// 包含头部
include 'includes/header.php';
?>

<div class="main-content">
    <div class="container">
<?php

// 初始化变量
$error = '';
$success = '';
$article = [
    'id' => '',
    'title' => '',
    'content' => '',
    'summary' => '',
    'category_id' => 1, // 默认分类ID
    'published' => 0
];

// 获取分类列表
$categories_sql = "SELECT * FROM article_categories ORDER BY is_default DESC, name ASC";
$categories = $db->getRows($categories_sql);

// 检查是否是编辑现有文章
if (isset($_GET['id']) && !empty($_GET['id'])) {
    $article_id = (int)$_GET['id'];
    
    // 获取文章数据
    $sql = "SELECT * FROM articles WHERE id = {$article_id}";
    $article_data = $db->getRow($sql);
    
    if ($article_data) {
        $article = $article_data;
    } else {
        $error = '未找到指定文章';
    }
}

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // 获取表单数据
    $title = isset($_POST['title']) ? Utils::sanitizeInput($_POST['title']) : '';
    $content = isset($_POST['content']) ? $_POST['content'] : '';
    $summary = isset($_POST['summary']) ? Utils::sanitizeInput($_POST['summary']) : '';
    $category_id = isset($_POST['category_id']) ? (int)$_POST['category_id'] : 1;
    $published = isset($_POST['published']) ? 1 : 0;
    
    // 如果摘要为空，则使用文章内容前30个字作为摘要
    if (empty($summary)) {
        $stripped_content = strip_tags($content);
        $summary = mb_substr($stripped_content, 0, 30, 'UTF-8');
        if (mb_strlen($stripped_content, 'UTF-8') > 30) {
            $summary .= '...';
        }
    }
    
    if (empty($title)) {
        $error = '文章标题不能为空';
    } else {
        if (empty($error)) {
            $now = date('Y-m-d H:i:s');
            
            if (!empty($article['id'])) {
                // 更新现有文章
                $sql = "UPDATE articles SET 
                        title = '{$db->escape($title)}',
                        content = '{$db->escape($content)}',
                        summary = '{$db->escape($summary)}',
                        category_id = {$category_id},
                        published = {$published},
                        update_time = '{$now}'
                        WHERE id = {$article['id']}";
                
                if ($db->query($sql)) {
                    $success = '文章更新成功';
                    
                    // 更新页面数据
                    $article['title'] = $title;
                    $article['content'] = $content;
                    $article['summary'] = $summary;
                    $article['category_id'] = $category_id;
                    $article['published'] = $published;
                    $article['update_time'] = $now;
                    
                    // 记录活动
                    Utils::logActivity('更新文章', "更新文章：{$title}");
                } else {
                    $error = '文章更新失败';
                }
            } else {
                // 创建新文章
                $article_id = $db->insert('articles', [
                    'title' => $title,
                    'content' => $content,
                    'summary' => $summary,
                    'category_id' => $category_id,
                    'published' => $published,
                    'create_time' => $now,
                    'update_time' => $now
                ]);
                
                if ($article_id) {
                    $success = '文章创建成功';
                    
                    // 更新页面数据
                    $article['id'] = $article_id;
                    $article['title'] = $title;
                    $article['content'] = $content;
                    $article['summary'] = $summary;
                    $article['category_id'] = $category_id;
                    $article['published'] = $published;
                    $article['create_time'] = $now;
                    $article['update_time'] = $now;
                    
                    // 记录活动
                    Utils::logActivity('创建文章', "创建文章：{$title}");
                } else {
                    $error = '文章创建失败';
                }
            }
        }
    }
}

// 页面标题
$page_title = empty($article['id']) ? '新增文章' : '编辑文章';
?>

<div class="article-editor-container">
    <div class="editor-header">
        <h1 class="page-title"><?php echo $page_title; ?></h1>
        <div class="editor-actions">
            <a href="articles.php" class="btn btn-secondary btn-sm"><i class="fas fa-arrow-left"></i> 返回列表</a>
            <?php if (!empty($article['id'])): ?>
                <a href="article_preview.php?id=<?php echo $article['id']; ?>" class="btn btn-info btn-sm" target="_blank"><i class="fas fa-eye"></i> 预览</a>
            <?php endif; ?>
        </div>
    </div>

    <?php if (!empty($error)): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
    <?php endif; ?>

    <?php if (!empty($success)): ?>
        <div class="alert alert-success"><?php echo $success; ?></div>
    <?php endif; ?>

    <div class="editor-card">
        <div class="card-header">
            <div class="card-title"><i class="fas fa-edit"></i> 文章编辑</div>
        </div>
        <div class="card-body">
            <form method="POST" action="article_edit.php<?php echo !empty($article['id']) ? '?id=' . $article['id'] : ''; ?>" id="articleForm">
                <div class="editor-layout">
                    <div class="editor-main">
                        <div class="form-group">
                            <label for="title" class="form-label">文章标题</label>
                            <input type="text" id="title" name="title" class="form-control" value="<?php echo $article['title']; ?>" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="summary" class="form-label">文章摘要</label>
                            <textarea id="summary" name="summary" class="form-control" rows="2"><?php echo $article['summary']; ?></textarea>
                            <div class="form-text">可选。若不填写，系统将自动使用文章内容前30个字作为摘要。</div>
                        </div>
                        
                        <div class="form-group">
                            <label for="content" class="form-label">
                                文章内容 
                                <span class="label-tag">支持Markdown格式</span>
                            </label>
                            <div class="markdown-editor">
                                <div class="editor-toolbar">
                                    <button type="button" class="toolbar-btn" title="加粗" onclick="insertMarkdown('**', '**')"><i class="fas fa-bold"></i></button>
                                    <button type="button" class="toolbar-btn" title="斜体" onclick="insertMarkdown('*', '*')"><i class="fas fa-italic"></i></button>
                                    <button type="button" class="toolbar-btn" title="标题" onclick="insertMarkdown('## ', '')"><i class="fas fa-heading"></i></button>
                                    <button type="button" class="toolbar-btn" title="链接" onclick="insertMarkdownLink()"><i class="fas fa-link"></i></button>
                                    <button type="button" class="toolbar-btn" title="列表" onclick="insertMarkdown('- ', '')"><i class="fas fa-list-ul"></i></button>
                                    <button type="button" class="toolbar-btn" title="引用" onclick="insertMarkdown('> ', '')"><i class="fas fa-quote-left"></i></button>
                                    <button type="button" class="toolbar-btn" title="分割线" onclick="insertMarkdown('\n---\n', '')"><i class="fas fa-minus"></i></button>
                                </div>
                                <textarea id="content" name="content" class="markdown-editor-input"><?php echo $article['content']; ?></textarea>
                            </div>
                            <div class="form-text">使用Markdown语法编写内容，支持格式化文本、链接和图片。</div>
                        </div>
                    </div>

                    <div class="editor-sidebar">
                        <div class="sidebar-card">
                            <h3 class="sidebar-card-title">文章设置</h3>
                            <div class="sidebar-card-body">
                                <div class="form-group">
                                    <label for="category_id" class="form-label">文章分类</label>
                                    <select id="category_id" name="category_id" class="form-control">
                                        <?php foreach ($categories as $category): ?>
                                            <option value="<?php echo $category['id']; ?>" <?php echo $article['category_id'] == $category['id'] ? 'selected' : ''; ?>>
                                                <?php echo $category['name']; ?><?php echo $category['is_default'] ? ' (默认)' : ''; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="form-text">
                                        <a href="article_categories.php" class="sidebar-link"><i class="fas fa-folder"></i> 管理分类</a>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <div class="publish-switch">
                                        <label class="switch-label">
                                            <input type="checkbox" id="published" name="published" <?php echo $article['published'] ? 'checked' : ''; ?>>
                                            <span class="switch-slider"></span>
                                        </label>
                                        <div class="switch-text">
                                            <label for="published">立即发布</label>
                                            <div class="form-text">勾选后文章将立即对用户可见，否则保存为草稿。</div>
                                        </div>
                                    </div>
                                </div>

                                <button type="submit" class="btn btn-primary btn-block"><i class="fas fa-save"></i> 保存文章</button>
                            </div>
                        </div>
                        
                        <div class="sidebar-card mt-3">
                            <h3 class="sidebar-card-title">
                                <i class="fas fa-image"></i> 插入图片
                            </h3>
                            <div class="sidebar-card-body">
                                <div class="upload-image-container">
                                    <div class="form-group">
                                        <label for="imageUpload" class="form-label">选择图片文件</label>
                                        <input type="file" id="imageUpload" class="form-control" accept="image/*">
                                        <div class="form-text">支持JPG、PNG、GIF等常见图片格式</div>
                                    </div>
                                    
                                    <div id="imagePreviewContainer" class="image-preview-container" style="display: none;">
                                        <div class="preview-header">
                                            图片预览
                                            <button type="button" id="cancelUpload" class="btn btn-sm btn-danger float-right">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                        <div class="preview-body">
                                            <img id="imagePreview" src="#" alt="预览图片">
                                            <div class="image-info"></div>
                                        </div>
                                        <div class="preview-footer">
                                            <div class="form-group">
                                                <label for="imageDescription" class="form-label">图片描述</label>
                                                <input type="text" id="imageDescription" class="form-control" placeholder="请输入图片描述">
                                            </div>
                                            <button type="button" id="uploadImage" class="btn btn-primary btn-block">
                                                <i class="fas fa-upload"></i> 上传并插入
                                            </button>
                                        </div>
                                    </div>
                                    
                                    <div id="uploadProgress" class="upload-progress" style="display: none;">
                                        <div class="progress-bar"></div>
                                        <div class="progress-text">上传中 (0%)</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
/* 文章编辑器专用样式 */
.article-editor-container {
    max-width: 1400px;
    margin: 0 auto;
}

.editor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.editor-actions {
    display: flex;
    gap: 10px;
}

.editor-card {
    background-color: white;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
    overflow: hidden;
    margin-bottom: 30px;
}

.card-header {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.card-title {
    font-size: 16px;
    font-weight: 600;
    color: #495057;
    margin: 0;
}

.card-title i {
    margin-right: 8px;
    color: #3498db;
}

.card-body {
    padding: 20px;
}

.editor-layout {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 25px;
}

.editor-main {
    width: 100%;
}

.editor-sidebar {
    width: 100%;
}

.label-tag {
    display: inline-block;
    font-size: 12px;
    font-weight: normal;
    padding: 2px 8px;
    background-color: #e9ecef;
    color: #495057;
    border-radius: 3px;
    margin-left: 8px;
}

.editor-toolbar {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    padding: 8px;
    background-color: #f8f9fa;
    border: 1px solid #ddd;
    border-bottom: none;
    border-radius: 4px 4px 0 0;
}

.toolbar-btn {
    background-color: transparent;
    border: none;
    width: 36px;
    height: 36px;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
}

.toolbar-btn:hover {
    background-color: #e9ecef;
}

.toolbar-btn i {
    color: #495057;
    font-size: 14px;
}

.markdown-editor {
    display: flex;
    flex-direction: column;
    margin-bottom: 15px;
}

.markdown-editor-input {
    flex: 1;
    min-height: 400px;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 0 0 4px 4px;
    font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, monospace;
    font-size: 14px;
    line-height: 1.5;
    resize: vertical;
}

.sidebar-card {
    background-color: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e9ecef;
    overflow: hidden;
}

.sidebar-card-title {
    padding: 12px 15px;
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    background-color: #e9ecef;
    color: #495057;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.sidebar-card-title i {
    margin-right: 5px;
    color: #3498db;
}

.sidebar-card-body {
    padding: 15px;
}

.sidebar-link {
    display: inline-block;
    font-size: 13px;
    color: #3498db;
    text-decoration: none;
    margin-top: 5px;
}

.sidebar-link:hover {
    text-decoration: underline;
}

.publish-switch {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
}

.switch-label {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.switch-label input {
    opacity: 0;
    width: 0;
    height: 0;
}

.switch-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
}

.switch-slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .switch-slider {
    background-color: #2ecc71;
}

input:checked + .switch-slider:before {
    transform: translateX(26px);
}

.switch-text {
    flex: 1;
}

.switch-text label {
    font-weight: 600;
    margin-bottom: 3px;
    display: block;
}

.btn-block {
    display: block;
    width: 100%;
}

.mt-3 {
    margin-top: 15px;
}

.p-2 {
    padding: 10px;
}

.float-right {
    float: right;
}

.image-library {
    border-radius: 4px;
    background-color: #ffffff;
    padding: 0;
}

.image-gallery {
    display: flex;
    flex-direction: column;
    gap: 10px;
    max-height: 400px;
    overflow-y: auto;
    padding: 5px;
}

.image-item {
    width: 100%;
    background-color: white;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    transition: transform 0.2s, box-shadow 0.2s;
    margin-bottom: 5px;
    display: flex;
    flex-direction: column;
}

.image-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 3px 6px rgba(0,0,0,0.15);
}

.image-item img {
    width: 100%;
    height: 120px;
    object-fit: cover;
    display: block;
    border-bottom: 1px solid #eee;
}

.image-item-info {
    padding: 8px;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.filename-text {
    font-size: 11px;
    color: #666;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.insert-image {
    width: 100%;
    padding: 4px;
    font-size: 12px;
}

.loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 30px;
    width: 100%;
}

.loading-spinner {
    width: 24px;
    height: 24px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 10px;
}

.loading-text {
    color: #666;
    font-size: 14px;
}

.img-error {
    font-size: 10px;
    color: #dc3545;
    padding: 5px;
}

.no-images {
    width: 100%;
    padding: 20px;
    text-align: center;
    color: #666;
}

.text-center {
    text-align: center;
}

.mt-2 {
    margin-top: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式调整 */
@media (max-width: 992px) {
    .editor-layout {
        grid-template-columns: 1fr;
    }
    
    .editor-sidebar {
        order: -1;
    }
}

@media (max-width: 768px) {
    .editor-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .editor-actions {
        margin-top: 10px;
    }
}

/* 图片上传相关样式 */
.upload-image-container {
    width: 100%;
}

.image-preview-container {
    border: 1px solid #dee2e6;
    border-radius: 4px;
    overflow: hidden;
    margin-top: 15px;
    background-color: #fff;
}

.preview-header {
    padding: 10px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
    font-size: 14px;
}

.preview-body {
    padding: 10px;
    text-align: center;
}

.preview-body img {
    max-width: 100%;
    max-height: 200px;
    border-radius: 4px;
    border: 1px solid #eee;
}

.image-info {
    margin-top: 8px;
    font-size: 12px;
    color: #6c757d;
}

.preview-footer {
    padding: 10px;
    border-top: 1px solid #dee2e6;
}

.upload-progress {
    margin-top: 15px;
}

.progress-bar {
    height: 8px;
    background-color: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

.progress-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 0;
    background-color: #3498db;
    transition: width 0.3s;
}

.progress-text {
    font-size: 12px;
    color: #6c757d;
    margin-top: 5px;
    text-align: center;
}

.sidebar-link {
    display: inline-block;
    font-size: 13px;
    color: #3498db;
    text-decoration: none;
    margin-top: 5px;
}

.sidebar-link:hover {
    text-decoration: underline;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const contentInput = document.getElementById('content');
    const imageUpload = document.getElementById('imageUpload');
    const imagePreviewContainer = document.getElementById('imagePreviewContainer');
    const imagePreview = document.getElementById('imagePreview');
    const imageInfo = document.querySelector('.image-info');
    const imageDescription = document.getElementById('imageDescription');
    const uploadImageBtn = document.getElementById('uploadImage');
    const cancelUploadBtn = document.getElementById('cancelUpload');
    const uploadProgress = document.getElementById('uploadProgress');
    const progressBar = document.querySelector('.progress-bar');
    const progressText = document.querySelector('.progress-text');
    
    // 文件选择监听
    imageUpload.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (!file) {
            return;
        }
        
        // 检查文件类型
        if (!file.type.match('image.*')) {
            alert('请选择图片文件');
            return;
        }
        
        // 显示预览
        const reader = new FileReader();
        reader.onload = function(e) {
            imagePreview.src = e.target.result;
            imageInfo.textContent = `${file.name} (${formatFileSize(file.size)})`;
            imagePreviewContainer.style.display = 'block';
            
            // 自动填充描述（使用文件名，去掉扩展名）
            const filename = file.name.replace(/\.[^/.]+$/, "");
            imageDescription.value = filename;
        };
        reader.readAsDataURL(file);
    });
    
    // 取消上传
    cancelUploadBtn.addEventListener('click', function() {
        resetUploadForm();
    });
    
    // 上传并插入图片
    uploadImageBtn.addEventListener('click', function() {
        const file = imageUpload.files[0];
        if (!file) {
            alert('请先选择图片');
            return;
        }
        
        const formData = new FormData();
        formData.append('image', file);
        formData.append('description', imageDescription.value || file.name);
        
        // 显示上传进度
        uploadProgress.style.display = 'block';
        uploadImageBtn.disabled = true;
        
        // 发送上传请求
        const xhr = new XMLHttpRequest();
        xhr.open('POST', 'ajax/upload_image.php', true);
        
        xhr.upload.onprogress = function(e) {
            if (e.lengthComputable) {
                const percentComplete = Math.round((e.loaded / e.total) * 100);
                updateProgressBar(percentComplete);
            }
        };
        
        xhr.onload = function() {
            if (xhr.status === 200) {
                try {
                    const response = JSON.parse(xhr.responseText);
                    if (response.success) {
                        // 插入Markdown格式的图片
                        const description = imageDescription.value || file.name.replace(/\.[^/.]+$/, "");
                        const imageMd = `![${description}](${response.image_url})`;
                        insertTextAtCursor(contentInput, imageMd);
                        
                        // 重置表单
                        resetUploadForm();
                        
                        // 显示成功消息
                        alert('图片上传成功');
                    } else {
                        alert('图片上传失败: ' + response.message);
                    }
                } catch (error) {
                    alert('上传处理失败: ' + error.message);
                }
            } else {
                alert('上传请求失败，服务器响应: ' + xhr.status);
            }
            
            // 隐藏进度条
            uploadProgress.style.display = 'none';
            uploadImageBtn.disabled = false;
        };
        
        xhr.onerror = function() {
            alert('上传出错，请检查网络连接');
            uploadProgress.style.display = 'none';
            uploadImageBtn.disabled = false;
        };
        
        xhr.send(formData);
    });
    
    // 更新进度条
    function updateProgressBar(percent) {
        progressBar.style.setProperty('--percent', percent + '%');
        progressBar.querySelector('::before').style.width = percent + '%';
        progressText.textContent = `上传中 (${percent}%)`;
    }
    
    // 重置上传表单
    function resetUploadForm() {
        imageUpload.value = '';
        imagePreviewContainer.style.display = 'none';
        imagePreview.src = '#';
        imageInfo.textContent = '';
        imageDescription.value = '';
        uploadProgress.style.display = 'none';
    }
    
    // 格式化文件大小
    function formatFileSize(bytes) {
        if (bytes < 1024) {
            return bytes + ' B';
        } else if (bytes < 1024 * 1024) {
            return (bytes / 1024).toFixed(2) + ' KB';
        } else {
            return (bytes / (1024 * 1024)).toFixed(2) + ' MB';
        }
    }
});

// 在光标位置插入文本
function insertTextAtCursor(textarea, text) {
    const startPos = textarea.selectionStart;
    const endPos = textarea.selectionEnd;
    const textBefore = textarea.value.substring(0, startPos);
    const textAfter = textarea.value.substring(endPos, textarea.value.length);
    
    textarea.value = textBefore + text + textAfter;
    textarea.selectionStart = startPos + text.length;
    textarea.selectionEnd = startPos + text.length;
    textarea.focus();
}

// Markdown工具栏功能
function insertMarkdown(before, after) {
    const textarea = document.getElementById('content');
    const startPos = textarea.selectionStart;
    const endPos = textarea.selectionEnd;
    const selection = textarea.value.substring(startPos, endPos);
    
    // 如果有选中文本，在选中文本前后添加标记
    if (startPos !== endPos) {
        const newText = before + selection + after;
        insertTextAtCursor(textarea, newText);
        textarea.selectionStart = startPos + before.length;
        textarea.selectionEnd = startPos + before.length + selection.length;
    } else {
        // 如果没有选中文本，则只插入标记
        insertTextAtCursor(textarea, before + after);
        textarea.selectionStart = startPos + before.length;
        textarea.selectionEnd = startPos + before.length;
    }
}

// 插入链接
function insertMarkdownLink() {
    const textarea = document.getElementById('content');
    const url = prompt('请输入链接地址:', 'https://');
    if (url) {
        const text = prompt('请输入链接文字:', '链接文字');
        if (text) {
            insertTextAtCursor(textarea, `[${text}](${url})`);
        }
    }
}
</script>

    </div><!-- 结束 .container -->
</div><!-- 结束 .main-content -->

<?php
// 包含底部<!-- 结束 .container -->
</div><!-- 结束 .main-content -->

<?php
include 'includes/footer.php';
?> 