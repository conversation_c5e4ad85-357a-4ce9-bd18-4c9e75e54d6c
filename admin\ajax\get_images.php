<?php
require_once '../config.php';
require_once '../includes/db.php';
require_once '../includes/utils.php';

// 验证管理员登录状态
session_start();
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('HTTP/1.1 403 Forbidden');
    echo json_encode(['error' => '未授权访问']);
    exit;
}

// 设置响应头
header('Content-Type: application/json; charset=utf-8');

// 输出调试信息
error_log('开始处理图片库请求');
error_log('ROOT_PATH: ' . ROOT_PATH);
error_log('SITE_URL: ' . SITE_URL);

// 图片目录路径
$images_dir = UPLOAD_DIR . 'browseImages/';
$absolute_path = realpath($images_dir);

// 输出调试信息
error_log('图片目录配置路径: ' . $images_dir);
error_log('图片目录绝对路径: ' . $absolute_path);

// 检查目录是否存在和可访问
if (!$absolute_path || !is_dir($absolute_path)) {
    error_log('警告: 图片目录不存在或不可访问');
    // 尝试创建目录
    if (!file_exists($images_dir)) {
        mkdir($images_dir, 0755, true);
        error_log('创建目录: ' . $images_dir);
    }
}

// 获取图片列表
$images = [];

try {
    // 读取目录中的图片文件
    if (file_exists($images_dir) && is_dir($images_dir)) {
        $files = scandir($images_dir);
        error_log('找到文件数量: ' . count($files));
        
        foreach ($files as $file) {
            // 跳过当前目录和上级目录
            if ($file === '.' || $file === '..') {
                continue;
            }
            
            // 输出文件信息
            $file_path = $images_dir . $file;
            error_log('处理文件: ' . $file_path . ' 存在: ' . (file_exists($file_path) ? '是' : '否'));
            
            // 获取文件扩展名
            $ext = strtolower(pathinfo($file, PATHINFO_EXTENSION));
            
            // 只包含图片文件
            $allowed_exts = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
            if (in_array($ext, $allowed_exts)) {
                $filepath = $images_dir . $file;
                
                if (file_exists($filepath)) {
                    $filesize = filesize($filepath);
                    $filetime = filemtime($filepath);
                    
                    $images[] = [
                        'filename' => $file,
                        'filesize' => $filesize,
                        'filesize_formatted' => Utils::formatFileSize($filesize),
                        'filetime' => $filetime,
                        'filetime_formatted' => date('Y-m-d H:i:s', $filetime),
                        'path' => str_replace(ROOT_PATH, SITE_URL, $filepath)
                    ];
                    error_log('添加图片: ' . $file . ' 路径: ' . str_replace(ROOT_PATH, SITE_URL, $filepath));
                } else {
                    error_log('文件不存在: ' . $filepath);
                }
            } else {
                error_log('忽略非图片文件: ' . $file . ' (扩展名: ' . $ext . ')');
            }
        }
    } else {
        error_log('无法读取目录: ' . $images_dir);
        throw new Exception('无法读取图片目录');
    }
    
    // 按修改时间倒序排序（最新的在前面）
    usort($images, function($a, $b) {
        return $b['filetime'] - $a['filetime'];
    });
    
    // 返回图片列表
    $response = json_encode([
        'images' => $images,
        'debug' => [
            'dir' => $images_dir,
            'absolute_path' => $absolute_path,
            'dir_exists' => is_dir($images_dir),
            'root_path' => ROOT_PATH,
            'site_url' => SITE_URL,
            'count' => count($images),
            'php_version' => PHP_VERSION,
            'os' => PHP_OS
        ]
    ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    
    if ($response === false) {
        throw new Exception('JSON编码失败: ' . json_last_error_msg());
    }
    
    error_log('返回图片数量: ' . count($images));
    echo $response;
} catch (Exception $e) {
    // 输出错误信息
    error_log('获取图片列表失败: ' . $e->getMessage());
    header('HTTP/1.1 500 Internal Server Error');
    echo json_encode([
        'error' => '获取图片列表失败: ' . $e->getMessage(),
        'debug' => [
            'dir' => $images_dir,
            'absolute_path' => $absolute_path,
            'dir_exists' => is_dir($images_dir),
            'root_path' => ROOT_PATH,
            'site_url' => SITE_URL,
            'php_version' => PHP_VERSION,
            'os' => PHP_OS
        ]
    ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
}
?> 