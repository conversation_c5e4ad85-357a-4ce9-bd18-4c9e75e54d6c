<view class="container">
  <!-- 加载中 -->
  <view class="loading" wx:if="{{loading}}">
    <text>加载中...</text>
  </view>
  
  <block wx:elif="{{article}}">
    <!-- 文章内容区域 -->
    <view class="article-wrapper">
      <!-- 文章标题 -->
      <view class="article-title">{{article.title}}</view>
      
      <!-- 视频播放区域 -->
      <view class="video-container" wx:if="{{article.video_url}}">
        <video 
          id="articleVideo"
          src="{{article.video_url}}"
          controls="{{true}}"
          show-center-play-btn="{{true}}"
          play-btn-position="center"
          enable-play-gesture="{{true}}"
          show-progress="{{true}}"
          bindplay="onVideoPlay"
          bindpause="onVideoPause"
          bindended="onVideoEnd"
          binderror="onVideoError"
        />
        <view class="video-error" wx:if="{{videoError}}">
          <text>视频加载失败</text>
        </view>
      </view>
     
      <!-- 文章内容（包含嵌入的媒体） -->
      <view class="article-content">
        <!-- 分段显示内容，在媒体位置插入播放器 -->
        <block wx:for="{{contentSegments}}" wx:key="index" wx:for-item="segment">
          <!-- 文本内容段 -->
          <rich-text wx:if="{{segment.type === 'text'}}" nodes="{{segment.content}}" space="nbsp" decode="true"></rich-text>

          <!-- 视频播放器 -->
          <view wx:elif="{{segment.type === 'video'}}" class="inline-video-container">
            <video
              src="{{segment.url}}"
              controls="{{true}}"
              show-center-play-btn="{{true}}"
              play-btn-position="center"
              enable-play-gesture="{{true}}"
              show-progress="{{true}}"
              object-fit="contain"
              class="inline-video"
              bindplay="onVideoPlay"
              bindpause="onVideoPause"
              bindended="onVideoEnd"
              binderror="onVideoError"
            />
          </view>

          <!-- 音频播放器 -->
          <view wx:elif="{{segment.type === 'audio'}}" class="inline-audio-container">
            <view class="audio-player-simple" bindtap="handleAudioControl" data-url="{{segment.url}}">
              <view class="audio-control-btn">
                <view class="control-icon">{{currentAudio === segment.url && isAudioPlaying ? '⏸️' : '▶️'}}</view>
              </view>
              <view class="audio-info">
                <view class="audio-name">{{segment.format.toUpperCase()}} 音频</view>
                <view class="audio-progress-container" wx:if="{{currentAudio === segment.url}}">
                  <slider class="audio-progress" value="{{audioProgress}}" max="100" bindchange="handleAudioSeek" activeColor="#007AFF" backgroundColor="#e9ecef" block-size="12" />
                  <view class="audio-time">{{formattedCurrentTime}} / {{formattedDuration}}</view>
                </view>
              </view>
            </view>
          </view>
        </block>
      </view>


      
      <!-- 底部安全区域，防止内容被底部栏遮挡 -->
      <view class="safe-bottom-area"></view>
    </view>
    
    <!-- 底部交互区 -->
    <view class="article-actions {{pageReady ? 'visible' : ''}}">
      <view class="action-item" bindtap="handleLike" hover-class="button-hover">
        <view class="action-icon {{isLiked ? 'liked' : ''}}">
          <view class="icon-text">{{isLiked ? '♥' : '♡'}}</view>
        </view>
        <text class="action-text">{{likeCount || 0}}</text>
      </view>
    </view>
  </block>
  
  <!-- 错误提示 -->
  <view class="error" wx:else>
    <text>无法加载文章内容</text>
  </view>
</view> 