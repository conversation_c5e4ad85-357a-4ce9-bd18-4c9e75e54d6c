<view class="container">
  <!-- Enhanced Header Section -->
  <view class="header">
    <view class="header-decoration"></view>
    <text class="title">游戏类型选择</text>

    <view class="title-underline"></view>
  </view>

  <!-- Enhanced Mode Selection Grid -->
  <view class="mode-list">
    <block wx:for="{{modes}}" wx:key="id" wx:for-index="index">
      <view class="mode-item mode-item-{{index}}" bindtap="goToMode" data-mode="{{item.id}}">
        <view class="mode-card-inner">
          <view class="mode-icon-container">
            <image class="mode-icon" src="{{item.icon}}" mode="aspectFit"></image>
            <view class="icon-glow"></view>
          </view>
          <view class="mode-content">
            <text class="mode-name">{{item.name}}</text>
            <text class="mode-description">{{item.description || '点击进入'}}</text>
          </view>
          <view class="mode-arrow-container">
            <view class="mode-arrow"></view>
          </view>
        </view>
        <view class="mode-item-bg"></view>
      </view>
    </block>
  </view>

  <!-- Footer Section -->
  <view class="footer">
    <view class="footer-text" bindtap="getLuckPost">{{luckpost}}</view>
    <view class="footer-tip">点击上方文字获取新的幸运帖</view>
  </view>
</view>