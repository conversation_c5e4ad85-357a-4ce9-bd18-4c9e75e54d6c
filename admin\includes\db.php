<?php
namespace admin\includes;

use mysqli;

require_once __DIR__ . '/../config.php';

// 创建全局mysqli连接
global $mysqli;
$mysqli = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
if ($mysqli->connect_error) {
    die("数据库连接失败: " . $mysqli->connect_error);
}
$mysqli->set_charset('utf8mb4');

class Database {
    private $conn;
    
    // 构造函数，创建数据库连接
    public function __construct() {
        global $mysqli;
        $this->conn = $mysqli;
    }
    
    // 执行SQL查询
    public function query($sql) {
        $result = $this->conn->query($sql);
        if (!$result) {
            error_log("SQL查询错误: " . $this->conn->error . " SQL: " . $sql);
            return false;
        }
        return $result;
    }
    
    // 获取单行数据
    public function getRow($sql) {
        $result = $this->query($sql);
        if ($result && $result->num_rows > 0) {
            return $result->fetch_assoc();
        }
        return false;
    }
    
    // 获取所有数据行
    public function getRows($sql) {
        $result = $this->query($sql);
        if ($result && $result->num_rows > 0) {
            $data = [];
            while ($row = $result->fetch_assoc()) {
                $data[] = $row;
            }
            return $data;
        }
        return [];
    }
    
    // 插入数据并返回插入ID
    public function insert($table, $data) {
        $columns = implode(', ', array_keys($data));
        $placeholders = implode(', ', array_fill(0, count($data), '?'));
        $types = '';
        $values = [];
        
        foreach ($data as $value) {
            if (is_int($value)) {
                $types .= 'i';
            } elseif (is_float($value)) {
                $types .= 'd';
            } elseif (is_string($value)) {
                $types .= 's';
            } else {
                $types .= 's';
            }
            $values[] = $value;
        }
        
        $sql = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
        $stmt = $this->conn->prepare($sql);
        
        if (!$stmt) {
            error_log("准备SQL语句失败: " . $this->conn->error);
            return false;
        }
        
        $stmt->bind_param($types, ...$values);
        $stmt->execute();
        
        if ($stmt->affected_rows > 0) {
            $id = $stmt->insert_id;
            $stmt->close();
            return $id;
        }
        
        $stmt->close();
        return false;
    }
    
    // 更新数据
    public function update($table, $data, $where) {
        $set = [];
        foreach ($data as $column => $value) {
            $set[] = "{$column} = ?";
        }
        $set = implode(', ', $set);
        
        $sql = "UPDATE {$table} SET {$set} WHERE {$where}";
        $stmt = $this->conn->prepare($sql);
        
        if (!$stmt) {
            error_log("准备SQL语句失败: " . $this->conn->error);
            return false;
        }
        
        $types = '';
        $values = [];
        
        foreach ($data as $value) {
            if (is_int($value)) {
                $types .= 'i';
            } elseif (is_float($value)) {
                $types .= 'd';
            } elseif (is_string($value)) {
                $types .= 's';
            } else {
                $types .= 's';
            }
            $values[] = $value;
        }
        
        $stmt->bind_param($types, ...$values);
        $stmt->execute();
        
        $affected = $stmt->affected_rows;
        $stmt->close();
        
        return $affected;
    }
    
    // 删除数据
    public function delete($table, $where) {
        $sql = "DELETE FROM {$table} WHERE {$where}";
        return $this->query($sql);
    }
    
    // 转义字符串
    public function escape($str) {
        if ($str === null) {
            return '';
        }
        return $this->conn->real_escape_string($str);
    }
    
    // 关闭数据库连接
    public function close() {
        if ($this->conn) {
            $this->conn->close();
        }
    }
}

// 创建数据库实例
$db = new Database(); 