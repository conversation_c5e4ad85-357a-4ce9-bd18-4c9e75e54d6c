<?php
require_once 'config.php';
require_once 'includes/utils.php';
require_once 'includes/db.php';

// 检查用户是否已登录且为管理员
Utils::checkLogin();

// 检查过期公告并将其优先级设置为普通
$current_date = date('Y-m-d');
$update_expired_sql = "UPDATE announcements SET priority = 0 WHERE valid_to < '{$current_date}' AND priority = 1";
$db->query($update_expired_sql);

// 批量操作处理
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['batch_action']) && isset($_POST['announcement_ids'])) {
    $batch_action = $_POST['batch_action'];
    $announcement_ids = $_POST['announcement_ids'];
    
    if (!empty($announcement_ids) && is_array($announcement_ids)) {
        $ids = implode(',', array_map('intval', $announcement_ids));
        
        if ($batch_action === 'delete') {
            // 批量删除
            $delete_sql = "DELETE FROM announcements WHERE id IN ({$ids})";
            if ($db->query($delete_sql)) {
                $_SESSION['success_message'] = "已成功删除所选公告";
            } else {
                $_SESSION['error_message'] = "删除公告时出错";
            }
        } elseif ($batch_action === 'activate') {
            // 批量激活
            $activate_sql = "UPDATE announcements SET status = 1 WHERE id IN ({$ids})";
            if ($db->query($activate_sql)) {
                $_SESSION['success_message'] = "已成功激活所选公告";
            } else {
                $_SESSION['error_message'] = "激活公告时出错";
            }
        } elseif ($batch_action === 'draft') {
            // 批量设为草稿
            $draft_sql = "UPDATE announcements SET status = 0 WHERE id IN ({$ids})";
            if ($db->query($draft_sql)) {
                $_SESSION['success_message'] = "已成功将所选公告设为草稿";
            } else {
                $_SESSION['error_message'] = "更新公告状态时出错";
            }
        } elseif ($batch_action === 'high_priority') {
            // 批量设为高优先级
            $high_sql = "UPDATE announcements SET priority = 1 WHERE id IN ({$ids})";
            if ($db->query($high_sql)) {
                $_SESSION['success_message'] = "已成功将所选公告设为高优先级";
            } else {
                $_SESSION['error_message'] = "更新公告优先级时出错";
            }
        } elseif ($batch_action === 'normal_priority') {
            // 批量设为普通优先级
            $normal_sql = "UPDATE announcements SET priority = 0 WHERE id IN ({$ids})";
            if ($db->query($normal_sql)) {
                $_SESSION['success_message'] = "已成功将所选公告设为普通优先级";
            } else {
                $_SESSION['error_message'] = "更新公告优先级时出错";
            }
        } elseif ($batch_action === 'extend_month') {
            // 批量延长有效期一个月
            $extend_sql = "UPDATE announcements SET valid_to = DATE_ADD(valid_to, INTERVAL 1 MONTH) WHERE id IN ({$ids})";
            if ($db->query($extend_sql)) {
                $_SESSION['success_message'] = "已成功延长所选公告有效期一个月";
            } else {
                $_SESSION['error_message'] = "延长公告有效期时出错";
            }
        }
        
        // 重定向以避免表单重复提交
        header("Location: announcements.php");
        exit;
    }
}

// 获取筛选参数
$status_filter = isset($_GET['status']) ? intval($_GET['status']) : -1; // -1表示全部，0表示草稿，1表示激活
$priority_filter = isset($_GET['priority']) ? intval($_GET['priority']) : -1; // -1表示全部，0表示普通，1表示高
$search_query = isset($_GET['search']) ? trim($_GET['search']) : '';

// 分页设置
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$items_per_page = 10;
$offset = ($page - 1) * $items_per_page;

// 处理删除操作
if (isset($_GET['action']) && $_GET['action'] == 'delete' && isset($_GET['id'])) {
    $id = intval($_GET['id']);
    
    // 从数据库中删除公告
    $db->delete('announcements', "id = $id");
    
    // 设置成功消息并重定向
    $_SESSION['success_message'] = "公告已成功删除";
    header("Location: announcements.php");
    exit;
}

// 构建查询条件
$where_clause = "";
$conditions = array();

if ($status_filter !== -1) {
    $conditions[] = "status = {$status_filter}";
}

if ($priority_filter !== -1) {
    $conditions[] = "priority = {$priority_filter}";
}

if (!empty($search_query)) {
    $search_query = $db->escape($search_query);
    $conditions[] = "(title LIKE '%{$search_query}%' OR content LIKE '%{$search_query}%')";
}

if (!empty($conditions)) {
    $where_clause = " WHERE " . implode(" AND ", $conditions);
}

// 获取公告总数
$count_sql = "SELECT COUNT(*) as total FROM announcements{$where_clause}";
$result = $db->query($count_sql);
$row = $result->fetch_assoc();
$total_items = $row['total'];
$total_pages = ceil($total_items / $items_per_page);

// 获取公告列表
$announcements_sql = "SELECT * FROM announcements{$where_clause} ORDER BY priority DESC, created_at DESC LIMIT $offset, $items_per_page";
$announcements = $db->getRows($announcements_sql);

$page_title = "管理公告";
$current_page = 'announcements.php';
include 'includes/header.php';
?>

<div class="container mt-4">
    <h1 class="page-title">管理公告</h1>

    <?php if (isset($_SESSION['success_message'])): ?>
        <div class="alert alert-success">
            <?php 
                echo $_SESSION['success_message']; 
                unset($_SESSION['success_message']);
            ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($_SESSION['error_message'])): ?>
        <div class="alert alert-danger">
            <?php 
                echo $_SESSION['error_message']; 
                unset($_SESSION['error_message']);
            ?>
        </div>
    <?php endif; ?>

    <div class="article-dashboard">
        <div class="card article-card">
            <div class="card-header">
                <div class="card-title">公告列表</div>
                <div class="card-actions">
                    <a href="announcement_edit.php" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus"></i> 添加新公告
                    </a>
                    <a href="import_export.php?type=announcement" class="btn btn-secondary btn-sm">
                        <i class="fas fa-file-import"></i> 导入/导出
                    </a>
                    <div class="batch-operation">
                        <select id="batch_action_top" class="form-control form-control-sm">
                            <option value="">批量操作...</option>
                            <option value="activate">设为激活</option>
                            <option value="draft">设为草稿</option>
                            <option value="high_priority">设为高优先级</option>
                            <option value="normal_priority">设为普通优先级</option>
                            <option value="extend_month">延长有效期一个月</option>
                            <option value="delete">删除</option>
                        </select>
                        <button type="button" class="btn btn-secondary btn-sm" id="apply-batch-top">
                            <i class="fas fa-check"></i> 应用
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="filter-section">
                <form action="announcements.php" method="GET" class="search-filter">
                    <div class="filter-row">
                        <div class="search-box">
                            <div class="input-group">
                                <input type="text" name="search" class="form-control" placeholder="搜索公告..." value="<?php echo htmlspecialchars($search_query); ?>">
                            </div>
                        </div>
                        <div class="status-box">
                            <select name="status" class="form-control">
                                <option value="-1"<?php echo $status_filter == -1 ? ' selected' : ''; ?>>所有状态</option>
                                <option value="0"<?php echo $status_filter === 0 ? ' selected' : ''; ?>>草稿</option>
                                <option value="1"<?php echo $status_filter === 1 ? ' selected' : ''; ?>>激活</option>
                            </select>
                        </div>
                        <div class="priority-box">
                            <select name="priority" class="form-control">
                                <option value="-1"<?php echo $priority_filter == -1 ? ' selected' : ''; ?>>所有优先级</option>
                                <option value="0"<?php echo $priority_filter === 0 ? ' selected' : ''; ?>>普通</option>
                                <option value="1"<?php echo $priority_filter === 1 ? ' selected' : ''; ?>>高</option>
                            </select>
                        </div>
                        <div class="filter-btn">
                            <button type="submit" class="btn btn-info">
                                <i class="fas fa-search"></i> 筛选
                            </button>
                        </div>
                    </div>
                </form>
            </div>
            
            <?php if (!empty($announcements)): ?>
                <form id="batch-form" method="POST" action="announcements.php">
                    <input type="hidden" name="batch_action" id="batch_action_hidden" value="">
                    
                    <div class="table-responsive">
                        <table class="table table-striped table-hover announcement-table">
                            <thead>
                                <tr>
                                    <th width="3%"><input type="checkbox" id="select-all"></th>
                                    <th width="5%">ID</th>
                                    <th width="20%">标题</th>
                                    <th width="8%">状态</th>
                                    <th width="8%">优先级</th>
                                    <th width="18%">有效期</th>
                                    <th width="12%">创建时间</th>
                                    <th width="20%">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($announcements as $announcement): ?>
                                    <tr>
                                        <td><input type="checkbox" name="announcement_ids[]" value="<?php echo $announcement['id']; ?>" class="announcement-checkbox"></td>
                                        <td><?php echo htmlspecialchars($announcement['id']); ?></td>
                                        <td class="announcement-title"><?php echo htmlspecialchars($announcement['title']); ?></td>
                                        <td>
                                            <?php if ($announcement['status'] == 1): ?>
                                                <span class="badge badge-success">激活</span>
                                            <?php else: ?>
                                                <span class="badge badge-secondary">草稿</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($announcement['priority'] == 1): ?>
                                                <span class="badge badge-danger">高</span>
                                            <?php else: ?>
                                                <span class="badge badge-info">普通</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php 
                                                echo date('Y-m-d', strtotime($announcement['valid_from'])); 
                                                echo " 至 ";
                                                echo date('Y-m-d', strtotime($announcement['valid_to']));
                                            ?>
                                        </td>
                                        <td><?php echo date('Y-m-d H:i', strtotime($announcement['created_at'])); ?></td>
                                        <td class="announcement-actions">
                                            <a href="announcement_edit.php?id=<?php echo $announcement['id']; ?>" class="btn btn-primary btn-sm" title="编辑">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="announcements.php?action=delete&id=<?php echo $announcement['id']; ?>" 
                                            class="btn btn-danger btn-sm" 
                                            title="删除"
                                            onclick="return confirm('确定要删除这条公告吗？此操作不可撤销。')">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </form>
                
                <!-- 分页 -->
                <?php if ($total_pages > 1): ?>
                    <div class="pagination-container">
                        <ul class="pagination">
                            <li class="page-item <?php echo $page <= 1 ? 'disabled' : ''; ?>">
                                <a class="page-link" href="announcements.php?page=1<?php 
                                    echo $status_filter !== -1 ? '&status='.$status_filter : '';
                                    echo $priority_filter !== -1 ? '&priority='.$priority_filter : '';
                                    echo !empty($search_query) ? '&search='.urlencode($search_query) : '';
                                ?>">首页</a>
                            </li>
                            <li class="page-item <?php echo $page <= 1 ? 'disabled' : ''; ?>">
                                <a class="page-link" href="announcements.php?page=<?php echo max(1, $page - 1); ?><?php 
                                    echo $status_filter !== -1 ? '&status='.$status_filter : '';
                                    echo $priority_filter !== -1 ? '&priority='.$priority_filter : '';
                                    echo !empty($search_query) ? '&search='.urlencode($search_query) : '';
                                ?>">上一页</a>
                            </li>
                            <li class="page-info">
                                <span class="page-text"><?php echo $page; ?>/<?php echo $total_pages; ?></span>
                            </li>
                            <li class="page-item <?php echo $page >= $total_pages ? 'disabled' : ''; ?>">
                                <a class="page-link" href="announcements.php?page=<?php echo min($total_pages, $page + 1); ?><?php 
                                    echo $status_filter !== -1 ? '&status='.$status_filter : '';
                                    echo $priority_filter !== -1 ? '&priority='.$priority_filter : '';
                                    echo !empty($search_query) ? '&search='.urlencode($search_query) : '';
                                ?>">下一页</a>
                            </li>
                            <li class="page-item <?php echo $page >= $total_pages ? 'disabled' : ''; ?>">
                                <a class="page-link" href="announcements.php?page=<?php echo $total_pages; ?><?php 
                                    echo $status_filter !== -1 ? '&status='.$status_filter : '';
                                    echo $priority_filter !== -1 ? '&priority='.$priority_filter : '';
                                    echo !empty($search_query) ? '&search='.urlencode($search_query) : '';
                                ?>">末页</a>
                            </li>
                        </ul>
                    </div>
                <?php endif; ?>
            <?php else: ?>
                <div class="alert alert-info mt-3">暂无公告</div>
            <?php endif; ?>
        </div>

        <div class="card help-card">
            <div class="card-header">
                <div class="card-title">公告管理说明</div>
                <div class="toggle-help"><i class="fas fa-chevron-down"></i></div>
            </div>
            <div class="help-content">
                <div class="help-grid">
                    <div class="help-item">
                        <div class="help-icon"><i class="fas fa-plus-circle"></i></div>
                        <div class="help-text">
                            <h4>添加新公告</h4>
                            <p>创建新的公告内容</p>
                        </div>
                    </div>
                    <div class="help-item">
                        <div class="help-icon"><i class="fas fa-edit"></i></div>
                        <div class="help-text">
                            <h4>编辑</h4>
                            <p>修改已有公告的内容和相关设置</p>
                        </div>
                    </div>
                    <div class="help-item">
                        <div class="help-icon"><i class="fas fa-trash"></i></div>
                        <div class="help-text">
                            <h4>删除</h4>
                            <p>永久删除公告（此操作不可恢复）</p>
                        </div>
                    </div>
                    <div class="help-item">
                        <div class="help-icon"><i class="fas fa-tasks"></i></div>
                        <div class="help-text">
                            <h4>批量操作</h4>
                            <p>批量修改公告状态、优先级和有效期</p>
                        </div>
                    </div>
                    <div class="help-item">
                        <div class="help-icon"><i class="fas fa-filter"></i></div>
                        <div class="help-text">
                            <h4>筛选搜索</h4>
                            <p>按关键词、状态和优先级筛选公告</p>
                        </div>
                    </div>
                    <div class="help-item">
                        <div class="help-icon"><i class="fas fa-file-import"></i></div>
                        <div class="help-text">
                            <h4>导入/导出</h4>
                            <p>批量导入或导出公告数据</p>
                        </div>
                    </div>
                </div>
                <div class="help-footer">
                    <p>公告可以设置优先级、有效期和状态。高优先级的公告会在前端显示时被突出显示。</p>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* 公告管理页面样式 */
.article-dashboard {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.article-card, .help-card {
    margin-bottom: 0;
    box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
    border-radius: 4px;
    overflow: hidden;
    padding: 0;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.card-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #495057;
}

.card-actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

.batch-operation {
    display: flex;
    gap: 5px;
    align-items: center;
    margin-left: 15px;
}

.batch-operation select {
    width: 150px;
}

.filter-section {
    padding: 15px 20px;
    border-bottom: 1px solid #e9ecef;
    background-color: #fff;
}

.search-filter {
    width: 100%;
}

.filter-row {
    display: flex;
    align-items: center;
    gap: 10px;
}

.search-box {
    flex: 1;
    min-width: 200px;
}

.status-box, .priority-box {
    width: 150px;
}

.filter-btn {
    width: auto;
}

.announcement-table {
    margin-bottom: 0;
}

.announcement-table th {
    font-size: 13px;
    font-weight: 600;
    padding: 12px 15px;
    background-color: #f1f3f5;
    border-top: none;
    white-space: nowrap;
}

.announcement-table td {
    padding: 10px 15px;
    vertical-align: middle;
    font-size: 14px;
}

.announcement-title {
    font-weight: 500;
}

.announcement-actions {
    display: flex;
    gap: 5px;
}

.badge {
    padding: 5px 10px;
    border-radius: 3px;
    font-weight: 500;
    font-size: 12px;
}

.badge-info {
    background-color: #17a2b8;
    color: white;
}

.badge-success {
    background-color: #28a745;
    color: white;
}

.badge-secondary {
    background-color: #6c757d;
    color: white;
}

.badge-danger {
    background-color: #dc3545;
    color: white;
}

.pagination-container {
    padding: 15px;
    display: flex;
    justify-content: center;
    border-top: 1px solid #e9ecef;
}

.pagination {
    display: flex;
    gap: 5px;
    list-style: none;
    padding: 0;
    margin: 0;
}

.page-item {
    display: inline-block;
}

.page-link {
    display: block;
    padding: 6px 12px;
    border-radius: 3px;
    text-decoration: none;
    color: #007bff;
    background-color: #fff;
    border: 1px solid #dee2e6;
    transition: all 0.2s;
}

.page-link:hover {
    color: #0056b3;
    background-color: #e9ecef;
    border-color: #dee2e6;
}

.page-item.disabled .page-link {
    color: #6c757d;
    pointer-events: none;
    cursor: not-allowed;
    background-color: #fff;
    border-color: #dee2e6;
}

.page-info {
    display: flex;
    align-items: center;
    margin: 0 8px;
}

.page-text {
    padding: 6px 12px;
    border-radius: 3px;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    color: #495057;
    font-size: 14px;
}

.help-card {
    background-color: white;
}

.toggle-help {
    cursor: pointer;
    color: #6c757d;
    transition: all 0.3s;
}

.toggle-help:hover {
    color: #007bff;
}

.help-content {
    padding: 15px 20px;
}

.help-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
    margin-bottom: 15px;
}

.help-item {
    display: flex;
    gap: 12px;
    align-items: center;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 4px;
    transition: all 0.2s;
}

.help-item:hover {
    background-color: #e9ecef;
    transform: translateY(-2px);
}

.help-icon {
    font-size: 20px;
    color: #007bff;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: rgba(0, 123, 255, 0.1);
}

.help-text h4 {
    margin: 0 0 5px 0;
    font-size: 14px;
    font-weight: 600;
}

.help-text p {
    margin: 0;
    font-size: 12px;
    color: #6c757d;
}

.help-footer {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
}

.help-footer p {
    margin: 0;
    font-size: 14px;
    color: #495057;
}

.alert {
    margin: 15px;
    border-radius: 4px;
}

@media (max-width: 768px) {
    .help-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .card-actions {
        flex-wrap: wrap;
    }
    
    .batch-operation {
        margin-left: 0;
        margin-top: 10px;
        width: 100%;
    }
    
    .batch-operation select {
        flex: 1;
    }
    
    .filter-row {
        flex-wrap: wrap;
    }
    
    .search-box {
        flex: 1 0 100%;
        margin-bottom: 10px;
    }
    
    .status-box, .priority-box {
        flex: 1;
        width: auto;
    }
    
    .filter-btn {
        margin-top: 10px;
        width: 100%;
    }
    
    .filter-btn button {
        width: 100%;
    }
}

@media (max-width: 576px) {
    .help-grid {
        grid-template-columns: 1fr;
    }
    
    .card-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .card-actions {
        margin-top: 10px;
        width: 100%;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 帮助卡片切换
    const toggleHelp = document.querySelector('.toggle-help');
    const helpContent = document.querySelector('.help-content');
    
    toggleHelp.addEventListener('click', function() {
        helpContent.style.display = helpContent.style.display === 'none' ? 'block' : 'none';
        toggleHelp.querySelector('i').classList.toggle('fa-chevron-down');
        toggleHelp.querySelector('i').classList.toggle('fa-chevron-up');
    });
    
    // 默认展开帮助
    helpContent.style.display = 'block';
    
    // 全选/取消全选
    const selectAll = document.getElementById('select-all');
    if (selectAll) {
        selectAll.addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.announcement-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });
        });
    }
    
    // 批量操作表单提交
    const batchForm = document.getElementById('batch-form');
    const batchActionHidden = document.getElementById('batch_action_hidden');
    const batchActionTop = document.getElementById('batch_action_top');
    const applyBatchTop = document.getElementById('apply-batch-top');
    
    if (applyBatchTop && batchForm) {
        applyBatchTop.addEventListener('click', function() {
            const action = batchActionTop.value;
            const selectedAnnouncements = document.querySelectorAll('.announcement-checkbox:checked');
            
            if (!action) {
                alert('请选择一个批量操作');
                return;
            }
            
            if (selectedAnnouncements.length === 0) {
                alert('请至少选择一条公告');
                return;
            }
            
            if (action === 'delete') {
                if (!confirm('确定要删除所选公告吗？此操作不可恢复！')) {
                    return;
                }
            }
            
            batchActionHidden.value = action;
            batchForm.submit();
        });
    }
});
</script>

<?php   
include 'includes/footer.php';
?> 