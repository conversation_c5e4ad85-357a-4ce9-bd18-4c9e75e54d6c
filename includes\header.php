<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . ' - 流星数据查询系统' : '流星数据查询系统'; ?></title>
    <link rel="icon" href="favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="favicon.ico" type="image/x-icon">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <!-- Prism.js 代码高亮 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/themes/prism.min.css">
    <!-- 自定义Markdown样式 -->
    <link rel="stylesheet" href="css/markdown.css">
    <style>
        body {
            font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
            background-color: #f8f9fa;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        .navbar {
            background-color: #3498db !important;
        }
        .navbar-brand, .navbar-nav .nav-link {
            color: #fff !important;
        }
        .navbar-nav .nav-link:hover {
            opacity: 0.8;
        }
        .badge {
            font-weight: 500;
        }
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            margin-bottom: 1.5rem;
        }
        .card-header {
            background-color: rgba(0, 0, 0, 0.03);
            font-weight: 500;
        }
        footer {
            background-color: #343a40;
            color: #ffffff;
            padding: 1.5rem 0;
            margin-top: auto;
        }
        .container {
            padding-top: 1.5rem;
        }
        main {
            flex: 1;
        }
        
        /* 短代码样式 */
        .shortcode {
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
        }
        .warning-box {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
        }
        .info-box {
            background-color: #d1ecf1;
            border-left: 4px solid #17a2b8;
        }
        .tip-box {
            background-color: #d4edda;
            border-left: 4px solid #28a745;
        }
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #eee;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            font-family: monospace;
            overflow-x: auto;
        }
        .quote-block {
            border-left: 4px solid #6c757d;
            background-color: #f8f9fa;
            padding: 15px;
            margin: 15px 0;
            font-style: italic;
        }
        .shortcode-button {
            display: inline-block;
            padding: 8px 15px;
            margin: 5px 0;
            background-color: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            transition: background-color 0.3s;
        }
        .shortcode-button:hover {
            background-color: #2980b9;
            color: white;
            text-decoration: none;
        }
        .shortcode-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        .shortcode-table th,
        .shortcode-table td {
            border: 1px solid #dee2e6;
            padding: 8px 12px;
            text-align: left;
        }
        .shortcode-table th {
            background-color: #f8f9fa;
        }
        .table-responsive {
            overflow-x: auto;
        }
        .download-box {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px 15px;
            margin: 15px 0;
        }
        .download-link {
            display: flex;
            align-items: center;
            text-decoration: none;
            color: #3498db;
        }
        .download-link:hover {
            text-decoration: underline;
        }
        .download-icon {
            margin-right: 10px;
            font-size: 1.2em;
        }
        .download-filename {
            font-weight: bold;
            margin-right: 5px;
        }
        .file-size {
            color: #6c757d;
            font-size: 0.9em;
        }
        .inline-code {
            background-color: #f8f9fa;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 0.9em;
            border: 1px solid #eee;
        }
        
        /* 文章目录样式 */
        .article-toc {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
            position: relative;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .article-toc h4 {
            margin-top: 0;
            margin-bottom: 12px;
            color: #212529;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 8px;
            font-size: 1.1em;
        }
        
        .toc-list, .toc-sub-list {
            list-style-type: none;
            padding-left: 0;
        }
        
        .toc-sub-list {
            padding-left: 1.5em;
            margin-top: 6px;
            margin-bottom: 6px;
        }
        
        .toc-item {
            margin-bottom: 6px;
            line-height: 1.4;
        }
        
        .toc-item a {
            color: #495057;
            text-decoration: none;
        }
        
        .toc-item a:hover {
            color: #0056b3;
            text-decoration: underline;
        }
        
        .toc-item a.active {
            color: #0056b3;
            font-weight: bold;
            position: relative;
        }
        
        .toc-item a.active::before {
            content: "▶";
            position: absolute;
            left: -15px;
            font-size: 0.7em;
            color: #0056b3;
            top: 3px;
        }
        
        .toc-level-1 {
            font-weight: 600;
        }
        
        .toc-level-2 {
            font-weight: 500;
        }
        
        .toc-level-3 {
            font-weight: 400;
        }
        
        /* 小屏幕上的目录样式 */
        @media (max-width: 768px) {
            .article-toc {
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="index.php">流星数据查询系统</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="announcements.php">公告列表</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="view_test.php">数据库视图</a>
                    </li>

                    
                </ul>
            </div>
        </div>
    </nav>

    <main>
    <?php
    // 显示提示消息
    if (isset($_SESSION['error_message'])) {
        echo '<div class="container mt-3"><div class="alert alert-danger">' . $_SESSION['error_message'] . '</div></div>';
        unset($_SESSION['error_message']);
    }
    if (isset($_SESSION['success_message'])) {
        echo '<div class="container mt-3"><div class="alert alert-success">' . $_SESSION['success_message'] . '</div></div>';
        unset($_SESSION['success_message']);
    }
    ?>
</main>
</body>
</html> 