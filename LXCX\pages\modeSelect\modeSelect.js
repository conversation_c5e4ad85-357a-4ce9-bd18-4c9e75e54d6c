Page({
  data: {
    modes: [
      {
        id: 'assassinate',
        name: '暗杀模式',
        description: '8V8团战行动，一击必杀',
        icon: '/images/assassinate.png'
      },
      {
        id: 'deathmatch',
        name: '死斗模式',
        description: '22/33/44激烈对战，生死较量',
        icon: '/images/deathmatch.png'
      },
      {
        id: 'boss',
        name: '战绩统计',
        description: '显示玩家的所有战绩',
        icon: '/images/ranking.png'
      }
    ],
    luckpost: '加载中...'
  },

  onLoad: function() {
    // 页面加载时的处理逻辑
    console.log('模式选择页面加载');
    this.getLuckPost();
  },

  // 获取随机luckpost
  getLuckPost: function() {
    const api = require('../../utils/api.js');
    api.getLuckPost().then(res => {
      // 根据API返回的数据结构，幸运帖内容在 res.xst 字段中
      this.setData({
        luckpost: res.xst || '今日运势：吉'
      });
    }).catch(err => {
      console.error('获取luckpost失败:', err);
      this.setData({
        luckpost: '今日运势：吉'
      });
    });
  },

  onShow: function() {
    // 页面显示时的处理逻辑
  },

  // 按钮点击事件：根据 data-mode 跳转不同页面
  goToMode(e) {
    const mode = e.currentTarget.dataset.mode;
    let url = '';
    
    // 根据模式判断跳转地址
    switch (mode) {
      case 'assassinate':
        url = '/pages/assassinate/assassinate';
        break;
      case 'deathmatch':
        url = '/pages/deathmatch/deathmatch';
        break;
      case 'boss':
        url = '/pages/match/match';
        break;
      case 'other':
        url = '/pages/other/other';
        break;
      default:
        url = '';
    }

    if (url) {
      wx.navigateTo({
        url: url,
        success: () => {
          console.log('页面跳转成功');
        },
        fail: (err) => {
          console.error('页面跳转失败', err);
          wx.showToast({
            title: '页面跳转失败',
            icon: 'none',
            duration: 2000
          });
        }
      });
    }
  },

  // 返回上一页
  onBack: function() {
    wx.navigateBack({
      delta: 1
    });
  },

  // 分享给好友
  onShareAppMessage: function () {
    return {
      title: this.data.shareTitle,
      imageUrl: this.data.shareImageUrl,
      path: '/pages/modeSelect/modeSelect',
      success: function(res) {
        wx.showToast({
          title: '分享成功',
          icon: 'success',
          duration: 2000
        });
      },
      fail: function(res) {
        wx.showToast({
          title: '分享失败',
          icon: 'none',
          duration: 2000
        });
      }
    }
  },

  // 分享到朋友圈
  onShareTimeline: function () {
    return {
      title: this.data.shareTitle,
      imageUrl: this.data.shareImageUrl,
      query: '',
      success: function(res) {
        wx.showToast({
          title: '分享成功',
          icon: 'success',
          duration: 2000
        });
      },
      fail: function(res) {
        wx.showToast({
          title: '分享失败',
          icon: 'none',
          duration: 2000
        });
      }
    }
  },
})