<?php
require_once 'config.php';
require_once 'includes/db.php';
require_once 'includes/utils.php';

// 检查登录状态
Utils::checkLogin();

// 检查是否是管理员
if (!isset($_SESSION['is_admin']) || $_SESSION['is_admin'] != 1) {
    $_SESSION['message'] = '您没有权限执行此操作';
    $_SESSION['message_type'] = 'danger';
    header('Location: index.php');
    exit;
}

echo "<h1>数据库更新</h1>";

// 更新文章表的content字段类型
$sql = "ALTER TABLE articles MODIFY content LONGTEXT;";

if ($db->query($sql)) {
    echo "<p>成功更新articles表的content字段类型为LONGTEXT</p>\n";
} else {
    echo "<p>更新失败，请检查数据库权限和连接</p>\n";
}

// 添加摘要字段到文章表
$sql = "ALTER TABLE articles ADD COLUMN summary TEXT AFTER content";
if ($db->query($sql)) {
    echo "<p>成功添加摘要字段到文章表</p>";
} else {
    echo "<p>添加摘要字段失败</p>";
}

// 检查games表是否有game_mode字段
$checkSql = "SHOW COLUMNS FROM games LIKE 'game_mode'";
$result = $db->getRow($checkSql);

// 如果已经存在，显示提示
if ($result) {
    echo "<p>game_mode字段已存在，无需添加</p>";
} else {
    // 添加game_mode字段
    $alterSql = "ALTER TABLE games ADD COLUMN game_mode VARCHAR(50) DEFAULT '暗杀模式' COMMENT '游戏模式'";
    try {
        $db->query($alterSql);
        
        // 更新现有记录，为每条记录分配一个游戏模式
        // 随机分配，三分之一的记录为每种模式
        $updateSql1 = "UPDATE games SET game_mode = '暗杀模式' WHERE id % 3 = 0";
        $updateSql2 = "UPDATE games SET game_mode = '死斗模式' WHERE id % 3 = 1";
        $updateSql3 = "UPDATE games SET game_mode = '盟主模式' WHERE id % 3 = 2";
        
        $db->query($updateSql1);
        $db->query($updateSql2);
        $db->query($updateSql3);
        
        // 记录活动
        Utils::logActivity('数据库更新', '成功添加game_mode字段到games表');
        
        echo "<p>成功添加game_mode字段，并为现有记录分配了游戏模式</p>";
    } catch (Exception $e) {
        // 记录错误
        error_log('添加game_mode字段失败: ' . $e->getMessage());
        
        echo "<p>添加game_mode字段失败: " . $e->getMessage() . "</p>";
    }
}

echo "<p>数据库更新完成</p>";
echo "<p><a href='index.php' class='btn btn-primary'>返回主页</a></p>";
?> 