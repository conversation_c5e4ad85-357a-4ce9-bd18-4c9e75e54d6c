<?php
require_once 'config.php';
require_once 'includes/db.php';
require_once 'includes/utils.php';
require_once 'includes/excel_handler.php';

// 包含头部
include 'includes/header.php';
?>

<div class="main-content">
    <div class="container">
<?php
// 检查ID参数
if (!isset($_GET['id']) || empty($_GET['id'])) {
    $_SESSION['message'] = '缺少游戏ID参数';
    $_SESSION['message_type'] = 'danger';
    header('Location: index.php');
    exit;
}

$game_id = (int)$_GET['id'];

// 获取游戏数据
$sql = "SELECT * FROM games WHERE id = {$game_id}";
$game = $db->getRow($sql);

if (!$game) {
    $_SESSION['message'] = '未找到指定游戏数据';
    $_SESSION['message_type'] = 'danger';
    header('Location: index.php');
    exit;
}

// 获取游戏记录
$sql = "SELECT gr.*, p.nickname, p.virtual_ip, p.player_rank 
        FROM game_records gr
        JOIN players p ON gr.player_id = p.id
        WHERE gr.game_id = {$game_id}
        ORDER BY gr.id ASC";
$records = $db->getRows($sql);

$success = '';
$error = '';

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['save_records'])) {
        // 更新游戏记录
        $success_count = 0;
        $error_count = 0;
        
        foreach ($_POST['records'] as $record_id => $record) {
            $record_id = (int)$record_id;
            
            // 验证数据
            $kills = isset($record['kills']) ? (int)$record['kills'] : 0;
            $deaths = isset($record['deaths']) ? (int)$record['deaths'] : 0;
            $team = isset($record['team']) ? Utils::sanitizeInput($record['team']) : '';
            $wins = isset($record['wins']) ? (int)$record['wins'] : 0;
            
            // 更新记录
            $sql = "UPDATE game_records SET 
                    kills = {$kills},
                    deaths = {$deaths},
                    team = '{$team}',
                    wins = {$wins}
                    WHERE id = {$record_id} AND game_id = {$game_id}";
            
            if ($db->query($sql)) {
                $success_count++;
            } else {
                $error_count++;
            }
        }
        
        if ($error_count > 0) {
            $error = "保存失败：{$error_count} 条记录更新失败";
        } else {
            $success = "成功更新 {$success_count} 条记录";
            Utils::logActivity('更新数据', "更新游戏数据：ID {$game_id}，更新 {$success_count} 条记录");
            
            // 重新获取游戏记录
            $sql = "SELECT gr.*, p.nickname, p.virtual_ip, p.player_rank 
                    FROM game_records gr
                    JOIN players p ON gr.player_id = p.id
                    WHERE gr.game_id = {$game_id}
                    ORDER BY gr.id ASC";
            $records = $db->getRows($sql);
        }
    }
}

// Excel文件路径
$excel_path = EXCEL_DIR . $game['excel_file'];
?>
<h1 class="page-title">表格编辑</h1>

<?php if (!empty($error)): ?>
    <div class="alert alert-danger"><?php echo $error; ?></div>
<?php endif; ?>

<?php if (!empty($success)): ?>
    <div class="alert alert-success"><?php echo $success; ?></div>
<?php endif; ?>

<div class="card">
    <div class="card-title">游戏信息</div>
    <div class="row">
        <div class="col-md-6">
            <p><strong>唯一ID：</strong> <?php echo $game['unique_id']; ?></p>
            <p><strong>游戏类型：</strong> 
                <span class="game-type game-type-<?php echo $game['game_type'] == '暗杀' ? 'assassination' : ($game['game_type'] == '死斗' ? 'deathmatch' : 'alliance'); ?>">
                    <?php echo $game['game_type']; ?>
                </span>
            </p>
            <p><strong>上传时间：</strong> <?php echo date('Y-m-d H:i:s', strtotime($game['upload_time'])); ?></p>
            <p><strong>Excel文件：</strong> <?php echo $game['excel_file']; ?></p>
        </div>
        <div class="col-md-6">
            <div class="image-preview" style="max-width: 300px;">
                <img src="<?php echo IMAGES_DIR . $game['image_file']; ?>" alt="游戏图片" style="max-width: 100%;">
            </div>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-title">表格数据编辑</div>
    <?php if (empty($records)): ?>
        <div class="alert alert-info">暂无数据记录</div>
    <?php else: ?>
        <form method="POST" action="create.php?id=<?php echo $game_id; ?>">
            <div class="excel-preview">
                <table class="edit-table">
                    <thead>
                        <tr>
                            <th>玩家昵称</th>
                            <th>大厅虚拟IP</th>
                            <th>大厅军衔</th>
                            <th>狙杀</th>
                            <th>死亡</th>
                            <th>分组</th>
                            <th>胜场</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($records as $record): ?>
                            <tr data-id="<?php echo $record['id']; ?>">
                                <td><?php echo $record['nickname']; ?></td>
                                <td><?php echo $record['virtual_ip']; ?></td>
                                <td><?php echo $record['player_rank'] ? $record['player_rank'] : '无数据'; ?></td>
                                <td data-editable="true" data-type="number" data-field="kills" data-save-callback="saveExcelEdit">
                                    <input type="number" name="records[<?php echo $record['id']; ?>][kills]" value="<?php echo $record['kills']; ?>" min="0">
                                </td>
                                <td data-editable="true" data-type="number" data-field="deaths" data-save-callback="saveExcelEdit">
                                    <input type="number" name="records[<?php echo $record['id']; ?>][deaths]" value="<?php echo $record['deaths']; ?>" min="0">
                                </td>
                                <td data-editable="true" data-field="team" data-save-callback="saveExcelEdit">
                                    <select name="records[<?php echo $record['id']; ?>][team]" class="form-select">
                                        <option value="流星" <?php echo $record['team'] == '流星' ? 'selected' : ''; ?>>流星</option>
                                        <option value="蝴蝶" <?php echo $record['team'] == '蝴蝶' ? 'selected' : ''; ?>>蝴蝶</option>
                                    </select>
                                </td>
                                <td data-editable="true" data-type="number" data-field="wins" data-save-callback="saveExcelEdit">
                                    <input type="number" name="records[<?php echo $record['id']; ?>][wins]" value="<?php echo $record['wins']; ?>" min="0">
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <div class="form-group" style="margin-top: 20px;">
                <button type="submit" name="save_records" class="btn btn-primary">保存修改</button>
                <a href="index.php" class="btn btn-secondary">返回列表</a>
            </div>
        </form>
    <?php endif; ?>
</div>

<script>
// 页面加载完后初始化表格编辑功能
document.addEventListener('DOMContentLoaded', function() {
    initExcelEditor();
});

// 保存单元格编辑
function saveExcelEdit(recordId, field, value) {
    // 直接使用AJAX更新数据
    fetch('ajax_handler.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `action=save_excel_cell&record_id=${recordId}&field=${field}&value=${value}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            // 成功保存
            showToast('单元格已更新', 'success');
        } else {
            showToast('保存失败：' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('保存失败：网络错误', 'error');
    });
}

// 初始化Excel编辑器
function initExcelEditor() {
    // 表格已使用表单元素直接编辑，无需额外初始化
}
</script>
    </div><!-- 结束 .container -->
</div><!-- 结束 .main-content -->

<?php
// 包含底部
include 'includes/footer.php';
?> 