/**app.wxss**/
/* iconfont 字体图标 */
/*
@font-face {
  font-family: 'iconfont';
  src: url('data:application/x-font-woff2;charset=utf-8;base64,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') format('woff2');
  font-weight: normal;
  font-style: normal;
}
*/

.iconfont {
  font-family: 'iconfont' !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-like:before {
  content: "\e645";
}

.icon-like-filled:before {
  content: "\e644";
}

.icon-star:before {
  content: "\e662";
}

.icon-star-filled:before {
  content: "\e663";
}

.icon-share:before {
  content: "\e624";
}

.icon-wechat:before {
  content: "\e6ea";
}

.icon-picture:before {
  content: "\e62b";
}

page {
  background-color: #f8f8f8;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 'PingFang SC', 'Microsoft YaHei', sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
}

.container {
  padding: 20rpx;
  box-sizing: border-box;
}

/* 卡片样式 */
.card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

/* 搜索框样式 */
.search-box {
  background-color: #eeeeee;
  border-radius: 30rpx;
  padding: 15rpx 30rpx;
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 10rpx;
}

.search-input {
  flex: 1;
  height: 32rpx;
  line-height: 32rpx;
  font-size: 28rpx;
}

/* 分隔线样式 */
.divider {
  height: 1rpx;
  background-color: #e5e5e5;
  margin: 20rpx 0;
}

/* 主按钮样式 */
.btn-primary {
  background-color: #4A90E2;
  color: #fff;
  border-radius: 8rpx;
  padding: 20rpx 40rpx;
  text-align: center;
  font-size: 28rpx;
}

/* 次按钮样式 */
.btn-secondary {
  background-color: #f5f5f5;
  color: #666;
  border-radius: 8rpx;
  padding: 20rpx 40rpx;
  text-align: center;
  font-size: 28rpx;
}

/* 文本溢出省略 */
.ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

/* 分类选项卡激活样式-全局优先级 */
.category-tabs .tab-item.active {
  color: #ffc700 !important; 
  font-weight: bold !important;
}

.category-tabs .tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #ffc700 !important;
  border-radius: 2rpx;
} 