# Article Summaries Batch API Endpoint

## Overview

The `article_summaries_batch` endpoint provides batch functionality for retrieving article summaries, allowing clients to request summaries for multiple articles in a single API call instead of making individual requests for each article.

## Endpoint Details

- **URL**: `/admin/api.php?path=article_summaries_batch`
- **Method**: `GET`
- **Authentication**: Requires valid API key (X-API-Key header or api_key parameter)

## Parameters

### Required Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `ids` | string | Article IDs to retrieve. Supports two formats:<br/>1. Comma-separated string: `"1,2,3"`<br/>2. JSON array string: `"[1,2,3]"` |

## Request Examples

### Comma-separated format
```
GET /admin/api.php?path=article_summaries_batch&ids=1,2,3
```

### JSON array format
```
GET /admin/api.php?path=article_summaries_batch&ids=[1,2,3]
```

### Single article
```
GET /admin/api.php?path=article_summaries_batch&ids=1
```

## Response Format

### Success Response

```json
{
    "status": "success",
    "message": "批量获取文章摘要成功，找到3篇文章",
    "data": {
        "requested_ids": [1, 2, 3],
        "found_articles": [
            {
                "id": "1",
                "title": "Article Title 1",
                "summary": "Article summary...",
                "category_id": "1",
                "category_name": "Category Name",
                "create_time": "2025-01-01 12:00:00",
                "update_time": "2025-01-01 12:00:00"
            },
            // ... more articles
        ],
        "not_found_ids": [],
        "total_requested": 3,
        "total_found": 3
    }
}
```

### Partial Success Response (some articles not found)

```json
{
    "status": "success",
    "message": "批量获取文章摘要成功，找到2篇文章，1篇文章未找到或未发布",
    "data": {
        "requested_ids": [1, 2, 999],
        "found_articles": [
            // ... found articles
        ],
        "not_found_ids": [999],
        "total_requested": 3,
        "total_found": 2
    }
}
```

### Error Response

```json
{
    "status": "error",
    "message": "Error description",
    "data": null
}
```

## Response Fields

### Data Object Fields

| Field | Type | Description |
|-------|------|-------------|
| `requested_ids` | array | Array of article IDs that were requested |
| `found_articles` | array | Array of article objects that were found |
| `not_found_ids` | array | Array of article IDs that were not found or not published |
| `total_requested` | integer | Total number of articles requested |
| `total_found` | integer | Total number of articles found |

### Article Object Fields

| Field | Type | Description |
|-------|------|-------------|
| `id` | string | Article ID |
| `title` | string | Article title |
| `summary` | string\|null | Article summary (may be null) |
| `category_id` | string | Category ID |
| `category_name` | string\|null | Category name |
| `create_time` | string | Creation timestamp |
| `update_time` | string | Last update timestamp |

## Error Conditions

| Error Message | Cause |
|---------------|-------|
| `缺少文章ID参数` | Missing `ids` parameter |
| `无效的JSON格式` | Invalid JSON format in `ids` parameter |
| `没有提供有效的文章ID` | No valid article IDs provided (empty or all invalid) |
| `批量查询数量不能超过50个` | More than 50 article IDs requested |
| `未找到任何指定的文章或文章未发布` | None of the requested articles were found or published |
| `不支持的请求方法` | Non-GET request method used |

## Features

### Input Validation
- Filters out invalid IDs (≤ 0)
- Supports both comma-separated and JSON array formats
- Enforces batch size limit (maximum 50 articles per request)

### Error Handling
- Gracefully handles missing articles
- Provides detailed information about found vs. not found articles
- Returns appropriate HTTP status codes and error messages

### Performance Optimization
- Uses single SQL query with IN clause for efficient batch retrieval
- Maintains original order of requested IDs in results
- Includes category information via JOIN

### Security
- Only returns published articles (`published = 1`)
- Input sanitization for all parameters
- API key authentication required

## Usage Recommendations

1. **Batch Size**: Keep batch sizes reasonable (≤ 50 articles) for optimal performance
2. **Error Handling**: Always check both `status` and `not_found_ids` in responses
3. **Caching**: Consider caching results on the client side for frequently accessed articles
4. **Fallback**: Have fallback logic for articles that are not found

## Comparison with Single Article Endpoint

| Feature | `article_summary` | `article_summaries_batch` |
|---------|-------------------|---------------------------|
| Request method | Single ID | Multiple IDs |
| Response format | Single article or error | Batch result with metadata |
| Performance | One query per article | One query for all articles |
| Error handling | Binary (success/error) | Partial success supported |
| Use case | Individual article access | Bulk operations, list views |
