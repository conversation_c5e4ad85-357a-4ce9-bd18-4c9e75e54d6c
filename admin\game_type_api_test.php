<?php
require_once 'config.php';
require_once 'includes/db.php';
require_once 'includes/utils.php';

// 包含头部
include 'includes/header.php';

// 检查登录状态
Utils::checkLogin();
?>

<div class="container mt-4">
    <h1 class="mb-4">API接口测试页面</h1>
    
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">游戏类型数据接口测试</h5>
        </div>
        <div class="card-body">
            <form id="gameStatsForm" class="mb-3">
                <div class="row g-3 align-items-end">
                    <div class="col-md-3">
                        <label for="gameType" class="form-label">游戏类型</label>
                        <select class="form-select" id="gameType" name="type">
                            <option value="暗杀">暗杀</option>
                            <option value="死斗">死斗</option>
                            <option value="盟主">盟主</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="searchTerm" class="form-label">搜索关键词</label>
                        <input type="text" class="form-control" id="searchTerm" name="search" placeholder="玩家昵称或军衔">
                    </div>
                    <div class="col-md-2">
                        <label for="sortBy" class="form-label">排序字段</label>
                        <select class="form-select" id="sortBy" name="sort">
                            <option value="kills">击杀数</option>
                            <option value="deaths">死亡数</option>
                            <option value="kd">KD值</option>
                            <option value="wins">胜场</option>
                            <option value="losses">败场</option>
                            <option value="nickname">昵称</option>
                            <option value="rank">军衔</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="orderBy" class="form-label">排序方向</label>
                        <select class="form-select" id="orderBy" name="order">
                            <option value="desc">降序</option>
                            <option value="asc">升序</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-primary w-100">查询</button>
                    </div>
                </div>
            </form>
            
            <div class="result-container">
                <h6>接口响应：</h6>
                <pre id="gameStatsResult" class="bg-light p-3 rounded" style="max-height: 400px; overflow-y: auto;">请点击查询按钮获取数据...</pre>
            </div>
        </div>
    </div>
    
    <div class="card mb-4">
        <div class="card-header bg-success text-white">
            <h5 class="mb-0">玩家详细数据接口测试</h5>
        </div>
        <div class="card-body">
            <form id="playerDetailForm" class="mb-3">
                <div class="row g-3 align-items-end">
                    <div class="col-md-4">
                        <label for="playerNickname" class="form-label">玩家昵称</label>
                        <input type="text" class="form-control" id="playerNickname" name="nickname" placeholder="输入玩家昵称">
                    </div>
                    <div class="col-md-4">
                        <label for="playerGameType" class="form-label">游戏类型（可选）</label>
                        <select class="form-select" id="playerGameType" name="type">
                            <option value="">全部</option>
                            <option value="暗杀">暗杀</option>
                            <option value="死斗">死斗</option>
                            <option value="盟主">盟主</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <button type="submit" class="btn btn-success w-100">查询</button>
                    </div>
                </div>
            </form>
            
            <div class="result-container">
                <h6>接口响应：</h6>
                <pre id="playerDetailResult" class="bg-light p-3 rounded" style="max-height: 400px; overflow-y: auto;">请点击查询按钮获取数据...</pre>
            </div>
        </div>
    </div>
    
    <div class="card mb-4">
        <div class="card-header bg-info text-white">
            <h5 class="mb-0">游戏类型汇总统计接口测试</h5>
        </div>
        <div class="card-body">
            <div class="d-grid">
                <button id="gameSummaryBtn" class="btn btn-info">获取汇总统计</button>
            </div>
            
            <div class="result-container mt-3">
                <h6>接口响应：</h6>
                <pre id="gameSummaryResult" class="bg-light p-3 rounded" style="max-height: 400px; overflow-y: auto;">请点击按钮获取数据...</pre>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 游戏类型数据接口测试
    document.getElementById('gameStatsForm').addEventListener('submit', function(e) {
        e.preventDefault();
        const form = this;
        const resultContainer = document.getElementById('gameStatsResult');
        
        resultContainer.textContent = '加载中...';
        
        const params = new URLSearchParams();
        params.append('type', form.querySelector('[name="type"]').value);
        if (form.querySelector('[name="search"]').value) {
            params.append('search', form.querySelector('[name="search"]').value);
        }
        params.append('sort', form.querySelector('[name="sort"]').value);
        params.append('order', form.querySelector('[name="order"]').value);
        
        fetch(`api/get_game_type_status.php?${params.toString()}`)
            .then(response => response.json())
            .then(data => {
                resultContainer.textContent = JSON.stringify(data, null, 2);
            })
            .catch(error => {
                resultContainer.textContent = `请求失败: ${error.message}`;
            });
    });
    
    // 玩家详细数据接口测试
    document.getElementById('playerDetailForm').addEventListener('submit', function(e) {
        e.preventDefault();
        const form = this;
        const resultContainer = document.getElementById('playerDetailResult');
        
        resultContainer.textContent = '加载中...';
        
        const nickname = form.querySelector('[name="nickname"]').value;
        if (!nickname) {
            resultContainer.textContent = '请输入玩家昵称';
            return;
        }
        
        const params = new URLSearchParams();
        params.append('nickname', nickname);
        if (form.querySelector('[name="type"]').value) {
            params.append('type', form.querySelector('[name="type"]').value);
        }
        
        fetch(`api/get_game_type_player_details.php?${params.toString()}`)
            .then(response => response.json())
            .then(data => {
                resultContainer.textContent = JSON.stringify(data, null, 2);
            })
            .catch(error => {
                resultContainer.textContent = `请求失败: ${error.message}`;
            });
    });
    
    // 游戏类型汇总统计接口测试
    document.getElementById('gameSummaryBtn').addEventListener('click', function() {
        const resultContainer = document.getElementById('gameSummaryResult');
        
        resultContainer.textContent = '加载中...';
        
        fetch('api/get_game_type_summary.php')
            .then(response => response.json())
            .then(data => {
                resultContainer.textContent = JSON.stringify(data, null, 2);
            })
            .catch(error => {
                resultContainer.textContent = `请求失败: ${error.message}`;
            });
    });
});
</script>

<?php
// 包含底部
include 'includes/footer.php';
?>