<?php
require_once '../config.php';

// 连接到MySQL服务器
$conn = new mysqli(DB_HOST, DB_USER, DB_PASS);

// 检查连接
if ($conn->connect_error) {
    die("连接失败: " . $conn->connect_error);
}

// 创建数据库（如果不存在）
$sql = "CREATE DATABASE IF NOT EXISTS " . DB_NAME;
if ($conn->query($sql) === TRUE) {
    echo "数据库 " . DB_NAME . " 创建成功或已存在<br>";
} else {
    die("创建数据库失败: " . $conn->error);
}

// 选择数据库
$conn->select_db(DB_NAME);

// 创建announcements表
$sql = "CREATE TABLE IF NOT EXISTS `announcements` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `content` text,
  `published` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";

if ($conn->query($sql) === TRUE) {
    echo "创建announcements表成功<br>";
} else {
    echo "创建announcements表失败: " . $conn->error . "<br>";
}

// 创建articles表
$sql = "CREATE TABLE IF NOT EXISTS `articles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `content` text,
  `summary` text,
  `author` varchar(100) DEFAULT NULL,
  `cover_image` varchar(255) DEFAULT NULL,
  `category_id` int(11) DEFAULT NULL,
  `view_count` int(11) NOT NULL DEFAULT '0',
  `published` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";

if ($conn->query($sql) === TRUE) {
    echo "创建articles表成功<br>";
} else {
    echo "创建articles表失败: " . $conn->error . "<br>";
}

// 插入示例公告数据
$sql = "INSERT INTO `announcements` (`title`, `content`, `published`, `created_at`, `updated_at`) VALUES
('服务器维护公告', '尊敬的玩家，我们将于2023年7月15日凌晨2:00-6:00进行服务器维护，期间游戏将无法登录，请您提前做好准备。感谢您的理解与支持！', 1, NOW(), NOW()),
('新赛季开启公告', '新赛季荣耀征程将于下周一正式开启，全新的积分规则和丰厚的赛季奖励等你来挑战！', 1, NOW(), NOW()),
('游戏版本更新公告', '游戏将更新到v2.5.0版本，新增多名英雄和地图，优化了游戏体验，详情请查看更新日志。', 1, NOW(), NOW());";

if ($conn->query($sql) === TRUE) {
    echo "插入示例公告数据成功<br>";
} else {
    echo "插入示例公告数据失败: " . $conn->error . "<br>";
}

// 插入示例文章数据
$sql = "INSERT INTO `articles` (`title`, `content`, `summary`, `author`, `category_id`, `published`, `created_at`, `updated_at`) VALUES
('新手入门指南：如何快速提升KD值', '详细介绍如何提高KD值的方法和技巧...', '本文将从武器选择、走位技巧、意识培养三个方面详细介绍如何快速提升您的KD值', '战术大师', 1, 1, NOW(), NOW()),
('2.5版本更新解析', '详细介绍新版本的变化和改进...', '本文深入分析了2.5版本的平衡性调整、新增内容及对游戏节奏的影响', '资深玩家', 2, 1, NOW(), NOW()),
('BDLX年度联赛回顾', '回顾本年度联赛的精彩瞬间...', '盘点年度联赛的精彩比赛、MVP玩家及各战队表现', '赛事解说', 3, 1, NOW(), NOW());";

if ($conn->query($sql) === TRUE) {
    echo "插入示例文章数据成功<br>";
} else {
    echo "插入示例文章数据失败: " . $conn->error . "<br>";
}

echo "<br>安装完成！";

// 关闭连接
$conn->close();
?> 