// 引入API请求
const api = require('../../../utils/api')
const util = require('../../../utils/util')

Page({
  data: {
    nickname: '',
    player: null,
    playerGames: [], // 添加玩家游戏场次列表数据
    currentTab: 0,
    tabs: ['总览', '对战历史'],
    loading: true,
    showTip: false,
    banCheckTimer: null, // 添加定时器变量
    showImagePreview: false, // 是否显示图片预览
    previewImageUrl: '', // 预览图片的URL
    previewGameInfo: null, // 保存游戏信息，可用于显示额外数据
    // 游戏类型相关
    currentGameType: '暗杀', // 当前选中的游戏类型
    gameTypes: ['暗杀', '死斗', '盟主'], // 可选的游戏类型
    gameTypeData: {} // 存储不同游戏类型的数据
  },

  onLoad: function (options) {
    if (options.nickname) {
      const nickname = decodeURIComponent(options.nickname)
      this.setData({ 
        nickname: nickname,
        loading: true 
      })
      
      // 立即获取封禁状态
      api.getPlayerBanStatus(nickname)
        .then(data => {
          // 保存ban_type
          this.setData({
            'player': {
              nickname: nickname,
              is_banned: data.is_banned,
              ban_type: data.ban_type
            }
          })
          
          // 加载其他玩家数据
          return this.loadPlayerDetail(nickname)
        })
        .then(() => {
          // 启动定时检查
          this.startBanStatusCheck()
        })
        .catch(error => {
          console.error('数据加载失败:', error)
          util.showToast('获取数据失败')
        })
    } else {
      util.showToast('参数错误')
      setTimeout(function() {
        wx.navigateBack()
      }, 1500)
    }
  },
  
  // 加载玩家详情 - 暗杀模式
  loadPlayerDetail: function (nickname) {
    return new Promise((resolve, reject) => {
      Promise.all([
        api.getPlayer(nickname),
        api.getPlayerGameCount(nickname),
        api.getPlayerDetails(nickname, { game_type: '暗杀' }), // 指定获取暗杀模式的玩家详情数据
        api.getPlayerGames(nickname, { game_type: '暗杀' }) // 指定获取暗杀模式的游戏数据
      ])
        .then(([player, gameCount, playerDetails, playerGames]) => {
          // 保持封禁状态
          const isBanned = this.data.player ? this.data.player.is_banned : false
          const banType = this.data.player ? this.data.player.ban_type : null
        
          // 过滤暗杀模式游戏数据 - 修复：创建新变量而不是修改filter的结果
          const filteredGames = playerGames.filter(game =>
            game.game_type === '暗杀' ||
            game.game_type === '暗杀模式' ||
            (game.game_type && game.game_type.includes('暗杀'))
          )
          
          // 计算暗杀模式统计数据
          const stats = this.calculateStats(filteredGames)
          
          // 合并数据
          player.excel_count = stats.total_games || 0
          player.total_kills = playerDetails.total_kills || stats.total_kills || 0
          player.total_deaths = playerDetails.total_deaths || stats.total_deaths || 0
          player.kd = playerDetails.kd || stats.kd || '0.00'
          player.total_wins = playerDetails.total_wins || stats.total_wins || 0
          player.total_losses = playerDetails.total_losses || stats.total_losses || 0
          player.win_rate = playerDetails.win_rate ? playerDetails.win_rate + '%' : stats.win_rate || '0.00%'
          player.is_banned = isBanned // 保持封禁状态
          player.ban_type = banType // 新增
          player.kpr = playerDetails.kpr || '0.00' // 直接使用API返回的KPR
          player.dpr = playerDetails.dpr || '0.00' // 直接使用API返回的DPR
          player.impact = playerDetails.impact || '0.00' // 直接使用API返回的impact
          
          // 处理游戏场次数据 - 修复：使用新数组存储处理后的游戏数据
          const formattedGames = [];
          if (filteredGames && filteredGames.length > 0) {
            filteredGames.forEach(game => {
              const formattedGame = {...game}; // 创建游戏数据的副本
              if (formattedGame.game_time) {
                const date = new Date(formattedGame.game_time)
                const month = String(date.getMonth() + 1).padStart(2, '0')
                const day = String(date.getDate()).padStart(2, '0')
                formattedGame.game_time = `${date.getFullYear()}-${month}-${day}`
              }
              formattedGames.push(formattedGame);
            });
          }
        
          // 处理图片路径
          if (player.card_image) {
            player.card_image = util.getOptimizedImagePath(player.card_image)
          }
          if (player.avatar) {
            player.avatar = util.getOptimizedImagePath(player.avatar)
          }
        
          this.setData({
            player: player, 
            playerGames: formattedGames || [],
            loading: false 
          })
          
          resolve()
        })
        .catch(err => {
          console.error('加载玩家详情失败:', err)
          reject(err)
        })
    })
  },
  
  // 计算统计数据
  calculateStats: function(games) {
    if (!games || games.length === 0) {
      return {
        total_kills: 0,
        total_deaths: 0,
        total_wins: 0,
        total_losses: 0,
        total_games: 0,
        kd: '0.00',
        win_rate: '0.00%'
      }
    }

    let totalKills = 0
    let totalDeaths = 0
    let totalWins = 0
    let totalLosses = 0
    let validGames = 0

    games.forEach(game => {
      // 确保只统计暗杀模式的游戏
      if (game.game_type === '暗杀' || game.game_type === '暗杀模式' || (game.game_type && game.game_type.includes('暗杀'))) {
        totalKills += parseInt(game.kills) || 0
        totalDeaths += parseInt(game.deaths) || 0
        totalWins += parseInt(game.wins) || 0
        totalLosses += parseInt(game.losses) || 0
        validGames++
      }
    })

    const totalGames = validGames // 使用有效游戏场次
    const kd = totalDeaths > 0 ? (totalKills / totalDeaths).toFixed(2) : totalKills.toFixed(2)
    const totalMatches = totalWins + totalLosses
    const winRate = totalMatches > 0 ? ((totalWins / totalMatches) * 100).toFixed(2) + '%' : '0.00%'

    return {
      total_kills: totalKills,
      total_deaths: totalDeaths,
      total_wins: totalWins,
      total_losses: totalLosses,
      total_games: totalGames,
      kd: kd,
      win_rate: winRate
    }
  },
  
  // 切换Tab
  switchTab: function (e) {
    var index = e.currentTarget.dataset.index
    this.setData({
      currentTab: index
    })
  },
  
  // 切换数据指标说明显示/隐藏
  toggleTip: function() {
    this.setData({
      showTip: !this.data.showTip
    })
  },
  
  
  
  // 分享
  onShareAppMessage: function () {
    const player = this.data.player;
    const nickname = this.data.nickname;
    let title = `${nickname}的暗杀模式战绩`;
    
    // 如果有KD和胜率数据，添加到分享标题
    if (player) {
      if (player.kd) {
        title += ` | KD ${player.kd}`;
      }
      if (player.win_rate) {
        title += ` | 胜率 ${player.win_rate}`;
      }
    }
    
    return {
      title: title,
      path: '/pages/assassinate/player-detail/player-detail?nickname=' + encodeURIComponent(nickname)
    }
  },
  
  // 处理图片加载错误
  handleImageError: function (e) {
    console.log('图片加载失败', e)
    // 设置一个备用图片
    this.setData({
      'player.card_image': '/images/lxrwimg/default.png'
    })
  },

  // 开始定时检查封禁状态
  startBanStatusCheck: function() {
    if (this.data.banCheckTimer) {
      clearInterval(this.data.banCheckTimer)
    }
    
    const timer = setInterval(() => {
      if (this.data.nickname) {
        api.getPlayerBanStatus(this.data.nickname)
          .then(data => {
            if (this.data.player && (this.data.player.is_banned !== data.is_banned || this.data.player.ban_type !== data.ban_type)) {
              this.setData({
                'player.is_banned': data.is_banned,
                'player.ban_type': data.ban_type
              })
              if (data.is_banned) {
                wx.showToast({
                  title: '该玩家已被封禁',
                  icon: 'none',
                  duration: 2000
                })
              }
            }
          })
          .catch(error => {
            console.error('定时检查封禁状态失败:', error)
          })
      }
    }, 3600000)
    
    this.setData({
      banCheckTimer: timer
    })
  },

  // 页面隐藏时清除定时器
  onHide: function() {
    if (this.data.banCheckTimer) {
      clearInterval(this.data.banCheckTimer)
      this.setData({
        banCheckTimer: null
      })
    }
  },

  // 页面卸载时清除定时器
  onUnload: function() {
    if (this.data.banCheckTimer) {
      clearInterval(this.data.banCheckTimer)
      this.setData({
        banCheckTimer: null
      })
    }
  },

  previewGameImage(e) {
    const { gameId, nickname, index } = e.currentTarget.dataset;
    console.log('点击的游戏ID:', gameId, '类型:', typeof gameId);
    console.log('点击的索引:', index);
    console.log('玩家昵称:', nickname);
    
    // 显示加载提示
    wx.showLoading({
      title: '加载中...',
      mask: true
    });

    try {
      // 首先检查当前数据中是否有对应的游戏ID
      if (this.data.playerGames && this.data.playerGames[index]) {
        const clickedGame = this.data.playerGames[index];
        console.log('从当前数据获取的游戏:', clickedGame);
        
        // 提取游戏ID，可能是game_id或id字段
        const clickedGameId = clickedGame.game_id || clickedGame.id;
        console.log('提取的游戏ID:', clickedGameId);
        
        if (!clickedGameId) {
          wx.hideLoading();
          wx.showToast({
            title: '无法识别游戏ID',
            icon: 'none',
            duration: 2000
          });
          return;
        }
        
        // 使用api模块中的getPlayerGameImages方法获取图片
        api.getPlayerGameImages(nickname)
          .then(data => {
            // 打印返回的数据结构，用于调试
            console.log('游戏图片数据:', data);

            if (data && data.games && data.games.length > 0) {
              // 使用find方法直接查找对应的游戏图片（优化后的查找方式）
              const gameInfo = data.games.find(game => {
                const apiGameId = game.game_id || game.id;
                return String(apiGameId) === String(clickedGameId);
              });

              if (gameInfo && gameInfo.image_url) {
                // 使用自定义弹窗显示图片
                wx.hideLoading();
                this.setData({
                  showImagePreview: true,
                  previewImageUrl: gameInfo.image_url,
                  previewGameInfo: gameInfo // 保存游戏信息，可用于显示额外数据
                });
              } else {
                wx.hideLoading();
                wx.showToast({
                  title: '未找到对应游戏图片',
                  icon: 'none',
                  duration: 2000
                });
              }
            } else {
              wx.hideLoading();
              wx.showToast({
                title: '未找到游戏图片记录',
                icon: 'none',
                duration: 2000
              });
            }
          })
          .catch(err => {
            console.error('获取游戏图片失败:', err);
            wx.hideLoading();
            wx.showToast({
              title: '获取图片失败',
              icon: 'none',
              duration: 2000
            });
          });
      } else {
        wx.hideLoading();
        wx.showToast({
          title: '无法获取游戏数据',
          icon: 'none',
          duration: 2000
        });
      }
    } catch (error) {
      console.error('预览图片发生错误:', error);
      wx.hideLoading();
      wx.showToast({
        title: '操作失败',
        icon: 'none',
        duration: 2000
      });
    }
  },

  // 关闭图片预览
  closeImagePreview() {
    this.setData({
      showImagePreview: false,
      previewImageUrl: '',
      previewGameInfo: null
    });
  },
  
  // 阻止事件冒泡
  stopPropagation(e) {
    // 阻止事件冒泡，防止点击弹窗内容时关闭弹窗
  },
  
  // 全屏预览图片
  previewFullImage() {
    if (this.data.previewImageUrl) {
      wx.previewImage({
        current: this.data.previewImageUrl,
        urls: [this.data.previewImageUrl]
      });
    }
  },
})
