<?php
require_once 'config.php';
require_once 'includes/db.php';
require_once 'includes/utils.php';

// 检查登录状态
Utils::checkLogin();

// 处理修改密码请求
$success = false;
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // 获取表单数据
    $current_password = isset($_POST['current_password']) ? $_POST['current_password'] : '';
    $new_password = isset($_POST['new_password']) ? $_POST['new_password'] : '';
    $confirm_password = isset($_POST['confirm_password']) ? $_POST['confirm_password'] : '';
    
    // 表单验证
    if (empty($current_password)) {
        $error = '请输入当前密码';
    } elseif (empty($new_password)) {
        $error = '请输入新密码';
    } elseif (strlen($new_password) < 6) {
        $error = '新密码长度不能少于6个字符';
    } elseif ($new_password !== $confirm_password) {
        $error = '两次输入的新密码不一致';
    } else {
        // 获取当前用户信息
        $admin_id = $_SESSION['admin_id'];
        $sql = "SELECT * FROM administrators WHERE id = {$admin_id}";
        $admin = $db->getRow($sql);
        
        if (!$admin) {
            $error = '获取用户信息失败';
        } elseif (!password_verify($current_password, $admin['password'])) {
            $error = '当前密码不正确';
        } else {
            // 更新密码
            $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
            $update_data = [
                'password' => $hashed_password
            ];
            
            $result = $db->update('administrators', $update_data, "id = {$admin_id}");
            
            if ($result) {
                // 记录日志
                Utils::logActivity('修改密码', '管理员 ' . $admin['username'] . ' 修改了密码');
                $success = true;
            } else {
                $error = '密码修改失败，请稍后再试';
            }
        }
    }
}

// 包含头部
include 'includes/header.php';
?>


    <div class="container">
<?php
?>

<div class="container">
    <h1 class="page-title">修改密码</h1>
    
    <div class="card">
        <div class="card-title">修改您的登录密码</div>
        
        <?php if ($success): ?>
            <div class="alert alert-success">密码修改成功！</div>
        <?php endif; ?>
        
        <?php if (!empty($error)): ?>
            <div class="alert alert-danger"><?php echo $error; ?></div>
        <?php endif; ?>
        
        <form method="POST" action="change_password.php" class="form">
            <div class="form-group">
                <label for="current_password" class="form-label">当前密码</label>
                <input type="password" id="current_password" name="current_password" class="form-control" required>
            </div>
            
            <div class="form-group">
                <label for="new_password" class="form-label">新密码</label>
                <input type="password" id="new_password" name="new_password" class="form-control" required>
                <small class="form-text text-muted">密码长度不能少于6个字符</small>
            </div>
            
            <div class="form-group">
                <label for="confirm_password" class="form-label">确认新密码</label>
                <input type="password" id="confirm_password" name="confirm_password" class="form-control" required>
            </div>
            
            <div class="form-group">
                <button type="submit" class="btn btn-primary">修改密码</button>
                <a href="index.php" class="btn btn-secondary">返回</a>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 密码强度验证
    const newPasswordInput = document.getElementById('new_password');
    
    newPasswordInput.addEventListener('input', function() {
        const password = this.value;
        let strength = 0;
        
        // 检查长度
        if (password.length >= 8) strength += 1;
        
        // 检查是否包含数字
        if (/\d/.test(password)) strength += 1;
        
        // 检查是否包含小写字母
        if (/[a-z]/.test(password)) strength += 1;
        
        // 检查是否包含大写字母
        if (/[A-Z]/.test(password)) strength += 1;
        
        // 检查是否包含特殊字符
        if (/[^a-zA-Z0-9]/.test(password)) strength += 1;
        
        // 根据强度显示不同的提示
        const strengthText = this.parentNode.querySelector('.form-text');
        if (password.length === 0) {
            strengthText.textContent = '密码长度不能少于6个字符';
            strengthText.className = 'form-text text-muted';
        } else if (password.length < 6) {
            strengthText.textContent = '密码太短';
            strengthText.className = 'form-text text-danger';
        } else if (strength <= 2) {
            strengthText.textContent = '密码强度：弱';
            strengthText.className = 'form-text text-danger';
        } else if (strength <= 3) {
            strengthText.textContent = '密码强度：中';
            strengthText.className = 'form-text text-warning';
        } else {
            strengthText.textContent = '密码强度：强';
            strengthText.className = 'form-text text-success';
        }
    });
    
    // 确认密码验证
    const confirmInput = document.getElementById('confirm_password');
    
    confirmInput.addEventListener('input', function() {
        const newPassword = newPasswordInput.value;
        const confirmPassword = this.value;
        
        if (confirmPassword && newPassword !== confirmPassword) {
            if (!this.nextElementSibling || !this.nextElementSibling.classList.contains('form-text')) {
                const errorText = document.createElement('small');
                errorText.className = 'form-text text-danger';
                errorText.textContent = '两次输入的密码不一致';
                this.parentNode.appendChild(errorText);
            } else {
                this.nextElementSibling.textContent = '两次输入的密码不一致';
                this.nextElementSibling.className = 'form-text text-danger';
            }
        } else if (this.nextElementSibling && this.nextElementSibling.classList.contains('form-text')) {
            this.nextElementSibling.remove();
        }
    });
    
    // 显示/隐藏密码功能
    const passwordFields = document.querySelectorAll('input[type="password"]');
    
    passwordFields.forEach(field => {
        // 创建切换按钮
        const toggleButton = document.createElement('span');
        toggleButton.innerHTML = '<i class="fas fa-eye"></i>';
        toggleButton.className = 'password-toggle';
        toggleButton.style.position = 'absolute';
        toggleButton.style.right = '10px';
        toggleButton.style.top = '50%';
        toggleButton.style.transform = 'translateY(-50%)';
        toggleButton.style.cursor = 'pointer';
        toggleButton.style.color = '#777';
        
        // 将字段容器设为相对定位
        field.parentElement.style.position = 'relative';
        
        // 添加切换按钮到字段后面
        field.parentElement.appendChild(toggleButton);
        
        // 添加点击事件
        toggleButton.addEventListener('click', function() {
            if (field.type === 'password') {
                field.type = 'text';
                this.innerHTML = '<i class="fas fa-eye-slash"></i>';
            } else {
                field.type = 'password';
                this.innerHTML = '<i class="fas fa-eye"></i>';
            }
        });
    });
});
</script>

    </div><!-- 结束 .container -->
</div><!-- 结束 .main-content -->

<?php
// 包含底部
include 'includes/footer.php';
?> 