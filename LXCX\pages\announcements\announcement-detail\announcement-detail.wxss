/* 页面容器 */
.container {
  min-height: 100vh;
  background-image: url('https://img1.lxbl.online/102.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  padding: 30rpx 20rpx;
  position: relative;
}

.container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1;
}

/* 加载状态 */
.loading {
  text-align: center;
  padding: 60rpx 0;
  color: #F5DEB3;
  font-family: "楷体", "STKaiti";
  font-size: 32rpx;
  text-shadow: 2rpx 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

/* 错误提示 */
.error {
  text-align: center;
  padding: 60rpx 0;
  color: #FF4D4F;
  font-family: "楷体", "STKaiti";
  font-size: 32rpx;
  text-shadow: 2rpx 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

/* 公告容器 */
.announcement-wrapper {
  position: relative;
  z-index: 2;
  background: rgba(255, 248, 240, 0.95);
  margin: 20rpx;
  padding: 40rpx;
  border-radius: 8rpx;
  box-shadow: 0 4rpx 20rpx rgba(139, 69, 19, 0.15);
}

/* 添加卷轴装饰 */
.announcement-wrapper::before,
.announcement-wrapper::after {
  content: '';
  position: absolute;
  width: 30rpx;
  height: 100%;
  top: 0;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="15" height="100%" viewBox="0 0 15 100"><path d="M0,0 Q7.5,50 0,100" fill="none" stroke="%238B4513" stroke-width="2"/></svg>');
  background-repeat: repeat-y;
}

.announcement-wrapper::before {
  left: -15rpx;
}

.announcement-wrapper::after {
  right: -15rpx;
  transform: scaleX(-1);
}

/* 公告标题 */
.announcement-title {
  font-family: "楷体", "STKaiti";
  font-size: 40rpx;
  color: #4A321F;
  text-align: center;
  margin-bottom: 30rpx;
  letter-spacing: 4rpx;
  line-height: 1.4;
  position: relative;
  padding-bottom: 20rpx;
}

.announcement-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60%;
  height: 2rpx;
  background: linear-gradient(to right, transparent, #8B4513, transparent);
}

/* 发布信息 */
.announcement-meta {
  display: flex;
  justify-content: center;
  gap: 40rpx;
  margin-bottom: 40rpx;
  font-family: "楷体", "STKaiti";
  color: #8B4513;
  font-size: 28rpx;
  opacity: 0.8;
}

/* 公告内容 */
.announcement-content {
  font-family: "楷体", "STKaiti";
  color: #4A321F;
  font-size: 32rpx;
  line-height: 1.8;
  letter-spacing: 2rpx;
  padding: 20rpx;
  background: rgba(255, 248, 240, 0.7);
  border: 1rpx solid rgba(139, 69, 19, 0.2);
  border-radius: 8rpx;
}

/* 富文本内容样式 */
.announcement-content rich-text {
  display: block;
}

/* 底部安全区域 */
.safe-bottom-area {
  height: 40rpx;
}

/* 公告内图片适配 */
.announcement-content image {
  max-width: 100%;
  height: auto;
  border-radius: 8rpx;
  margin: 20rpx 0;
}

/* Markdown 样式 */
.announcement-content h1 {
  font-size: 38rpx;
  margin: 40rpx 0 20rpx;
  font-weight: bold;
}

.announcement-content h2 {
  font-size: 34rpx;
  margin: 30rpx 0 20rpx;
  font-weight: bold;
}

.announcement-content h3 {
  font-size: 32rpx;
  margin: 24rpx 0 16rpx;
  font-weight: bold;
}

.announcement-content ul, .announcement-content ol {
  padding-left: 40rpx;
  margin: 20rpx 0;
}

.announcement-content blockquote {
  padding: 20rpx;
  margin: 20rpx 0;
  border-left: 6rpx solid #ddd;
  background-color: #f9f9f9;
  color: #666;
}

.announcement-content code.inline-code {
  background-color: #f0f0f0;
  padding: 0 6rpx;
  font-family: monospace;
  border-radius: 4rpx;
}

.announcement-content pre.code-block {
  background-color: #f5f5f5;
  padding: 20rpx;
  margin: 20rpx 0;
  border-radius: 8rpx;
  overflow-x: auto;
}

.announcement-content pre.code-block code {
  font-family: monospace;
  white-space: pre;
}

.announcement-content hr {
  margin: 20rpx 0;
  border: none;
  border-top: 1rpx solid #ddd;
}

/* 短代码样式 */
.announcement-content .shortcode {
  margin: 20rpx 0;
  padding: 20rpx;
  border-radius: 8rpx;
}

.announcement-content .warning-box {
  background-color: rgb(255, 230, 230);
  border-left: 8rpx solid rgb(255, 51, 24);
}

.announcement-content .info-box {
  background-color: #e6f4ff;
  border-left: 8rpx solid #1890ff;
}

.announcement-content .tip-box {
  background-color: #e6fff0;
  border-left: 8rpx solid #52c41a;
}

.announcement-content .download-box {
  background-color: #f5f5f5;
  padding: 20rpx;
  border-radius: 8rpx;
  margin: 20rpx 0;
}

.announcement-content .download-link {
  display: flex;
  align-items: center;
  color: #1890ff;
}

.announcement-content .download-icon {
  margin-right: 10rpx;
  font-size: 36rpx;
}

.announcement-content .download-filename {
  font-weight: bold;
  margin-right: 10rpx;
}

.announcement-content .file-size {
  color: #999;
  font-size: 26rpx;
}

.announcement-content .shortcode-button {
  display: inline-block;
  padding: 12rpx 30rpx;
  background-color: #1890ff;
  color: white;
  border-radius: 6rpx;
  margin: 10rpx 0;
  text-align: center;
}

.announcement-content .shortcode-table {
  width: 100%;
  border-collapse: collapse;
  margin: 20rpx 0;
}

.announcement-content .shortcode-table th,
.announcement-content .shortcode-table td {
  border: 1rpx solid #ddd;
  padding: 16rpx;
  text-align: left;
}

.announcement-content .shortcode-table th {
  background-color: #f5f5f5;
  font-weight: bold;
}

.announcement-content .table-responsive {
  overflow-x: auto;
  margin: 20rpx 0;
}

.announcement-content .markdown-image {
  display: block;
  margin: 20rpx 0;
  max-width: 100%;
}

/* 链接样式 */
.announcement-content .link {
  color: #1890ff;
  text-decoration: underline;
}

/* 下载链接样式优化 */
.announcement-content .download-link {
  display: flex;
  align-items: center;
  color: #1890ff;
  text-decoration: underline;
} 