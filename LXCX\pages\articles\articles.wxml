<!-- 文章页面 -->
<view class="articles-container">
 
  <!-- 分类选项卡 -->
  <scroll-view scroll-x class="category-tabs">
    <!-- 全部分类选项 -->
    <view class="tab-item {{currentCategoryId == 0 ? 'tab-active' : ''}}"
          data-id="0"
          bindtap="switchCategory">
      <text>全部</text>
      <view wx:if="{{currentCategoryId == 0}}" class="active-line"></view>
    </view>
    
    <!-- 其他分类选项 -->
    <view wx:for="{{categories}}" 
          wx:key="id" 
          class="tab-item {{currentCategoryId == item.id ? 'tab-active' : ''}}"
          data-id="{{item.id}}" 
          bindtap="switchCategory">
      <text>{{item.name}}</text>
      <view wx:if="{{currentCategoryId == item.id}}" class="active-line"></view>
    </view>
  </scroll-view>

  <!-- 文章列表 -->
  <view class="articles-list">
    <!-- 骨架屏加载状态 -->
    <block wx:if="{{isLoading && articles.length === 0}}">
      <view class="skeleton-item" wx:for="{{[1,2,3,4,5]}}" wx:key="*this">
        <view class="skeleton-cover"></view>
        <view class="skeleton-content">
          <view class="skeleton-title"></view>
          <view class="skeleton-meta">
            <view class="skeleton-category"></view>
            <view class="skeleton-date"></view>
          </view>
          <view class="skeleton-summary"></view>
          <view class="skeleton-summary short"></view>
        </view>
      </view>
    </block>

    <!-- 文章列表内容 -->
    <block wx:if="{{articles.length > 0}}">
      <view class="article-item"
            wx:for="{{articles}}"
            wx:key="id"
            bindtap="goToDetail"
            data-id="{{item.id}}">
        <!-- 左侧封面图 -->
        <image class="article-cover"
               src="{{item.cover || '/images/default-avatar.png'}}"
               mode="aspectFill"></image>

        <!-- 右侧内容区 -->
        <view class="article-content">
          <!-- 上部标题 -->
          <view class="article-title">{{item.title}}</view>

          <!-- 中部分类和日期 -->
          <view class="article-meta">
            <view class="article-category">{{item.category_name}}</view>
            <view class="article-date">{{item.publish_time}}</view>
          </view>

          <!-- 下部摘要 -->
          <view class="article-summary" wx:if="{{item.summary}}">{{item.summary}}</view>
          <view class="article-summary empty-summary" wx:else>暂无内容摘要</view>
        </view>
      </view>
      
      <!-- 加载更多状态 - 仅当有文章时显示 -->
      <view class="load-more-container">
        <!-- 正在加载更多 -->
        <view wx:if="{{isLoadingMore}}" class="load-more-loading">
          <view class="loading-spinner small"></view>
          <text class="load-more-text">加载更多中...</text>
        </view>

        <!-- 没有更多数据 -->
        <view wx:elif="{{!hasMore}}" class="load-more-end">
          <view class="end-line"></view>
          <text class="end-text">没有更多文章了</text>
          <view class="end-line"></view>
        </view>

        <!-- 网络错误重试 -->
        <view wx:elif="{{loadError}}" class="load-more-error" bindtap="retryLoadMore">
          <text class="error-text">加载失败，点击重试</text>
          <view class="retry-icon">🔄</view>
        </view>
      </view>
    </block>

    <!-- 空状态 -->
    <view wx:if="{{!isLoading && articles.length === 0}}" class="empty-state">
      <view class="empty-icon">📄</view>
      <text class="empty-text">当前分类下暂无文章</text>
      <view class="empty-tip">试试其他分类吧</view>
    </view>
  </view>
</view>
