/* 整体容器 */
.ranking-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 顶部统计信息区域 */
.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #4A90E2;
  color: white;
  padding: 20rpx 30rpx;
  border-radius: 0 0 20rpx 20rpx;
}

.stats-left {
  display: flex;
  flex-direction: column;
}

.stats-title {
  font-size: 28rpx;
  margin-bottom: 10rpx;
}

.stats-counter {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.counter-box {
  width: 60rpx;
  height: 80rpx;
  background-color: white;
  color: #4A90E2;
  font-size: 48rpx;
  font-weight: bold;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 8rpx;
  border-radius: 8rpx;
}

.counter-unit {
  font-size: 32rpx;
  margin-left: 10rpx;
}

.stats-total {
  font-size: 24rpx;
}

.stats-right {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.calendar-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 10rpx;
}

.calendar-icon image {
  width: 100%;
  height: 100%;
}

.rules-btn {
  background-color: rgba(221, 89, 219, 0.5);
  border-radius: 30rpx;
  padding: 8rpx 20rpx;
  font-size: 24rpx;
  margin-bottom:20rpx;
  color: #FFD700;
}

/* 前三名区域 */
.top-three {
  display: flex;
  justify-content: center;
  align-items: flex-end;
  padding: 30rpx 20rpx;
  background-color: white;
  margin-bottom: 20rpx;
}

.top-player {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  padding: 0 20rpx;
}

.first {
  z-index: 3;
  transform: scale(1.1);
}

.second {
  z-index: 2;
  margin-right: 20rpx;
}

.third {
  z-index: 1;
  margin-left: 20rpx;
}

.player-avatar-container {
  position: relative;
  margin-bottom: 10rpx;
}

.player-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 4rpx solid #F0F0F0;
}

.first .player-avatar {
  width: 140rpx;
  height: 140rpx;
  border: 4rpx solid #FFD700;
}

.second .player-avatar {
  border: 4rpx solid #C0C0C0;
}

.third .player-avatar {
  border: 4rpx solid #CD7F32;
}

.crown {
  position: absolute;
  top: -30rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 60rpx;
}

.player-rank {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  background-color: #F0F0F0;
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
  margin-bottom: 8rpx;
}

.first .player-rank {
  background-color: #FFD700;
  color: white;
}

.second .player-rank {
  background-color: #C0C0C0;
  color: white;
}

.third .player-rank {
  background-color: #CD7F32;
  color: white;
}
/* 调试前三名玩家昵称的显示效果 */
.player-name {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 4rpx;
  max-width: 200rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.player-hall {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
  max-width: 150rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.player-score {
  display: flex;
  align-items: center;
  background-color: #F0F0F0;
  border-radius: 20rpx;
  padding: 4rpx 16rpx;
}

.score-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 6rpx;
}

/* 排行榜列表 */
.ranking-list {
  background-color: white;
  padding: 0 30rpx;
}

.ranking-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #F0F0F0;
}

.rank-number {
  width: 120rpx;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.player-info {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 16rpx;
}

.ranking-item .player-name {
  font-size: 28rpx;
  font-weight: bold;
  max-width: 200rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
/* VIP标签古风样式 */
.ranking-item .player-hall {
  background: linear-gradient(45deg,rgba(218, 165, 32, 0.5),rgba(255, 217, 0, 0.5));
  color:rgba(139, 69, 19, 0.5);
  border: 1rpx solidrgba(139, 69, 19, 0.5);
  padding: 4rpx 16rpx;
  font-family: "楷体", "STKaiti";
  text-shadow: 0 0 2rpx rgba(139, 69, 19, 0.3);
  border-radius: 35rpx; /* 圆角卷轴感 */
}
/* VIP标签古风样式 */
.top-three .player-hall {
  background: linear-gradient(45deg,rgb(218, 165, 32),rgb(255, 217, 0));
  color:rgba(139, 69, 19, 0.5);
  border: 1rpx solidrgba(139, 69, 19, 0.5);
  padding: 4rpx 16rpx;
  font-family: "楷体", "STKaiti";
  text-shadow: 0 0 2rpx rgba(139, 69, 19, 0.3);
  border-radius: 35rpx; /* 圆角卷轴感 */
}




.ranking-item .player-summary {
  font-size: 24rpx;
  color: #4A90E2;
  margin-left: auto;
}

.ranking-item .player-score {
  background-color:rgb(240, 240, 240);
  border-radius: 20rpx;
  padding: 4rpx 16rpx;
  font-size: 28rpx;
}

/* 游戏类型选择器样式 */
.game-type-selector {
  margin-bottom: 10rpx;
  position: relative;
}

.custom-select {
  display: flex;
  align-items: center;
  background: rgb(249, 161, 161);
  padding: 8rpx 35rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  color: #fff;
  cursor: pointer;
}

.selected-value {
  margin-right: 8rpx;
}

.select-arrow {
  font-size: 20rpx;
  transition: transform 0.3s ease;
}

.select-arrow.open {
  transform: rotate(180deg);
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: #fff;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  margin-top: 8rpx;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10rpx);
  transition: all 0.3s ease;
  z-index: 100;
}

.dropdown-menu.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-item {
  padding: 16rpx 20rpx;
  font-size: 24rpx;
  color: #333;
  transition: background-color 0.3s ease;
}

.dropdown-item.active {
  background: rgba(74, 144, 226, 0.1);
  color: #4A90E2;
}

.dropdown-item:hover {
  background: rgba(0, 0, 0, 0.05);
} 