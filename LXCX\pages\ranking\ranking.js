Page({
  /**
   * 页面的初始数据
   */
  data: {
    dayCount: ['0', '0', '0', '0' ], // 签到天数，个位、十位、百位、千位
    total_kills: 0, // 累计狙杀数量
    startDate: '2025-05-01', // 开始计算天数的日期
    rankList: [],
    gameTypes: ['暗杀', '死斗', '盟主'], // 添加游戏类型数组
    currentGameType: '暗杀', // 当前选中的游戏类型
    isDropdownOpen: false,
    ruleTitle: '', // 规则标题
    ruleContent: '', // 规则内容
    hasShownStartupRules: false, // 是否已显示启动时的规则弹窗
    ruleDataLoaded: false // 规则数据是否已加载完成
  },

  /**
   * 切换游戏类型
   */
  switchGameType: function(e) {
    const gameType = this.data.gameTypes[e.detail.value];
    this.setData({
      currentGameType: gameType
    });
    this.fetchRankingData();
  },

  /**
   * 显示活跃度规则
   */
  showRules: function() {
    wx.showModal({
      title: this.data.ruleTitle || '规则加载中...',
      content: this.data.ruleContent || '正在获取最新规则...',
      showCancel: false,
      confirmText: '我知道了'
    });
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.fetchRankingData();
    this.fetchLatestRule(); // 获取最新规则
  },

  /**
   * 获取排行榜数据
   */
  fetchRankingData: function() {
    // 计算从开始日期到现在的天数
    const days = this.calculateDaysSinceStart();
    
    // 格式化天数显示
    this.setData({
      dayCount: this.formatDayCount(days)
    });
    
    // 获取游戏类型汇总统计数据
    this.fetchGameTypeSummary();
    
    // 获取玩家排行榜
    this.fetchGameRanking();
  },
  
  /**
   * 获取游戏类型汇总统计数据
   */
  fetchGameTypeSummary: function() {
    const app = getApp();
    const gameType = this.data.currentGameType;
    
    // 构建API请求URL
    const url = `${app.globalData.apiBaseUrl}?path=game_type_summary&type=${gameType}&api_key=${app.globalData.apiKey}`;
    
    console.log('请求游戏类型汇总URL:', url);
    
    wx.request({
      url: url,
      method: 'GET',
      success: (res) => {
        console.log('获取游戏类型汇总响应:', res.data);
        
        if (res.statusCode === 200 && res.data.status === 'success') {
          const data = res.data.data;
          let totalKills = 0;
          
          // 从game_types数组中查找当前游戏类型
          if (data && data.game_types && Array.isArray(data.game_types)) {
            const gameTypeData = data.game_types.find(item => item.game_type === gameType);
            if (gameTypeData && gameTypeData.total_kills) {
              totalKills = parseInt(gameTypeData.total_kills) || 0;
              console.log(`从game_types中找到的${gameType}数量:`, totalKills);
            }
          }
          
          // 如果在game_types中没找到，尝试从summary中获取
          if (totalKills === 0 && data && data.summary && data.summary.total_records) {
            totalKills = parseInt(data.summary.total_records) || 0;
            console.log('从summary中找到的总击杀数量:', totalKills);
          }
          
          // 更新击杀数量
          this.setData({
            total_records: totalKills
          });
        } else {
          console.error('获取游戏类型汇总数据失败:', res);
        }
      },
      fail: (err) => {
        console.error('请求游戏类型汇总接口失败:', err);
      }
    });
  },

  /**
   * 计算从开始日期到现在的天数
   */
  calculateDaysSinceStart: function() {
    const startDate = new Date(this.data.startDate);
    const today = new Date();
    
    // 确保日期有效
    if (isNaN(startDate.getTime())) {
      console.error('无效的开始日期');
      return 0;
    }
    
    // 计算天数差异
    const diffTime = today.getTime() - startDate.getTime();
    const diffDays = Math.max(0, Math.floor(diffTime / (1000 * 60 * 60 * 24)));
    
    return diffDays;
  },

  /**
   * 格式化天数为数组
   */
  formatDayCount: function(days) {
    return days.toString().padStart(4, '0').split('');
  },

  /**
   * 获取游戏排行榜
   */
  fetchGameRanking: function() {
    const app = getApp();
    const gameType = this.data.currentGameType;
    
    // 构建API请求URL
    const url = `${app.globalData.apiBaseUrl}?path=game_type_status&type=${gameType}&api_key=${app.globalData.apiKey}`;
    
    console.log('请求排行榜URL:', url);
    
    wx.request({
      url: url,
      method: 'GET',
      success: (res) => {
        console.log('获取排行榜响应:', res.data);
        
        if (res.statusCode === 200 && res.data.status === 'success') {
          let playerList = [];
          
          // 处理返回的玩家数据
          if (res.data.data && Array.isArray(res.data.data.players)) {
            playerList = res.data.data.players.map(player => ({
              nickname: player.nickname || '未知玩家',
              hall: player.player_rank || '未知段位',
              total_kills: parseInt(player.total_kills || 0),
              snipeCount: parseInt(player.total_kills || 0),
              avatarUrl: player.avatar_url || '/images/default-avatar.png'
            }));
            
            // 按击杀数量降序排序
            playerList.sort((a, b) => b.total_kills - a.total_kills);
            
            // 只取前15名
            playerList = playerList.slice(0, 15);
            
            console.log('处理后的排行榜数据:', playerList);
          }
          
          // 更新排行榜数据
          this.setData({
            rankList: playerList
          });
        } else {
          console.error('获取排行榜数据失败:', res);
        }
      },
      fail: (err) => {
        console.error('请求排行榜接口失败:', err);
      }
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
    // 页面渲染完成后，检查是否需要显示启动规则弹窗
    this.checkAndShowStartupRules();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    // 页面显示时也检查是否需要显示启动规则弹窗
    // 这样可以处理用户通过 tab 切换等方式进入页面的情况
    this.checkAndShowStartupRules();
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    this.fetchRankingData();
    wx.stopPullDownRefresh();
  },

  /**
   * 切换下拉框显示状态
   */
  toggleDropdown: function() {
    this.setData({
      isDropdownOpen: !this.data.isDropdownOpen
    });
  },

  /**
   * 选择游戏类型
   */
  selectGameType: function(e) {
    const gameType = e.currentTarget.dataset.type;
    this.setData({
      currentGameType: gameType,
      isDropdownOpen: false
    });
    this.fetchRankingData();
  },

 

  /**
   * 获取最新规则
   */
  fetchLatestRule: function() {
    const app = getApp();
    // 与其他API请求保持一致的URL构建方式
    const url = `${app.globalData.apiBaseUrl}?path=latest_rule&api_key=${app.globalData.apiKey}`;

    console.log('请求规则URL:', url);

    wx.request({
      url: url,
      method: 'GET',
      success: (res) => {
        console.log('获取规则响应:', res.data);
        if (res.statusCode === 200 && res.data.status === 'success') {
          // 处理可能的转义字符
          const content = res.data.data.content ? res.data.data.content.trim() : '暂无规则内容';

          this.setData({
            ruleTitle: res.data.data.title || '规则通知',
            ruleContent: content,
            ruleDataLoaded: true // 标记规则数据已加载完成
          });

          // 如果页面已经准备好且还没有显示过启动弹窗，则自动显示
          this.checkAndShowStartupRules();
        } else {
          console.error('获取规则数据失败:', res);
          this.setData({
            ruleTitle: '规则获取失败',
            ruleContent: '暂时无法获取最新规则，请稍后再试',
            ruleDataLoaded: true // 即使失败也标记为已加载，避免无限等待
          });

          // 即使获取失败也尝试显示弹窗
          this.checkAndShowStartupRules();
        }
      },
      fail: (err) => {
        console.error('请求规则接口失败:', err);
        this.setData({
          ruleTitle: '规则获取失败',
          ruleContent: '暂时无法获取最新规则，请稍后再试',
          ruleDataLoaded: true // 即使失败也标记为已加载，避免无限等待
        });

        // 即使获取失败也尝试显示弹窗
        this.checkAndShowStartupRules();
      }
    });
  },

  /**
   * 检查并显示启动时的规则弹窗
   */
  checkAndShowStartupRules: function() {
    // 只有在规则数据已加载且还没有显示过启动弹窗时才显示
    if (this.data.ruleDataLoaded && !this.data.hasShownStartupRules) {
      this.setData({
        hasShownStartupRules: true
      });

      // 延迟一小段时间确保页面渲染完成
      setTimeout(() => {
        this.showRules();
      }, 500);
    }
  },
  // 分享功能
  onShareAppMessage: function () {
    return {
      title: `${this.data.currentGameType}排行榜`,
      path: `/pages/ranking/ranking?type=${encodeURIComponent(this.data.currentGameType)}`
    }
  },

  // 分享到朋友圈
  onShareTimeline: function () {
    return {
      title: `${this.data.currentGameType}排行榜`,
      path: `/pages/ranking/ranking?type=${encodeURIComponent(this.data.currentGameType)}`,
      query: '',
      success: function(res) {
        wx.showToast({
          title: '分享成功',
          icon: 'success',
          duration: 2000
        });
      },
      fail: function(res) {
        wx.showToast({
          title: '分享失败',
          icon: 'none',
          duration: 2000
        });
      }
    }
  },
}); 