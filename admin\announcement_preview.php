<?php
require_once 'config.php';
require_once 'includes/utils.php';
require_once 'includes/db.php';
require_once 'includes/shortcodes.php';
require_once 'includes/markdown.php';
require_once 'includes/formatting.php';

// 检查用户是否已登录且为管理员
Utils::checkLogin();

// 检查是否有ID参数
if (!isset($_GET['id']) || empty($_GET['id'])) {
    $_SESSION['error_message'] = "未提供公告ID";
    header("Location: announcement_list.php");
    exit;
}

$id = intval($_GET['id']);

// 获取公告详情
$sql = "SELECT * FROM announcements WHERE id = " . $db->escape($id);
$announcement = $db->getRow($sql);

if (!$announcement) {
    $_SESSION['error_message'] = "找不到该公告";
    header("Location: announcement_list.php");
    exit;
}

// 获取状态标签
function getStatusBadge($status) {
    if ($status == 1) {
        return '<span class="badge bg-success">已发布</span>';
    } else {
        return '<span class="badge bg-secondary">草稿</span>';
    }
}

// 获取优先级标签
function getPriorityBadge($priority) {
    if ($priority == 1) {
        return '<span class="badge bg-danger">高优先级</span>';
    } else {
        return '<span class="badge bg-info">普通优先级</span>';
    }
}

// 获取当前有效性状态
function getValidityBadge($validFrom, $validTo) {
    $now = time();
    $fromTime = strtotime($validFrom);
    $toTime = strtotime($validTo);
    
    if ($now < $fromTime) {
        return '<span class="badge bg-warning">未开始</span>';
    } elseif ($now > $toTime) {
        return '<span class="badge bg-danger">已过期</span>';
    } else {
        return '<span class="badge bg-success">有效中</span>';
    }
}

$page_title = "预览公告";
include 'includes/header.php';
?>


    <div class="container">
<?php
?>

<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">预览公告</h1>
        <div>
            <a href="announcement_edit.php?id=<?php echo $announcement['id']; ?>" class="btn btn-primary me-2">
                <i class="fas fa-edit"></i> 编辑
            </a>
            <a href="announcement_list.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> 返回列表
            </a>
        </div>
    </div>
    
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h2 class="h4 mb-0"><?php echo htmlspecialchars($announcement['title']); ?></h2>
        </div>
        <div class="card-body">
            <div class="row mb-4">
                <div class="col-md-6">
                    <p class="mb-1"><strong>状态:</strong> <?php echo getStatusBadge($announcement['status']); ?></p>
                    <p class="mb-1"><strong>优先级:</strong> <?php echo getPriorityBadge($announcement['priority']); ?></p>
                    <p class="mb-0"><strong>当前状态:</strong> <?php echo getValidityBadge($announcement['valid_from'], $announcement['valid_to']); ?></p>
                </div>
                <div class="col-md-6">
                    <p class="mb-1"><strong>有效期:</strong> <?php echo date('Y-m-d', strtotime($announcement['valid_from'])); ?> 至 <?php echo date('Y-m-d', strtotime($announcement['valid_to'])); ?></p>
                    <p class="mb-1"><strong>创建时间:</strong> <?php echo date('Y-m-d H:i:s', strtotime($announcement['created_at'])); ?></p>
                    <p class="mb-0"><strong>更新时间:</strong> <?php echo date('Y-m-d H:i:s', strtotime($announcement['updated_at'])); ?></p>
                </div>
            </div>
            
            <hr class="my-4">
            
            <div class="card">
                <div class="card-header bg-light">
                    <h3 class="h5 mb-0">公告内容</h3>
                </div>
                <div class="card-body">
                    <?php echo render_markdown($announcement['content'], 'markdown-preview'); ?>
                </div>
            </div>
            
            <hr class="my-4">
            
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-light">
                            <h3 class="h5 mb-0">原始Markdown</h3>
                        </div>
                        <div class="card-body">
                            <pre class="border p-3 bg-light"><code><?php echo htmlspecialchars($announcement['content']); ?></code></pre>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-light">
                            <h3 class="h5 mb-0">经过短代码处理的内容</h3>
                        </div>
                        <div class="card-body">
                            <pre class="border p-3 bg-light"><code><?php echo htmlspecialchars(do_shortcode($announcement['content'])); ?></code></pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
include 'includes/footer.php';
?> 